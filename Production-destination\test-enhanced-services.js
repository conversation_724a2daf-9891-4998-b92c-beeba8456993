/**
 * Test Script for Enhanced Azure Services
 * This script tests Event Grid, Redis, and Service Bus connectivity
 */

const { EventGridPublisherClient, AzureKeyCredential } = require('@azure/eventgrid');
const { ServiceBusClient } = require('@azure/service-bus');
const redis = require('redis');

// Configuration from environment variables
const config = {
  eventGrid: {
    endpoint: process.env.EVENT_GRID_TOPIC_ENDPOINT,
    key: process.env.EVENT_GRID_TOPIC_KEY
  },
  serviceBus: {
    connectionString: process.env.SERVICE_BUS_CONNECTION_STRING
  },
  redis: {
    connectionString: process.env.REDIS_CONNECTION_STRING
  }
};

console.log('🧪 Testing Enhanced Azure Services...');
console.log('=====================================');

async function testEventGrid() {
  console.log('\n⚡ Testing Event Grid...');
  
  try {
    if (!config.eventGrid.endpoint || !config.eventGrid.key) {
      throw new Error('Event Grid configuration missing');
    }

    const client = new EventGridPublisherClient(
      config.eventGrid.endpoint,
      new AzureKeyCredential(config.eventGrid.key)
    );

    const testEvent = {
      id: `test-${Date.now()}`,
      source: 'test-script',
      type: 'test.event',
      time: new Date(),
      data: {
        message: 'Test event from enhanced services test script',
        timestamp: new Date().toISOString()
      }
    };

    await client.send([testEvent]);
    console.log('✅ Event Grid: Successfully published test event');
    return true;
  } catch (error) {
    console.log(`❌ Event Grid: ${error.message}`);
    return false;
  }
}

async function testServiceBus() {
  console.log('\n📨 Testing Service Bus...');
  
  try {
    if (!config.serviceBus.connectionString) {
      throw new Error('Service Bus configuration missing');
    }

    const client = new ServiceBusClient(config.serviceBus.connectionString);
    
    // Test analytics-events queue
    const sender = client.createSender('analytics-events');
    
    const testMessage = {
      body: {
        eventType: 'test_event',
        source: 'test-script',
        data: {
          message: 'Test message from enhanced services test script',
          timestamp: new Date().toISOString()
        }
      },
      messageId: `test-${Date.now()}`,
      subject: 'test.message'
    };

    await sender.sendMessages(testMessage);
    console.log('✅ Service Bus: Successfully sent test message to analytics-events queue');
    
    await sender.close();
    await client.close();
    return true;
  } catch (error) {
    console.log(`❌ Service Bus: ${error.message}`);
    return false;
  }
}

async function testRedis() {
  console.log('\n🔴 Testing Redis...');
  
  try {
    if (!config.redis.connectionString) {
      throw new Error('Redis configuration missing');
    }

    const client = redis.createClient({
      url: `redis://${config.redis.connectionString}`
    });

    await client.connect();
    
    // Test set and get
    const testKey = `test:${Date.now()}`;
    const testValue = {
      message: 'Test data from enhanced services test script',
      timestamp: new Date().toISOString()
    };

    await client.setEx(testKey, 60, JSON.stringify(testValue)); // 60 seconds TTL
    const retrievedValue = await client.get(testKey);
    
    if (retrievedValue) {
      const parsed = JSON.parse(retrievedValue);
      console.log('✅ Redis: Successfully stored and retrieved test data');
      console.log(`   Stored: ${testValue.message}`);
      console.log(`   Retrieved: ${parsed.message}`);
    } else {
      throw new Error('Failed to retrieve test data');
    }
    
    // Clean up
    await client.del(testKey);
    await client.disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Redis: ${error.message}`);
    return false;
  }
}

async function testCachePatterns() {
  console.log('\n🔄 Testing Cache Patterns...');
  
  try {
    const client = redis.createClient({
      url: `redis://${config.redis.connectionString}`
    });

    await client.connect();
    
    // Test user profile cache pattern
    const userId = 'test-user-123';
    const userProfile = {
      id: userId,
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      displayName: 'Test User',
      cachedAt: new Date().toISOString()
    };

    const userKey = `user:${userId}:profile`;
    await client.setEx(userKey, 3600, JSON.stringify(userProfile)); // 1 hour TTL
    
    const cachedProfile = await client.get(userKey);
    if (cachedProfile) {
      console.log('✅ Cache Pattern: User profile caching works');
    }
    
    // Test project cache pattern
    const projectId = 'test-project-456';
    const projectData = {
      id: projectId,
      name: 'Test Project',
      documentCount: 5,
      memberCount: 3,
      storageUsed: 1024000,
      cachedAt: new Date().toISOString()
    };

    const projectKey = `project:${projectId}:details`;
    await client.setEx(projectKey, 1800, JSON.stringify(projectData)); // 30 minutes TTL
    
    const cachedProject = await client.get(projectKey);
    if (cachedProject) {
      console.log('✅ Cache Pattern: Project details caching works');
    }
    
    // Clean up
    await client.del(userKey);
    await client.del(projectKey);
    await client.disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Cache Patterns: ${error.message}`);
    return false;
  }
}

async function testServiceBusTopics() {
  console.log('\n📢 Testing Service Bus Topics...');
  
  try {
    const client = new ServiceBusClient(config.serviceBus.connectionString);
    
    // Test user-events topic
    const sender = client.createSender('user-events');
    
    const testMessage = {
      body: {
        eventType: 'user_test',
        userId: 'test-user-123',
        action: 'profile_updated',
        data: {
          updatedFields: ['firstName', 'lastName'],
          timestamp: new Date().toISOString()
        }
      },
      messageId: `test-topic-${Date.now()}`,
      subject: 'user.test'
    };

    await sender.sendMessages(testMessage);
    console.log('✅ Service Bus Topics: Successfully sent test message to user-events topic');
    
    await sender.close();
    await client.close();
    return true;
  } catch (error) {
    console.log(`❌ Service Bus Topics: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('Starting comprehensive test suite...\n');
  
  const results = {
    eventGrid: await testEventGrid(),
    serviceBus: await testServiceBus(),
    redis: await testRedis(),
    cachePatterns: await testCachePatterns(),
    serviceBusTopics: await testServiceBusTopics()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.padEnd(20)}: ${status}`);
  });
  
  console.log(`\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! Enhanced services are working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check configuration and Azure resources.');
  }
  
  return passed === total;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testEventGrid,
  testServiceBus,
  testRedis,
  testCachePatterns,
  testServiceBusTopics,
  runAllTests
};
