/**
 * Document Approval Function
 * Handles document approval workflows, review processes, and approval tracking
 * Migrated from old-arch/src/document-service/approval/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';
import { publishEvent, EventType } from './event-grid-handlers';
import { serviceBusEnhanced } from '../shared/services/service-bus';
import { redis } from '../shared/services/redis';
import { signalREnhanced } from '../shared/services/signalr';

// Approval types and enums
enum ApprovalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED'
}

enum ApprovalType {
  SINGLE = 'SINGLE',
  SEQUENTIAL = 'SEQUENTIAL',
  PARALLEL = 'PARALLEL',
  MAJORITY = 'MAJORITY',
  UNANIMOUS = 'UNANIMOUS'
}

enum ReviewerStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ABSTAINED = 'ABSTAINED'
}

// Validation schemas
const createApprovalSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  approvalType: Joi.string().valid(...Object.values(ApprovalType)).default(ApprovalType.SINGLE),
  reviewers: Joi.array().items(Joi.object({
    userId: Joi.string().uuid().required(),
    order: Joi.number().min(1).optional(),
    required: Joi.boolean().default(true),
    role: Joi.string().optional()
  })).min(1).required(),
  dueDate: Joi.string().isoDate().optional(),
  message: Joi.string().max(1000).optional(),
  requireComments: Joi.boolean().default(false),
  allowDelegation: Joi.boolean().default(false),
  autoApproveAfter: Joi.number().min(1).max(30).optional(), // Days
  metadata: Joi.object().optional()
});

const reviewDocumentSchema = Joi.object({
  approvalId: Joi.string().uuid().required(),
  decision: Joi.string().valid('APPROVED', 'REJECTED', 'ABSTAINED').required(),
  comments: Joi.string().max(2000).optional(),
  attachments: Joi.array().items(Joi.object({
    fileName: Joi.string().required(),
    fileUrl: Joi.string().uri().required(),
    fileSize: Joi.number().optional()
  })).optional(),
  delegateTo: Joi.string().uuid().optional()
});

interface CreateApprovalRequest {
  documentId: string;
  approvalType: ApprovalType;
  reviewers: Array<{
    userId: string;
    order?: number;
    required: boolean;
    role?: string;
  }>;
  dueDate?: string;
  message?: string;
  requireComments: boolean;
  allowDelegation: boolean;
  autoApproveAfter?: number;
  metadata?: any;
}

interface ReviewDocumentRequest {
  approvalId: string;
  decision: 'APPROVED' | 'REJECTED' | 'ABSTAINED';
  comments?: string;
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileSize?: number;
  }>;
  delegateTo?: string;
}

/**
 * Create document approval handler
 */
export async function createDocumentApproval(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create document approval started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createApprovalSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const approvalRequest: CreateApprovalRequest = value;

    // Verify document exists and user has permission
    const document = await db.readItem('documents', approvalRequest.documentId, approvalRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check if user can create approval for this document
    const canCreateApproval = await checkApprovalPermission(documentData, user);
    if (!canCreateApproval) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to create approval for this document" }
      }, request);
    }

    // Check if document already has pending approval
    const existingApprovalQuery = 'SELECT * FROM c WHERE c.documentId = @docId AND c.status = @status';
    const existingApprovals = await db.queryItems('document-approvals', existingApprovalQuery, [approvalRequest.documentId, ApprovalStatus.PENDING]);
    
    if (existingApprovals.length > 0) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document already has a pending approval" }
      }, request);
    }

    // Validate reviewers exist
    const reviewerIds = approvalRequest.reviewers.map(r => r.userId);
    const reviewerValidation = await validateReviewers(reviewerIds);
    if (!reviewerValidation.valid) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: `Invalid reviewers: ${reviewerValidation.invalidIds.join(', ')}` }
      }, request);
    }

    // Create approval record
    const approvalId = uuidv4();
    const now = new Date().toISOString();

    // Prepare reviewers with status
    const reviewersWithStatus = approvalRequest.reviewers.map((reviewer, index) => ({
      ...reviewer,
      id: uuidv4(),
      status: ReviewerStatus.PENDING,
      order: reviewer.order || index + 1,
      assignedAt: now,
      reviewedAt: null,
      decision: null,
      comments: null,
      attachments: []
    }));

    const approval = {
      id: approvalId,
      documentId: approvalRequest.documentId,
      documentName: documentData.name,
      approvalType: approvalRequest.approvalType,
      status: ApprovalStatus.PENDING,
      reviewers: reviewersWithStatus,
      dueDate: approvalRequest.dueDate,
      message: approvalRequest.message,
      requireComments: approvalRequest.requireComments,
      allowDelegation: approvalRequest.allowDelegation,
      autoApproveAfter: approvalRequest.autoApproveAfter,
      metadata: approvalRequest.metadata,
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      completedAt: null,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      tenantId: user.tenantId
    };

    await db.createItem('document-approvals', approval);

    // Update document status
    const updatedDocument = {
      ...documentData,
      status: 'PENDING_APPROVAL',
      approvalId,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('documents', updatedDocument);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_approval_created",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: approvalRequest.documentId,
      timestamp: now,
      details: {
        approvalId,
        documentName: documentData.name,
        approvalType: approvalRequest.approvalType,
        reviewerCount: reviewersWithStatus.length,
        dueDate: approvalRequest.dueDate
      },
      tenantId: user.tenantId
    });

    // Cache approval data in Redis for quick access
    try {
      await redis.setWithExpiry(
        `approval:${approvalId}`,
        JSON.stringify({
          id: approvalId,
          documentId: approvalRequest.documentId,
          documentName: documentData.name,
          status: ApprovalStatus.PENDING,
          approvalType: approvalRequest.approvalType,
          reviewerCount: reviewersWithStatus.length,
          createdBy: user.id,
          organizationId: documentData.organizationId,
          projectId: documentData.projectId
        }),
        7200 // 2 hours cache
      );
    } catch (redisError) {
      logger.warn("Failed to cache approval data", { redisError });
    }

    // Publish Event Grid event for approval creation
    try {
      await publishEvent(
        EventType.WORKFLOW_STARTED,
        `approvals/${approvalId}/created`,
        {
          approvalId,
          documentId: approvalRequest.documentId,
          documentName: documentData.name,
          approvalType: approvalRequest.approvalType,
          reviewerCount: reviewersWithStatus.length,
          createdBy: user.id,
          organizationId: documentData.organizationId,
          projectId: documentData.projectId,
          dueDate: approvalRequest.dueDate,
          timestamp: now
        }
      );
    } catch (eventError) {
      logger.warn("Failed to publish approval created event", { eventError });
    }

    // Send approval workflow to Service Bus for processing
    try {
      await serviceBusEnhanced.sendToQueue('workflow-orchestration', {
        body: {
          type: 'approval_workflow_started',
          approvalId,
          documentId: approvalRequest.documentId,
          approvalType: approvalRequest.approvalType,
          reviewers: reviewersWithStatus.map(r => ({
            userId: r.userId,
            order: r.order,
            required: r.required
          })),
          organizationId: documentData.organizationId,
          projectId: documentData.projectId,
          createdBy: user.id,
          timestamp: now
        },
        messageId: `approval-workflow-${approvalId}-${Date.now()}`,
        correlationId,
        applicationProperties: {
          approvalId,
          documentId: approvalRequest.documentId,
          organizationId: documentData.organizationId,
          source: 'document-approval'
        }
      });
    } catch (serviceBusError) {
      logger.warn("Failed to send approval workflow to Service Bus", { serviceBusError });
    }

    // Send real-time notifications via SignalR
    try {
      for (const reviewer of reviewersWithStatus) {
        await signalREnhanced.sendToUser(reviewer.userId, 'approvalAssigned', {
          approvalId,
          documentId: approvalRequest.documentId,
          documentName: documentData.name,
          approvalType: approvalRequest.approvalType,
          dueDate: approvalRequest.dueDate,
          message: approvalRequest.message,
          assignedBy: user.id,
          timestamp: now
        });
      }
    } catch (signalRError) {
      logger.warn("Failed to send real-time notifications", { signalRError });
    }

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentApprovalCreated',
      aggregateId: approvalId,
      aggregateType: 'DocumentApproval',
      version: 1,
      data: {
        approval,
        document: documentData,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    // Send notifications to reviewers
    await notifyReviewers(approval, documentData, user, 'ASSIGNED');

    logger.info("Document approval created successfully", {
      correlationId,
      approvalId,
      documentId: approvalRequest.documentId,
      documentName: documentData.name,
      approvalType: approvalRequest.approvalType,
      reviewerCount: reviewersWithStatus.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: approvalId,
        documentId: approvalRequest.documentId,
        documentName: documentData.name,
        approvalType: approvalRequest.approvalType,
        status: ApprovalStatus.PENDING,
        reviewers: reviewersWithStatus.map(r => ({
          id: r.id,
          userId: r.userId,
          order: r.order,
          required: r.required,
          status: r.status,
          assignedAt: r.assignedAt
        })),
        dueDate: approvalRequest.dueDate,
        createdBy: user.id,
        createdAt: now,
        message: "Document approval created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create document approval failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Review document handler
 */
export async function reviewDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Review document started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = reviewDocumentSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const reviewRequest: ReviewDocumentRequest = value;

    // Get approval record
    const approval = await db.readItem('document-approvals', reviewRequest.approvalId, reviewRequest.approvalId);
    if (!approval) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Approval not found" }
      }, request);
    }

    const approvalData = approval as any;

    // Check if approval is still pending
    if (approvalData.status !== ApprovalStatus.PENDING) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Approval is no longer pending" }
      }, request);
    }

    // Find reviewer in approval
    const reviewerIndex = approvalData.reviewers.findIndex((r: any) => r.userId === user.id && r.status === ReviewerStatus.PENDING);
    if (reviewerIndex === -1) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "You are not authorized to review this document or have already reviewed it" }
      }, request);
    }

    // Check if comments are required
    if (approvalData.requireComments && !reviewRequest.comments) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Comments are required for this approval" }
      }, request);
    }

    const now = new Date().toISOString();

    // Update reviewer status
    const updatedReviewers = [...approvalData.reviewers];
    updatedReviewers[reviewerIndex] = {
      ...updatedReviewers[reviewerIndex],
      status: reviewRequest.decision as ReviewerStatus,
      decision: reviewRequest.decision,
      comments: reviewRequest.comments,
      attachments: reviewRequest.attachments || [],
      reviewedAt: now,
      reviewedBy: user.id
    };

    // Check if approval is complete
    const approvalResult = calculateApprovalResult(approvalData.approvalType, updatedReviewers);

    // Update approval record
    const updatedApproval = {
      ...approvalData,
      reviewers: updatedReviewers,
      status: approvalResult.status,
      completedAt: approvalResult.isComplete ? now : null,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('document-approvals', updatedApproval);

    // Update document status if approval is complete
    if (approvalResult.isComplete) {
      const document = await db.readItem('documents', approvalData.documentId, approvalData.documentId);
      if (document) {
        const documentData = document as any;
        const updatedDocument = {
          ...documentData,
          status: approvalResult.status === ApprovalStatus.APPROVED ? 'APPROVED' : 'REJECTED',
          approvalCompletedAt: now,
          updatedAt: now,
          updatedBy: user.id
        };

        await db.updateItem('documents', updatedDocument);
      }
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_reviewed",
      userId: user.id,
      organizationId: approvalData.organizationId,
      projectId: approvalData.projectId,
      documentId: approvalData.documentId,
      timestamp: now,
      details: {
        approvalId: reviewRequest.approvalId,
        decision: reviewRequest.decision,
        documentName: approvalData.documentName,
        hasComments: !!reviewRequest.comments,
        isComplete: approvalResult.isComplete,
        finalStatus: approvalResult.status
      },
      tenantId: user.tenantId
    });

    // Update Redis cache with review status
    try {
      await redis.setWithExpiry(
        `approval:${reviewRequest.approvalId}`,
        JSON.stringify({
          id: reviewRequest.approvalId,
          documentId: approvalData.documentId,
          documentName: approvalData.documentName,
          status: approvalResult.status,
          approvalType: approvalData.approvalType,
          reviewerCount: updatedReviewers.length,
          completedReviews: updatedReviewers.filter(r => r.status !== ReviewerStatus.PENDING).length,
          isComplete: approvalResult.isComplete,
          lastReviewedBy: user.id,
          lastReviewedAt: now,
          organizationId: approvalData.organizationId,
          projectId: approvalData.projectId
        }),
        7200 // 2 hours cache
      );
    } catch (redisError) {
      logger.warn("Failed to update approval cache", { redisError });
    }

    // Publish Event Grid event for review completion
    try {
      await publishEvent(
        approvalResult.isComplete ? EventType.WORKFLOW_COMPLETED : EventType.WORKFLOW_STEP_COMPLETED,
        `approvals/${reviewRequest.approvalId}/reviewed`,
        {
          approvalId: reviewRequest.approvalId,
          documentId: approvalData.documentId,
          documentName: approvalData.documentName,
          reviewerId: user.id,
          decision: reviewRequest.decision,
          isComplete: approvalResult.isComplete,
          finalStatus: approvalResult.status,
          organizationId: approvalData.organizationId,
          projectId: approvalData.projectId,
          timestamp: now
        }
      );
    } catch (eventError) {
      logger.warn("Failed to publish review event", { eventError });
    }

    // Send workflow update to Service Bus
    try {
      await serviceBusEnhanced.sendToQueue('workflow-orchestration', {
        body: {
          type: 'approval_review_completed',
          approvalId: reviewRequest.approvalId,
          documentId: approvalData.documentId,
          reviewerId: user.id,
          decision: reviewRequest.decision,
          isComplete: approvalResult.isComplete,
          finalStatus: approvalResult.status,
          organizationId: approvalData.organizationId,
          projectId: approvalData.projectId,
          timestamp: now
        },
        messageId: `approval-review-${reviewRequest.approvalId}-${user.id}-${Date.now()}`,
        correlationId,
        applicationProperties: {
          approvalId: reviewRequest.approvalId,
          documentId: approvalData.documentId,
          reviewerId: user.id,
          organizationId: approvalData.organizationId,
          source: 'document-approval'
        }
      });
    } catch (serviceBusError) {
      logger.warn("Failed to send review update to Service Bus", { serviceBusError });
    }

    // Send real-time notifications via SignalR
    try {
      // Notify document owner
      await signalREnhanced.sendToUser(approvalData.createdBy, 'approvalReviewed', {
        approvalId: reviewRequest.approvalId,
        documentId: approvalData.documentId,
        documentName: approvalData.documentName,
        reviewerId: user.id,
        decision: reviewRequest.decision,
        isComplete: approvalResult.isComplete,
        finalStatus: approvalResult.status,
        timestamp: now
      });

      // Notify other reviewers if approval is complete
      if (approvalResult.isComplete) {
        const otherReviewers = updatedReviewers.filter(r => r.userId !== user.id);
        for (const reviewer of otherReviewers) {
          await signalREnhanced.sendToUser(reviewer.userId, 'approvalCompleted', {
            approvalId: reviewRequest.approvalId,
            documentId: approvalData.documentId,
            documentName: approvalData.documentName,
            finalStatus: approvalResult.status,
            completedBy: user.id,
            timestamp: now
          });
        }
      }
    } catch (signalRError) {
      logger.warn("Failed to send real-time review notifications", { signalRError });
    }

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentReviewed',
      aggregateId: reviewRequest.approvalId,
      aggregateType: 'DocumentApproval',
      version: 1,
      data: {
        approval: updatedApproval,
        review: {
          reviewerId: user.id,
          decision: reviewRequest.decision,
          comments: reviewRequest.comments,
          reviewedAt: now
        },
        isComplete: approvalResult.isComplete
      },
      userId: user.id,
      organizationId: approvalData.organizationId,
      tenantId: user.tenantId
    });

    // Send notifications
    await notifyApprovalUpdate(updatedApproval, user, reviewRequest.decision, approvalResult.isComplete);

    logger.info("Document reviewed successfully", {
      correlationId,
      approvalId: reviewRequest.approvalId,
      documentId: approvalData.documentId,
      decision: reviewRequest.decision,
      isComplete: approvalResult.isComplete,
      finalStatus: approvalResult.status,
      reviewedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        approvalId: reviewRequest.approvalId,
        documentId: approvalData.documentId,
        documentName: approvalData.documentName,
        decision: reviewRequest.decision,
        reviewedAt: now,
        isComplete: approvalResult.isComplete,
        finalStatus: approvalResult.status,
        message: "Document reviewed successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Review document failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkApprovalPermission(document: any, user: any): Promise<boolean> {
  // Document owner can create approval
  if (document.createdBy === user.id) {
    return true;
  }

  // Check organization/project permissions
  if (document.organizationId === user.tenantId) {
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, document.organizationId, 'ACTIVE']);
    
    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN';
    }
  }

  return false;
}

async function validateReviewers(reviewerIds: string[]): Promise<{ valid: boolean; invalidIds: string[] }> {
  try {
    const invalidIds: string[] = [];
    
    for (const reviewerId of reviewerIds) {
      const user = await db.readItem('users', reviewerId, reviewerId);
      if (!user) {
        invalidIds.push(reviewerId);
      }
    }

    return {
      valid: invalidIds.length === 0,
      invalidIds
    };
  } catch (error) {
    logger.error('Failed to validate reviewers', { error, reviewerIds });
    return { valid: false, invalidIds: reviewerIds };
  }
}

function calculateApprovalResult(approvalType: ApprovalType, reviewers: any[]): { status: ApprovalStatus; isComplete: boolean } {
  const approvedCount = reviewers.filter(r => r.status === ReviewerStatus.APPROVED).length;
  const rejectedCount = reviewers.filter(r => r.status === ReviewerStatus.REJECTED).length;
  const pendingCount = reviewers.filter(r => r.status === ReviewerStatus.PENDING).length;
  const totalReviewers = reviewers.length;
  const requiredReviewers = reviewers.filter(r => r.required).length;

  switch (approvalType) {
    case ApprovalType.SINGLE:
      if (approvedCount > 0) return { status: ApprovalStatus.APPROVED, isComplete: true };
      if (rejectedCount > 0) return { status: ApprovalStatus.REJECTED, isComplete: true };
      break;

    case ApprovalType.UNANIMOUS:
      if (rejectedCount > 0) return { status: ApprovalStatus.REJECTED, isComplete: true };
      if (approvedCount === requiredReviewers) return { status: ApprovalStatus.APPROVED, isComplete: true };
      break;

    case ApprovalType.MAJORITY:
      const majorityThreshold = Math.ceil(requiredReviewers / 2);
      if (approvedCount >= majorityThreshold) return { status: ApprovalStatus.APPROVED, isComplete: true };
      if (rejectedCount >= majorityThreshold) return { status: ApprovalStatus.REJECTED, isComplete: true };
      break;

    case ApprovalType.SEQUENTIAL:
      // Check if current reviewer in sequence has completed
      const nextPendingReviewer = reviewers
        .filter(r => r.required)
        .sort((a, b) => a.order - b.order)
        .find(r => r.status === ReviewerStatus.PENDING);
      
      if (!nextPendingReviewer) {
        // All required reviewers have completed
        if (rejectedCount > 0) return { status: ApprovalStatus.REJECTED, isComplete: true };
        return { status: ApprovalStatus.APPROVED, isComplete: true };
      }
      break;

    case ApprovalType.PARALLEL:
      if (pendingCount === 0) {
        // All reviewers have completed
        if (rejectedCount > 0) return { status: ApprovalStatus.REJECTED, isComplete: true };
        return { status: ApprovalStatus.APPROVED, isComplete: true };
      }
      break;
  }

  return { status: ApprovalStatus.PENDING, isComplete: false };
}

async function notifyReviewers(approval: any, document: any, creator: any, action: string): Promise<void> {
  try {
    for (const reviewer of approval.reviewers) {
      if (reviewer.status === ReviewerStatus.PENDING) {
        await notificationService.sendNotification({
          userId: reviewer.userId,
          type: 'DOCUMENT_APPROVAL_ASSIGNED',
          title: 'Document approval required',
          message: `${creator.name || creator.email} has requested your approval for "${document.name}".`,
          priority: 'high',
          metadata: {
            approvalId: approval.id,
            documentId: approval.documentId,
            documentName: document.name,
            dueDate: approval.dueDate,
            approvalType: approval.approvalType,
            organizationId: document.organizationId
          },
          organizationId: document.organizationId,
          projectId: document.projectId
        });
      }
    }
  } catch (error) {
    logger.error('Failed to notify reviewers', { error, approvalId: approval.id });
  }
}

async function notifyApprovalUpdate(approval: any, reviewer: any, decision: string, isComplete: boolean): Promise<void> {
  try {
    // Notify approval creator
    await notificationService.sendNotification({
      userId: approval.createdBy,
      type: 'DOCUMENT_APPROVAL_UPDATED',
      title: `Document ${decision.toLowerCase()}`,
      message: `${reviewer.name || reviewer.email} has ${decision.toLowerCase()} the document "${approval.documentName}".`,
      priority: 'normal',
      metadata: {
        approvalId: approval.id,
        documentId: approval.documentId,
        documentName: approval.documentName,
        decision,
        isComplete,
        reviewedBy: reviewer.id,
        organizationId: approval.organizationId
      },
      organizationId: approval.organizationId,
      projectId: approval.projectId
    });

    // If complete, notify all reviewers
    if (isComplete) {
      for (const rev of approval.reviewers) {
        if (rev.userId !== reviewer.id) {
          await notificationService.sendNotification({
            userId: rev.userId,
            type: 'DOCUMENT_APPROVAL_COMPLETED',
            title: `Document approval ${approval.status.toLowerCase()}`,
            message: `The approval for "${approval.documentName}" has been ${approval.status.toLowerCase()}.`,
            priority: 'normal',
            metadata: {
              approvalId: approval.id,
              documentId: approval.documentId,
              documentName: approval.documentName,
              finalStatus: approval.status,
              organizationId: approval.organizationId
            },
            organizationId: approval.organizationId,
            projectId: approval.projectId
          });
        }
      }
    }
  } catch (error) {
    logger.error('Failed to notify approval update', { error, approvalId: approval.id });
  }
}

// Register functions
app.http('document-approval-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/approvals',
  handler: createDocumentApproval
});

app.http('document-review', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/approvals/review',
  handler: reviewDocument
});
