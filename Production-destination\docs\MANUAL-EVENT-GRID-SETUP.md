# Manual Event Grid Subscriptions Setup Guide

## 🎯 Overview

This guide provides step-by-step instructions to create Event Grid subscriptions through the Azure Portal for the complete document lifecycle. The automated CLI setup failed due to webhook validation, so manual setup is required.

## 📋 Prerequisites

1. **Azure Portal Access**: Admin access to the `docucontext` resource group
2. **Function App Running**: Ensure `hepzlogic` Function App is running
3. **Webhook Endpoints**: Verify endpoints are accessible

## 🔧 Setup Instructions

### Step 1: Navigate to Event Grid Topic

1. Open [Azure Portal](https://portal.azure.com)
2. Navigate to **Resource Groups** → **docucontext**
3. Find and click on **Event Grid Topic**: `hepzeg`

### Step 2: Create Document Lifecycle Events Subscription

1. In the Event Grid Topic, click **+ Event Subscription**
2. Fill in the following details:

**Basic Information:**
- **Name**: `document-lifecycle-events`
- **Event Schema**: `Event Grid Schema`

**Topic Details:**
- **Topic Type**: `Event Grid Topics`
- **Source Resource**: `/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.EventGrid/topics/hepzeg`

**Event Types:**
- **Filter to Event Types**: `Yes`
- **Event Types to Include**:
  - `Document.Uploaded`
  - `Document.Processed`
  - `Document.Shared`
  - `Document.VersionCreated`
  - `Document.VersionRestored`
  - `Document.Archived`
  - `Document.Deleted`

**Endpoint Details:**
- **Endpoint Type**: `Web Hook`
- **Endpoint**: `https://hepzlogic.azurewebsites.net/api/eventgrid/webhook`

**Additional Settings:**
- **Max Delivery Attempts**: `3`
- **Event Time to Live**: `1440` (24 hours)

3. Click **Create**

### Step 3: Create AI Document Analysis Events Subscription

1. Click **+ Event Subscription** again
2. Fill in the following details:

**Basic Information:**
- **Name**: `ai-document-analysis-events`
- **Event Schema**: `Event Grid Schema`

**Event Types:**
- **Event Types to Include**:
  - `Document.AIAnalysisStarted`
  - `Document.AIAnalysisCompleted`
  - `Document.AIAnalysisFailed`

**Endpoint Details:**
- **Endpoint Type**: `Web Hook`
- **Endpoint**: `https://hepzlogic.azurewebsites.net/api/eventgrid/webhook`

3. Click **Create**

### Step 4: Create Document Processing Events Subscription

1. Click **+ Event Subscription** again
2. Fill in the following details:

**Basic Information:**
- **Name**: `document-processing-events`

**Event Types:**
- **Event Types to Include**:
  - `Document.ProcessingStarted`
  - `Document.ProcessingCompleted`
  - `Document.ProcessingFailed`
  - `Document.TextExtracted`
  - `Document.ThumbnailGenerated`

**Endpoint Details:**
- **Endpoint**: `https://hepzlogic.azurewebsites.net/api/eventgrid/webhook`

3. Click **Create**

### Step 5: Create Document Collaboration Events Subscription

1. Click **+ Event Subscription** again
2. Fill in the following details:

**Basic Information:**
- **Name**: `document-collaboration-events`

**Event Types:**
- **Event Types to Include**:
  - `Document.ShareUpdated`
  - `Document.ShareDeleted`
  - `Document.CollaborationStarted`
  - `Document.CollaborationEnded`

**Endpoint Details:**
- **Endpoint**: `https://hepzlogic.azurewebsites.net/api/eventgrid/webhook`

3. Click **Create**

### Step 6: Create Search and Analytics Events Subscription

1. Click **+ Event Subscription** again
2. Fill in the following details:

**Basic Information:**
- **Name**: `document-search-analytics-events`

**Event Types:**
- **Event Types to Include**:
  - `Search.Performed`
  - `Analytics.Generated`
  - `Document.Viewed`
  - `Document.Downloaded`

**Endpoint Details:**
- **Endpoint**: `https://hepzlogic.azurewebsites.net/api/eventgrid/webhook`

3. Click **Create**

### Step 7: Create Storage Events Subscription

1. Navigate back to **Resource Groups** → **docucontext**
2. Find and click on **Event Grid System Topic**: `stdocucontex900520441468-events`
3. Click **+ Event Subscription**
4. Fill in the following details:

**Basic Information:**
- **Name**: `storage-blob-events`
- **Event Schema**: `Event Grid Schema`

**Event Types:**
- **Filter to Event Types**: `Yes`
- **Event Types to Include**:
  - `Microsoft.Storage.BlobCreated`
  - `Microsoft.Storage.BlobDeleted`

**Filters:**
- **Enable Subject Filtering**: `Yes`
- **Subject Begins With**: `/blobServices/default/containers/documents/`

**Endpoint Details:**
- **Endpoint Type**: `Web Hook`
- **Endpoint**: `https://hepzlogic.azurewebsites.net/api/eventgrid/storage-webhook`

5. Click **Create**

## ✅ Verification Steps

### 1. Check Subscription Status
1. Navigate to each Event Grid Topic/System Topic
2. Click on **Event Subscriptions**
3. Verify all subscriptions show **Status**: `Succeeded`

### 2. Test Event Flow
1. Upload a test document through the application
2. Check Azure Function logs for event processing
3. Verify events are being received and processed

### 3. Monitor Event Delivery
1. In each Event Subscription, click **Metrics**
2. Monitor **Delivered Events** and **Failed Events**
3. Check **Dead Letter Events** for any failures

## 🚨 Troubleshooting

### Webhook Validation Fails
- **Issue**: Subscription creation fails with webhook validation error
- **Solution**: 
  1. Ensure Function App `hepzlogic` is running
  2. Check Function App logs for validation requests
  3. Verify webhook endpoint is accessible
  4. Try creating subscription again

### Events Not Being Delivered
- **Issue**: Events are published but not received
- **Solution**:
  1. Check Event Subscription status
  2. Verify endpoint URL is correct
  3. Check Function App authentication settings
  4. Review Event Grid delivery logs

### High Dead Letter Count
- **Issue**: Events are going to dead letter queue
- **Solution**:
  1. Check Function App error logs
  2. Verify event handler implementation
  3. Increase retry attempts if needed
  4. Fix any code issues in event handlers

## 📊 Expected Results

After successful setup, you should see:

1. **6 Event Subscriptions** created successfully
2. **Event delivery** working for document operations
3. **Function App logs** showing event processing
4. **Zero dead letter events** under normal operation
5. **Real-time event flow** for document lifecycle

## 🎯 Next Steps

1. **Complete the manual setup** following this guide
2. **Test document operations** to verify event flow
3. **Monitor performance** metrics
4. **Set up alerts** for failed event deliveries
5. **Document any issues** encountered during setup

## 📝 Notes

- **Webhook Validation**: The Function App must be running and accessible for webhook validation to succeed
- **Event Types**: Use exact event type names as specified in the codebase
- **Endpoint URLs**: Ensure URLs are correct and accessible
- **Retry Policy**: Configure appropriate retry settings for production use
