"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyzeDocument = analyzeDocument;
/**
 * AI Document Analysis Function
 * Handles advanced AI-powered document analysis including classification, entity extraction, and insights
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const ai_services_1 = require("../shared/services/ai-services");
const rag_service_1 = require("../shared/services/rag-service");
const redis_1 = require("../shared/services/redis");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
const service_bus_1 = require("../shared/services/service-bus");
const event_driven_cache_1 = require("../shared/services/event-driven-cache");
// Analysis types enum
var AnalysisType;
(function (AnalysisType) {
    AnalysisType["CLASSIFICATION"] = "CLASSIFICATION";
    AnalysisType["ENTITY_EXTRACTION"] = "ENTITY_EXTRACTION";
    AnalysisType["SENTIMENT_ANALYSIS"] = "SENTIMENT_ANALYSIS";
    AnalysisType["KEY_PHRASE_EXTRACTION"] = "KEY_PHRASE_EXTRACTION";
    AnalysisType["LANGUAGE_DETECTION"] = "LANGUAGE_DETECTION";
    AnalysisType["SUMMARIZATION"] = "SUMMARIZATION";
    AnalysisType["KNOWLEDGE_EXTRACTION"] = "KNOWLEDGE_EXTRACTION";
    AnalysisType["COMPLIANCE_CHECK"] = "COMPLIANCE_CHECK";
})(AnalysisType || (AnalysisType = {}));
// Validation schema
const analyzeDocumentSchema = joi_1.default.object({
    documentId: joi_1.default.string().uuid().required(),
    analysisTypes: joi_1.default.array().items(joi_1.default.string().valid(...Object.values(AnalysisType))).min(1).required(),
    options: joi_1.default.object({
        language: joi_1.default.string().length(2).optional(),
        confidenceThreshold: joi_1.default.number().min(0).max(1).default(0.7),
        maxEntities: joi_1.default.number().integer().min(1).max(100).default(50),
        maxKeyPhrases: joi_1.default.number().integer().min(1).max(50).default(20),
        summaryLength: joi_1.default.string().valid('short', 'medium', 'long').default('medium'),
        complianceStandards: joi_1.default.array().items(joi_1.default.string()).optional()
    }).optional(),
    organizationId: joi_1.default.string().uuid().required(),
    projectId: joi_1.default.string().uuid().required()
});
/**
 * Analyze document handler
 */
async function analyzeDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("AI document analysis started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = analyzeDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { documentId, analysisTypes, options, organizationId, projectId } = value;
        const startTime = Date.now();
        const analysisId = (0, uuid_1.v4)();
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Get document content
        let documentText = document.extractedText || '';
        // If no extracted text, try to get from blob storage
        if (!documentText) {
            const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
            const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
            const blobClient = containerClient.getBlobClient(document.blobName);
            try {
                const downloadResponse = await blobClient.download();
                if (downloadResponse.readableStreamBody) {
                    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
                    const contentType = document.contentType || 'application/octet-stream';
                    // Production: Extract text using Azure Document Intelligence
                    try {
                        documentText = await extractTextWithDocumentIntelligence(documentBuffer, contentType);
                        logger_1.logger.info('Text extracted using Azure Document Intelligence', {
                            documentId,
                            textLength: documentText.length,
                            contentType
                        });
                    }
                    catch (extractError) {
                        logger_1.logger.warn('Document Intelligence extraction failed, using fallback', {
                            documentId,
                            error: extractError instanceof Error ? extractError.message : String(extractError)
                        });
                        documentText = `[Binary content - ${documentBuffer.length} bytes]`;
                    }
                }
            }
            catch (error) {
                logger_1.logger.warn("Could not download document for analysis", { documentId, error });
            }
        }
        if (!documentText) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "No text content available for analysis" }
            }, request);
        }
        // Initialize services
        const serviceBusService = service_bus_1.ServiceBusEnhancedService.getInstance();
        await Promise.all([
            serviceBusService.initialize(),
            event_driven_cache_1.eventDrivenCache.initialize()
        ]);
        // Check Redis cache for recent analysis results
        const cacheKey = `ai-analysis:${documentId}:${JSON.stringify(analysisTypes)}`;
        const cachedResult = await redis_1.redis.getJson(cacheKey);
        if (cachedResult && cachedResult.timestamp && (Date.now() - cachedResult.timestamp) < 1800000) { // 30 minutes cache
            logger_1.logger.info('Returning cached AI analysis result', {
                correlationId,
                documentId,
                cacheAge: Date.now() - cachedResult.timestamp
            });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { ...cachedResult.result, fromCache: true }
            }, request);
        }
        // Publish AI analysis started event
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Document.AIAnalysisStarted',
            subject: `documents/${documentId}/ai-analysis`,
            data: {
                documentId,
                userId: user.id,
                organizationId: user.tenantId,
                analysisTypes,
                startedAt: new Date().toISOString(),
                correlationId
            }
        });
        // Send analysis message to Service Bus
        await serviceBusService.sendToQueue('ai-operations', {
            body: {
                documentId,
                action: 'ai-analysis-started',
                analysisTypes,
                userId: user.id,
                organizationId: user.tenantId,
                correlationId,
                timestamp: new Date().toISOString()
            },
            messageId: `ai-analysis-${documentId}-${Date.now()}`,
            correlationId,
            subject: 'document.ai.analysis.started'
        });
        // Perform comprehensive document analysis using enhanced services
        const analysisResults = await performEnhancedAIAnalysis(documentText, analysisTypes, options || {}, document.contentType, documentId);
        // Calculate overall confidence
        const overallConfidence = calculateOverallConfidence(analysisResults);
        // Save analysis results
        const analysis = {
            id: analysisId,
            documentId,
            analysisTypes,
            results: analysisResults,
            confidence: overallConfidence,
            options,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            organizationId,
            projectId,
            processingTime: Date.now() - startTime,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('document-analyses', analysis);
        // Update document with analysis results
        const updatedDocument = {
            ...document,
            id: documentId,
            aiAnalysis: {
                lastAnalysisId: analysisId,
                lastAnalyzedAt: new Date().toISOString(),
                classification: analysisResults.classification,
                entities: analysisResults.entities?.slice(0, 10), // Store top 10 entities
                sentiment: analysisResults.sentiment,
                language: analysisResults.language,
                summary: analysisResults.summary,
                confidence: overallConfidence
            },
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Cache the analysis result
        await redis_1.redis.setJson(cacheKey, {
            result: {
                documentId,
                analysisId,
                analysisTypes,
                results: analysisResults,
                confidence: overallConfidence,
                processingTime: Date.now() - startTime,
                success: true
            },
            timestamp: Date.now(),
            documentId,
            analysisTypes,
            overallConfidence
        }, 1800); // 30 minutes cache
        // Invalidate related cache entries using Redis
        await redis_1.redis.del(`doc:${documentId}:content`);
        await redis_1.redis.del(`doc:${documentId}:metadata`);
        await redis_1.redis.del(`user:${user.id}:recent-analyses`);
        // Publish AI analysis completed event
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Document.AIAnalysisCompleted',
            subject: `documents/${documentId}/ai-analysis/completed`,
            data: {
                documentId,
                analysisId,
                userId: user.id,
                organizationId,
                analysisTypes,
                results: {
                    overallConfidence,
                    processingTime: Date.now() - startTime,
                    analysisCount: Object.keys(analysisResults).length,
                    entitiesFound: analysisResults.entities?.length || 0
                },
                completedAt: new Date().toISOString(),
                correlationId
            }
        });
        // Send completion message to Service Bus
        await serviceBusService.sendToQueue('ai-operations', {
            body: {
                documentId,
                analysisId,
                action: 'ai-analysis-completed',
                analysisTypes,
                results: {
                    overallConfidence,
                    processingTime: Date.now() - startTime,
                    analysisCount: Object.keys(analysisResults).length,
                    entitiesFound: analysisResults.entities?.length || 0
                },
                userId: user.id,
                organizationId,
                correlationId,
                timestamp: new Date().toISOString()
            },
            messageId: `ai-analysis-complete-${documentId}-${Date.now()}`,
            correlationId,
            subject: 'document.ai.analysis.completed'
        });
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_ai_analyzed",
            userId: user.id,
            organizationId,
            projectId,
            documentId,
            analysisId,
            timestamp: new Date().toISOString(),
            details: {
                analysisTypes,
                confidence: overallConfidence,
                entitiesFound: analysisResults.entities?.length || 0,
                keyPhrasesFound: analysisResults.keyPhrases?.length || 0,
                processingTime: Date.now() - startTime
            },
            tenantId: user.tenantId
        });
        const response = {
            documentId,
            analysisId,
            analysisTypes,
            results: analysisResults,
            confidence: overallConfidence,
            processingTime: Date.now() - startTime,
            success: true
        };
        logger_1.logger.info("AI document analysis completed successfully", {
            correlationId,
            documentId,
            analysisId,
            analysisTypes,
            confidence: overallConfidence,
            userId: user.id,
            processingTime: response.processingTime
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("AI document analysis failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Extract text using Azure Document Intelligence
 */
async function extractTextWithDocumentIntelligence(documentBuffer, contentType) {
    const { DocumentAnalysisClient, AzureKeyCredential } = require('@azure/ai-form-recognizer');
    const endpoint = process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT;
    const key = process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY;
    if (!endpoint || !key) {
        throw new Error('Azure Document Intelligence not configured');
    }
    const client = new DocumentAnalysisClient(endpoint, new AzureKeyCredential(key));
    try {
        // Use prebuilt-read model for general text extraction
        const poller = await client.beginAnalyzeDocument('prebuilt-read', documentBuffer);
        const result = await poller.pollUntilDone();
        if (!result.content) {
            throw new Error('No content extracted from document');
        }
        logger_1.logger.info('Document Intelligence extraction successful', {
            contentLength: result.content.length,
            pages: result.pages?.length || 0,
            contentType
        });
        return result.content;
    }
    catch (error) {
        logger_1.logger.error('Document Intelligence extraction failed', {
            error: error instanceof Error ? error.message : String(error),
            contentType
        });
        throw error;
    }
}
/**
 * Perform AI analysis using Azure AI services (production implementation)
 */
async function performEnhancedAIAnalysis(text, analysisTypes, _options, contentType, documentId) {
    const results = {};
    try {
        // Initialize AI services
        await ai_services_1.aiServices.initialize();
        // Prepare context for AI analysis
        const analysisContext = [
            `Document Type: ${contentType}`,
            `Document Length: ${text.length} characters`,
            `Analysis Types Requested: ${analysisTypes.join(', ')}`
        ];
        for (const analysisType of analysisTypes) {
            try {
                switch (analysisType) {
                    case AnalysisType.CLASSIFICATION:
                        const classificationPrompt = `Analyze this document and classify it. Determine the document type, main categories, and confidence level.

Document Content:
${text.substring(0, 2000)}...

Provide a JSON response with:
- documentType: string
- confidence: number (0-1)
- categories: string[]
- reasoning: string`;
                        const classificationResult = await ai_services_1.aiServices.reason(classificationPrompt, analysisContext, {
                            systemPrompt: 'You are a document classification expert. Analyze documents and provide structured classification results.',
                            temperature: 0.3,
                            maxTokens: 1000
                        });
                        try {
                            const parsed = JSON.parse(classificationResult.content);
                            results.classification = parsed;
                        }
                        catch {
                            results.classification = {
                                documentType: 'general',
                                confidence: classificationResult.confidence,
                                categories: ['document'],
                                reasoning: classificationResult.reasoning
                            };
                        }
                        break;
                    case AnalysisType.ENTITY_EXTRACTION:
                        const entityPrompt = `Extract all important entities from this document. Find people, organizations, locations, dates, amounts, and other key information.

Document Content:
${text.substring(0, 3000)}...

Provide a JSON array of entities with:
- text: string
- category: string
- subCategory: string (optional)
- confidence: number (0-1)
- context: string`;
                        const entityResult = await ai_services_1.aiServices.reason(entityPrompt, analysisContext, {
                            systemPrompt: 'You are an expert at extracting entities from documents. Be thorough and accurate.',
                            temperature: 0.2,
                            maxTokens: 1500
                        });
                        try {
                            results.entities = JSON.parse(entityResult.content);
                        }
                        catch {
                            results.entities = [];
                        }
                        break;
                    case AnalysisType.SENTIMENT_ANALYSIS:
                        const sentimentPrompt = `Analyze the sentiment and tone of this document. Consider the overall emotional tone, formality, and intent.

Document Content:
${text.substring(0, 2000)}...

Provide a JSON response with:
- overall: string (positive/negative/neutral)
- confidence: number (0-1)
- positive: number (0-1)
- neutral: number (0-1)
- negative: number (0-1)
- tone: string (formal/informal/professional/casual)
- reasoning: string`;
                        const sentimentResult = await ai_services_1.aiServices.reason(sentimentPrompt, analysisContext, {
                            systemPrompt: 'You are an expert at analyzing sentiment and tone in documents.',
                            temperature: 0.2,
                            maxTokens: 800
                        });
                        try {
                            results.sentiment = JSON.parse(sentimentResult.content);
                        }
                        catch {
                            results.sentiment = {
                                overall: 'neutral',
                                confidence: sentimentResult.confidence,
                                positive: 0.33,
                                neutral: 0.34,
                                negative: 0.33,
                                tone: 'neutral',
                                reasoning: sentimentResult.reasoning
                            };
                        }
                        break;
                    case AnalysisType.SUMMARIZATION:
                        const summaryPrompt = `Create a comprehensive summary of this document. Include key points, main themes, and important details.

Document Content:
${text}

Provide a JSON response with:
- text: string (summary)
- keyPoints: string[] (main points)
- themes: string[] (main themes)
- confidence: number (0-1)
- wordCount: number`;
                        const summaryResult = await ai_services_1.aiServices.reason(summaryPrompt, analysisContext, {
                            systemPrompt: 'You are an expert at creating comprehensive document summaries.',
                            temperature: 0.4,
                            maxTokens: 1500
                        });
                        try {
                            results.summary = JSON.parse(summaryResult.content);
                        }
                        catch {
                            results.summary = {
                                text: summaryResult.content,
                                keyPoints: [],
                                themes: [],
                                confidence: summaryResult.confidence,
                                wordCount: summaryResult.content.split(' ').length
                            };
                        }
                        break;
                    default:
                        logger_1.logger.warn(`Unsupported analysis type: ${analysisType}`);
                }
            }
            catch (error) {
                logger_1.logger.error(`Error performing ${analysisType} analysis`, { error, documentId });
                results[analysisType.toLowerCase()] = null;
            }
        }
        // Index document for RAG if text is substantial
        if (text.length > 500) {
            try {
                await rag_service_1.ragService.indexDocument({
                    documentId,
                    content: text,
                    metadata: {
                        contentType,
                        analysisTypes,
                        analysisResults: results
                    }
                });
                logger_1.logger.info('Document indexed for RAG', { documentId });
            }
            catch (error) {
                logger_1.logger.warn('Failed to index document for RAG', { documentId, error });
            }
        }
        return results;
    }
    catch (error) {
        logger_1.logger.error('Enhanced AI analysis failed', { error, documentId });
        throw error;
    }
}
/**
 * Calculate overall confidence score
 */
function calculateOverallConfidence(results) {
    const confidenceValues = [];
    if (results.classification?.confidence)
        confidenceValues.push(results.classification.confidence);
    if (results.sentiment?.confidence)
        confidenceValues.push(results.sentiment.confidence);
    if (results.language?.confidence)
        confidenceValues.push(results.language.confidence);
    if (results.entities) {
        const avgEntityConfidence = results.entities.reduce((sum, entity) => sum + entity.confidence, 0) / results.entities.length;
        confidenceValues.push(avgEntityConfidence);
    }
    if (results.keyPhrases) {
        const avgPhraseConfidence = results.keyPhrases.reduce((sum, phrase) => sum + phrase.confidence, 0) / results.keyPhrases.length;
        confidenceValues.push(avgPhraseConfidence);
    }
    return confidenceValues.length > 0
        ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length
        : 0.5;
}
/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        readableStream.on("data", (data) => {
            chunks.push(data instanceof Buffer ? data : Buffer.from(data));
        });
        readableStream.on("end", () => {
            resolve(Buffer.concat(chunks));
        });
        readableStream.on("error", reject);
    });
}
// Register functions
functions_1.app.http('ai-document-analysis', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{id}/ai-analysis',
    handler: analyzeDocument
});
//# sourceMappingURL=ai-document-analysis.js.map