"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.organizationManage = organizationManage;
/**
 * Organization Management Function
 * Handles retrieving, updating, and deleting organizations
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const service_bus_1 = require("../shared/services/service-bus");
const event_grid_handlers_1 = require("./event-grid-handlers");
// Organization tiers enum
var OrganizationTier;
(function (OrganizationTier) {
    OrganizationTier["FREE"] = "FREE";
    OrganizationTier["PROFESSIONAL"] = "PROFESSIONAL";
    OrganizationTier["ENTERPRISE"] = "ENTERPRISE";
})(OrganizationTier || (OrganizationTier = {}));
// Validation schemas
const updateOrganizationSchema = Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    tier: Joi.string().valid(...Object.values(OrganizationTier)).optional(),
    settings: Joi.object({
        allowedDocumentTypes: Joi.array().items(Joi.string()).optional(),
        maxFileSize: Joi.number().integer().min(1024).optional(),
        features: Joi.object({
            aiAnalysis: Joi.boolean().optional(),
            advancedWorkflows: Joi.boolean().optional(),
            bulkProcessing: Joi.boolean().optional(),
            apiAccess: Joi.boolean().optional()
        }).optional()
    }).optional()
});
/**
 * Organization management handler
 */
async function organizationManage(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const organizationId = request.params.organizationId;
    if (!organizationId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Organization ID is required' }
        }, request);
    }
    logger_1.logger.info("Organization management started", { correlationId, organizationId, method: request.method });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get organization
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user is a member of the organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        const membership = memberships[0];
        if (request.method === 'GET') {
            // Get organization details with enriched data
            // Get member count
            const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
            const memberCountResult = await database_1.db.queryItems('organization-members', memberCountQuery, [organizationId, 'active']);
            const memberCount = Number(memberCountResult[0]) || 0;
            // Get project count
            const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
            const projectCountResult = await database_1.db.queryItems('projects', projectCountQuery, [organizationId]);
            const projectCount = Number(projectCountResult[0]) || 0;
            // Get document count
            const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
            const documentCountResult = await database_1.db.queryItems('documents', documentCountQuery, [organizationId]);
            const documentCount = Number(documentCountResult[0]) || 0;
            // Calculate actual storage usage
            const storageUsed = await calculateOrganizationStorageUsage(organizationId);
            // Enrich organization with statistics
            const enrichedOrganization = {
                ...organization,
                memberCount,
                projectCount,
                documentCount,
                storageUsed,
                userRole: membership.role
            };
            // Cache organization data in Redis
            await redis_1.redis.setJson(`org:${organizationId}:details`, enrichedOrganization, 1800); // 30 minutes cache
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: enrichedOrganization
            }, request);
        }
        else if (request.method === 'PATCH') {
            // Update organization
            // Check if user has admin role
            if (membership.role !== 'ADMIN') {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Only organization admins can update organization settings" }
                }, request);
            }
            // Validate request body
            const body = await request.json();
            const { error, value } = updateOrganizationSchema.validate(body);
            if (error) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: 'Validation Error',
                        message: error.details.map(d => d.message).join(', ')
                    }
                }, request);
            }
            const updateData = value;
            // Update organization
            const updatedOrganization = {
                ...organization,
                id: organizationId,
                ...updateData,
                updatedBy: user.id,
                updatedAt: new Date().toISOString()
            };
            // If tier is being updated, update feature settings
            if (updateData.tier) {
                updatedOrganization.settings = {
                    ...updatedOrganization.settings,
                    maxProjects: updateData.tier === OrganizationTier.FREE ? 3 :
                        updateData.tier === OrganizationTier.PROFESSIONAL ? 10 :
                            updateData.tier === OrganizationTier.ENTERPRISE ? 100 : 3,
                    features: {
                        ...updatedOrganization.settings.features,
                        aiAnalysis: updateData.tier !== OrganizationTier.FREE,
                        advancedWorkflows: updateData.tier === OrganizationTier.ENTERPRISE,
                        bulkProcessing: updateData.tier !== OrganizationTier.FREE,
                        apiAccess: updateData.tier === OrganizationTier.ENTERPRISE
                    }
                };
            }
            await database_1.db.updateItem('organizations', updatedOrganization);
            // Invalidate Redis cache
            await redis_1.redis.del(`org:${organizationId}:details`);
            await redis_1.redis.del(`user:${user.id}:organizations`);
            // Publish Event Grid event
            await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.ORGANIZATION_UPDATED, `organizations/${organizationId}/updated`, {
                organizationId,
                updatedFields: Object.keys(updateData),
                previousTier: organization.tier,
                newTier: updateData.tier || organization.tier,
                updatedBy: user.id,
                timestamp: new Date().toISOString()
            });
            // Send Service Bus message for workflow orchestration
            await service_bus_1.serviceBusEnhanced.sendToQueue('analytics-events', {
                body: {
                    eventType: 'organization_updated',
                    organizationId,
                    updatedBy: user.id,
                    updatedFields: Object.keys(updateData),
                    timestamp: new Date().toISOString()
                },
                messageId: `org-update-${organizationId}-${Date.now()}`,
                correlationId: `org-${organizationId}`,
                subject: 'organization.updated'
            });
            // Create activity record
            await database_1.db.createItem('activities', {
                id: (0, uuid_1.v4)(),
                type: "organization_updated",
                userId: user.id,
                organizationId,
                timestamp: new Date().toISOString(),
                details: {
                    updatedFields: Object.keys(updateData),
                    previousTier: organization.tier,
                    newTier: updateData.tier || organization.tier
                },
                tenantId: user.tenantId
            });
            logger_1.logger.info("Organization updated successfully", {
                correlationId,
                organizationId,
                userId: user.id,
                updatedFields: Object.keys(updateData)
            });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    id: organizationId,
                    message: "Organization updated successfully"
                }
            }, request);
        }
        else if (request.method === 'DELETE') {
            // Delete organization
            // Check if user has admin role
            if (membership.role !== 'ADMIN') {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Only organization admins can delete organizations" }
                }, request);
            }
            // Check if organization has projects
            const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
            const projectCountResult = await database_1.db.queryItems('projects', projectCountQuery, [organizationId]);
            const projectCount = Number(projectCountResult[0]) || 0;
            if (projectCount > 0) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: "Cannot delete organization with existing projects",
                        message: `Organization has ${projectCount} projects. Please delete all projects first.`
                    }
                }, request);
            }
            // Delete organization members
            const allMembersQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
            const allMembers = await database_1.db.queryItems('organization-members', allMembersQuery, [organizationId]);
            for (const member of allMembers) {
                await database_1.db.deleteItem('organization-members', member.id, member.id);
            }
            // Delete organization
            await database_1.db.deleteItem('organizations', organizationId, organizationId);
            // Invalidate Redis cache
            await redis_1.redis.del(`org:${organizationId}:details`);
            await redis_1.redis.del(`user:${user.id}:organizations`);
            // Publish Event Grid event
            await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.ORGANIZATION_DELETED, `organizations/${organizationId}/deleted`, {
                organizationId,
                organizationName: organization.name,
                memberCount: allMembers.length,
                deletedBy: user.id,
                timestamp: new Date().toISOString()
            });
            // Send Service Bus message for cleanup workflows
            await service_bus_1.serviceBusEnhanced.sendToQueue('analytics-events', {
                body: {
                    eventType: 'organization_deleted',
                    organizationId,
                    organizationName: organization.name,
                    memberCount: allMembers.length,
                    deletedBy: user.id,
                    timestamp: new Date().toISOString()
                },
                messageId: `org-delete-${organizationId}-${Date.now()}`,
                correlationId: `org-${organizationId}`,
                subject: 'organization.deleted'
            });
            // Create activity record
            await database_1.db.createItem('activities', {
                id: (0, uuid_1.v4)(),
                type: "organization_deleted",
                userId: user.id,
                organizationId,
                timestamp: new Date().toISOString(),
                details: {
                    organizationName: organization.name,
                    memberCount: allMembers.length
                },
                tenantId: user.tenantId
            });
            logger_1.logger.info("Organization deleted successfully", {
                correlationId,
                organizationId,
                userId: user.id
            });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    message: "Organization deleted successfully"
                }
            }, request);
        }
        else {
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Method not allowed" }
            }, request);
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Organization management failed", {
            correlationId,
            organizationId,
            method: request.method,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Calculate actual storage usage for an organization
 */
async function calculateOrganizationStorageUsage(organizationId) {
    try {
        // Get all documents for the organization
        const documentsQuery = 'SELECT c.size FROM c WHERE c.organizationId = @orgId AND c.size != null';
        const documents = await database_1.db.queryItems('documents', documentsQuery, [organizationId]);
        // Calculate total storage in bytes
        const totalBytes = documents.reduce((total, doc) => {
            return total + (doc.size || 0);
        }, 0);
        // Convert to GB and round to 2 decimal places
        const totalGB = Math.round((totalBytes / (1024 * 1024 * 1024)) * 100) / 100;
        logger_1.logger.info('Organization storage calculated', {
            organizationId,
            totalBytes,
            totalGB,
            documentCount: documents.length
        });
        return totalGB;
    }
    catch (error) {
        logger_1.logger.error('Failed to calculate organization storage usage', {
            error: error instanceof Error ? error.message : String(error),
            organizationId
        });
        return 0;
    }
}
// Register functions
functions_1.app.http('organization-manage', {
    methods: ['GET', 'PATCH', 'DELETE', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations/{organizationId}',
    handler: organizationManage
});
//# sourceMappingURL=organization-manage.js.map