# Azure Enhanced Services Configuration

This document outlines the Azure resources configuration for the enhanced Event Grid, Redis, and Service Bus integration.

## 📋 Overview

The enhanced services provide:
- **Event Grid**: Real-time event publishing for user, project, and organization operations
- **Redis Cache**: High-performance caching for profiles, projects, and organizations
- **Service Bus**: Reliable messaging for analytics, thumbnails, migrations, and integrations

## 🚀 Quick Setup

### Prerequisites
- Azure CLI installed and logged in
- Access to the `docucontext` resource group
- Contributor permissions on the subscription

### Setup Commands

**For Linux/macOS:**
```bash
chmod +x azure-setup-enhanced-services.sh
./azure-setup-enhanced-services.sh
```

**For Windows (PowerShell):**
```powershell
.\azure-setup-enhanced-services.ps1
```

### Verification
```bash
chmod +x azure-verify-enhanced-services.sh
./azure-verify-enhanced-services.sh
```

## 📨 Service Bus Configuration

### Queues Created
| Queue Name | Purpose | TTL | Max Delivery |
|------------|---------|-----|--------------|
| `analytics-events` | Analytics data processing | 14 days | 10 |
| `thumbnail-processing` | Document thumbnail generation | 7 days | 5 |
| `data-migration` | Data migration operations | 30 days | 3 |
| `enterprise-integration` | External system sync | 14 days | 5 |
| `ai-operations` | AI processing tasks | 14 days | 10 |
| `scheduled-emails` | Email delivery | 7 days | 5 |
| `document-processing` | Document operations | 14 days | 10 |
| `notification-delivery` | Push notifications | 1 day | 5 |

### Topics and Subscriptions
| Topic Name | Subscription | Purpose |
|------------|--------------|---------|
| `user-events` | `user-processor` | User lifecycle events |
| `project-events` | `project-processor` | Project lifecycle events |
| `organization-events` | `organization-processor` | Organization lifecycle events |
| `workflow-orchestration` | `workflow-processor` | Workflow coordination |
| `document-collaboration` | `collaboration-processor` | Real-time collaboration |
| `analytics-events` | `analytics-aggregator` | Analytics aggregation |
| `monitoring-events` | `system-monitor` | System monitoring |

## ⚡ Event Grid Configuration

### Topic
- **Name**: `hepz-events`
- **Schema**: EventGridSchema
- **Location**: East US

### Event Types Published
| Event Type | Source | Description |
|------------|--------|-------------|
| `user.created` | User Management | New user registration |
| `user.updated` | User Profile | Profile updates |
| `user.deleted` | User Management | User deletion |
| `project.created` | Project Management | New project creation |
| `project.updated` | Project Management | Project updates |
| `project.deleted` | Project Management | Project deletion |
| `organization.created` | Organization Management | New organization |
| `organization.updated` | Organization Management | Organization updates |
| `organization.deleted` | Organization Management | Organization deletion |

### Subscriptions
- **user-events-subscription**: Routes to `event-grid-handlers` function
- **project-events-subscription**: Routes to `event-grid-handlers` function  
- **organization-events-subscription**: Routes to `event-grid-handlers` function

## 🔴 Redis Cache Configuration

### Cache Details
- **Name**: `hepzbackend`
- **SKU**: Standard C1
- **SSL**: Enabled (required)
- **TLS**: Minimum 1.2
- **Port**: 6380 (SSL)

### Cache Keys Used
| Key Pattern | Purpose | TTL |
|-------------|---------|-----|
| `user:{userId}:profile` | User profile data | 1 hour |
| `user:{userId}:organizations` | User organization memberships | 1 hour |
| `project:{projectId}:details` | Project details with stats | 30 minutes |
| `org:{orgId}:details` | Organization details | 30 minutes |
| `org:{orgId}:members` | Organization members | 15 minutes |

## ⚙️ Function App Configuration

### Environment Variables Added
```
EVENT_GRID_TOPIC_ENDPOINT=https://hepz-events.eastus-1.eventgrid.azure.net/api/events
EVENT_GRID_TOPIC_KEY=[Generated Key]
REDIS_HOSTNAME=hepzbackend.redis.cache.windows.net
REDIS_PORT=6380
REDIS_KEY=[Generated Key]
REDIS_CONNECTION_STRING=hepzbackend.redis.cache.windows.net:6380,password=[key],ssl=True,abortConnect=False
SERVICE_BUS_CONNECTION_STRING=Endpoint=sb://hepzbackend.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=[key]
```

## 🔧 Integration Points

### User Management Functions
- **Cache**: Profile data, organization memberships
- **Events**: User lifecycle events to Event Grid
- **Messaging**: Analytics events to Service Bus

### Project Management Functions  
- **Cache**: Project details, member counts, storage usage
- **Events**: Project lifecycle events to Event Grid
- **Messaging**: Analytics and workflow events to Service Bus

### Organization Management Functions
- **Cache**: Organization details, member lists
- **Events**: Organization lifecycle events to Event Grid
- **Messaging**: Analytics and audit events to Service Bus

### Document Processing
- **Messaging**: Thumbnail generation via Service Bus
- **Cache**: Document metadata and thumbnails
- **Events**: Document lifecycle events

## 📊 Monitoring and Metrics

### Key Metrics to Monitor
- **Service Bus**: Message count, dead letter count, processing time
- **Redis**: Hit ratio, memory usage, connection count
- **Event Grid**: Event delivery success rate, retry count

### Monitoring Commands
```bash
# Service Bus metrics
az monitor metrics list --resource /subscriptions/{subscription}/resourceGroups/docucontext/providers/Microsoft.ServiceBus/namespaces/hepzbackend

# Redis metrics  
az monitor metrics list --resource /subscriptions/{subscription}/resourceGroups/docucontext/providers/Microsoft.Cache/Redis/hepzbackend

# Event Grid metrics
az monitor metrics list --resource /subscriptions/{subscription}/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events

# Function App logs
az functionapp logs tail --resource-group docucontext --name hepzlogic
```

## 🚨 Troubleshooting

### Common Issues

1. **Event Grid Subscription Validation**
   - Ensure the Function App is deployed before creating subscriptions
   - Check that the `event-grid-handlers` function exists

2. **Redis Connection Issues**
   - Verify SSL is enabled and TLS 1.2 is used
   - Check firewall rules allow Function App access

3. **Service Bus Authentication**
   - Ensure connection string has correct permissions
   - Verify namespace and queue/topic names are correct

### Verification Steps
1. Run the verification script: `./azure-verify-enhanced-services.sh`
2. Check Function App logs for connection errors
3. Test individual services using Azure CLI
4. Monitor metrics in Azure Portal

## 🔄 Maintenance

### Regular Tasks
- Monitor dead letter queues and process failed messages
- Review Redis memory usage and adjust cache TTLs
- Check Event Grid delivery metrics and retry policies
- Update connection strings if keys are rotated

### Scaling Considerations
- **Redis**: Upgrade to Premium for clustering if needed
- **Service Bus**: Increase message units for higher throughput
- **Event Grid**: No scaling needed, automatically scales

## 📚 Additional Resources

- [Azure Service Bus Documentation](https://docs.microsoft.com/en-us/azure/service-bus-messaging/)
- [Azure Event Grid Documentation](https://docs.microsoft.com/en-us/azure/event-grid/)
- [Azure Cache for Redis Documentation](https://docs.microsoft.com/en-us/azure/azure-cache-for-redis/)
- [Azure Functions Documentation](https://docs.microsoft.com/en-us/azure/azure-functions/)
