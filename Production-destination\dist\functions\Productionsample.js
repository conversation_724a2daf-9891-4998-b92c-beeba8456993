"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Productionsample = Productionsample;
/**
 * Production Sample Function
 * A simple test function to verify the Azure Functions setup is working
 */
const functions_1 = require("@azure/functions");
async function Productionsample(request, context) {
    context.log(`Http function processed request for url "${request.url}"`);
    const name = request.query.get('name') || await request.text() || 'world';
    return {
        status: 200,
        headers: {
            "Content-Type": "application/json"
        },
        jsonBody: {
            message: `Hello, ${name}!`,
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development'
        }
    };
}
functions_1.app.http('Production-sample', {
    methods: ['GET', 'POST'],
    authLevel: 'anonymous',
    route: 'sample',
    handler: Productionsample
});
//# sourceMappingURL=Productionsample.js.map