/**
 * AI Services Integration
 * Production-ready implementations for DeepSeek R1, Llama, and other AI services
 */
export interface AIServiceConfig {
    endpoint: string;
    key: string;
    model: string;
    enabled: boolean;
}
export interface AIRequest {
    prompt: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    context?: string[];
    metadata?: any;
}
export interface AIResponse {
    content: string;
    reasoning?: string;
    confidence: number;
    tokensUsed: number;
    model: string;
    processingTime: number;
}
export interface EmbeddingRequest {
    text: string;
    model?: string;
}
export interface EmbeddingResponse {
    embedding: number[];
    dimensions: number;
    model: string;
    tokensUsed: number;
}
export declare class DeepSeekR1Service {
    private config;
    private client;
    constructor();
    initialize(): Promise<void>;
    reason(request: AIRequest): Promise<AIResponse>;
    generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse>;
    private extractReasoning;
    private calculateConfidence;
}
export declare class LlamaService {
    private config;
    private client;
    constructor();
    initialize(): Promise<void>;
    generateContent(request: AIRequest): Promise<AIResponse>;
    private calculateContentQuality;
}
export declare class AIServiceManager {
    private deepSeekR1;
    private llama;
    private initialized;
    constructor();
    initialize(): Promise<void>;
    reason(prompt: string, context?: string[], options?: Partial<AIRequest>): Promise<AIResponse>;
    generateContent(prompt: string, options?: Partial<AIRequest>): Promise<AIResponse>;
    generateEmbeddings(text: string, model?: string): Promise<EmbeddingResponse>;
    getDeepSeekR1(): DeepSeekR1Service;
    getLlama(): LlamaService;
}
export declare const aiServices: AIServiceManager;
