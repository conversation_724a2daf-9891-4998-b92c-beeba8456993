"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createProject = createProject;
/**
 * Project Create Function
 * Handles creating new projects within organizations
 * Enhanced with notification service and event system integration
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const project_list_1 = require("./project-list");
const notification_1 = require("../shared/services/notification");
const redisEnhanced_1 = require("../shared/services/redisEnhanced");
const serviceBusEnhanced_1 = require("../shared/services/serviceBusEnhanced");
const event_grid_handlers_1 = require("./event-grid-handlers");
// Project visibility enum
var ProjectVisibility;
(function (ProjectVisibility) {
    ProjectVisibility["PRIVATE"] = "PRIVATE";
    ProjectVisibility["ORGANIZATION"] = "ORGANIZATION";
    ProjectVisibility["PUBLIC"] = "PUBLIC";
})(ProjectVisibility || (ProjectVisibility = {}));
// User roles enum
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["MEMBER"] = "MEMBER";
    UserRole["VIEWER"] = "VIEWER";
})(UserRole || (UserRole = {}));
// Validation schema
const createProjectSchema = Joi.object({
    name: Joi.string().required().min(2).max(100),
    description: Joi.string().max(500).optional(),
    organizationId: Joi.string().uuid().required(),
    visibility: Joi.string().valid(...Object.values(ProjectVisibility)).default(ProjectVisibility.PRIVATE),
    tags: Joi.array().items(Joi.string().max(50)).max(10).default([])
});
/**
 * Create project handler
 */
async function createProject(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create project started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createProjectSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { name, description, organizationId, visibility, tags } = value;
        // Check if organization exists and user has access
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user is a member of the organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "You must be a member of the organization to create projects" }
            }, request);
        }
        // Check project limit for organization
        const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const projectCountResult = await database_1.db.queryItems('projects', projectCountQuery, [organizationId]);
        const projectCount = Number(projectCountResult[0]) || 0;
        const maxProjects = organization.settings?.maxProjects || 3;
        if (projectCount >= maxProjects) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Project limit reached for this organization tier",
                    limit: maxProjects,
                    current: projectCount
                }
            }, request);
        }
        // Create project
        const projectId = (0, uuid_1.v4)();
        const project = {
            id: projectId,
            organizationId,
            name,
            description: description || "",
            visibility,
            tags,
            documentIds: [],
            workflowIds: [],
            memberIds: [user.id],
            teamIds: [],
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            settings: {
                defaultDocumentTags: [],
                defaultWorkflowId: null,
                autoProcessing: true
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('projects', project);
        // Add project to organization's project list
        const updatedOrganization = {
            ...organization,
            id: organizationId,
            projectIds: [...(organization.projectIds || []), projectId],
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('organizations', updatedOrganization);
        // Create project membership for creator
        const projectMembership = {
            id: (0, uuid_1.v4)(),
            userId: user.id,
            projectId,
            organizationId,
            role: UserRole.ADMIN,
            joinedAt: new Date().toISOString(),
            invitedBy: user.id,
            permissions: [],
            tenantId: user.tenantId
        };
        await database_1.db.createItem('project-members', projectMembership);
        // Cache project data in Redis
        const redis = redisEnhanced_1.RedisEnhancedService.getInstance();
        const projectWithStats = {
            ...project,
            documentCount: 0,
            workflowCount: 0,
            memberCount: 1,
            storageUsed: 0,
            organizationName: organization.name,
            userRole: 'admin'
        };
        await redis.setJson(`project:${projectId}:details`, projectWithStats, 1800); // 30 minutes cache
        // Invalidate organization cache
        await redis.del(`org:${organizationId}:details`);
        await redis.del(`user:${user.id}:projects`);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "project_created",
            userId: user.id,
            organizationId,
            projectId,
            timestamp: new Date().toISOString(),
            details: {
                projectName: name,
                visibility,
                organizationName: organization.name
            },
            tenantId: user.tenantId
        });
        // Publish Event Grid event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PROJECT_CREATED, `projects/${projectId}/created`, {
            projectId,
            projectName: name,
            organizationId,
            organizationName: organization.name,
            visibility,
            createdBy: user.id,
            memberCount: 1,
            timestamp: new Date().toISOString()
        });
        // Send Service Bus message for workflow orchestration
        const serviceBusService = serviceBusEnhanced_1.ServiceBusEnhancedService.getInstance();
        await serviceBusService.sendToQueue('analytics-events', {
            body: {
                eventType: 'project_created',
                projectId,
                projectName: name,
                organizationId,
                organizationName: organization.name,
                visibility,
                createdBy: user.id,
                timestamp: new Date().toISOString()
            },
            messageId: `project-create-${projectId}-${Date.now()}`,
            correlationId: `project-${projectId}`,
            subject: 'project.created'
        });
        // Send notification
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'PROJECT_CREATED',
            title: 'Project created successfully!',
            message: `Your project "${name}" has been created in ${organization.name}. You can now start adding documents and workflows.`,
            priority: 'normal',
            metadata: {
                projectId,
                projectName: name,
                organizationId,
                organizationName: organization.name,
                visibility
            },
            organizationId,
            projectId
        });
        logger_1.logger.info("Project created successfully", {
            correlationId,
            projectId,
            userId: user.id,
            organizationId,
            visibility
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: projectId,
                name,
                organizationId,
                visibility,
                message: "Project created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create project failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined projects handler
 */
async function handleProjects(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createProject(request, context);
        case 'GET':
            return await (0, project_list_1.listProjects)(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('projects', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects',
    handler: handleProjects
});
//# sourceMappingURL=project-create.js.map