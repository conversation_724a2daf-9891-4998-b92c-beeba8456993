"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentStatus = exports.DocumentType = void 0;
exports.processDocument = processDocument;
/**
 * Document Processing Function
 * Handles AI-powered document analysis and content extraction
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const enhanced_document_intelligence_1 = require("../shared/services/enhanced-document-intelligence");
const rag_service_1 = require("../shared/services/rag-service");
const redis_1 = require("../shared/services/redis");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
const service_bus_1 = require("../shared/services/service-bus");
const event_driven_cache_1 = require("../shared/services/event-driven-cache");
// Document types enum
var DocumentType;
(function (DocumentType) {
    DocumentType["GENERAL"] = "general";
    DocumentType["INVOICE"] = "invoice";
    DocumentType["CONTRACT"] = "contract";
    DocumentType["RECEIPT"] = "receipt";
    DocumentType["IDENTITY"] = "identity";
    DocumentType["FINANCIAL"] = "financial";
    DocumentType["LEGAL"] = "legal";
    DocumentType["MEDICAL"] = "medical";
    DocumentType["TAX"] = "tax";
    DocumentType["INSURANCE"] = "insurance";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
var DocumentStatus;
(function (DocumentStatus) {
    DocumentStatus["PENDING"] = "pending";
    DocumentStatus["UPLOADED"] = "uploaded";
    DocumentStatus["PROCESSING"] = "processing";
    DocumentStatus["PROCESSED"] = "processed";
    DocumentStatus["FAILED"] = "failed";
    DocumentStatus["ARCHIVED"] = "archived";
})(DocumentStatus || (exports.DocumentStatus = DocumentStatus = {}));
// Processing request schema
const processingSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    analysisType: Joi.string().valid('layout', 'general', 'invoice', 'receipt', 'identity', 'custom').default('layout'),
    extractTables: Joi.boolean().default(true),
    extractKeyValuePairs: Joi.boolean().default(true),
    extractEntities: Joi.boolean().default(false),
    customModelId: Joi.string().optional()
});
/**
 * Extract text from document using Azure Document Intelligence
 */
async function extractTextFromDocument(documentBuffer, modelId, client) {
    try {
        const poller = await client.beginAnalyzeDocument(modelId, documentBuffer);
        const result = await poller.pollUntilDone();
        if (!result) {
            throw new Error('No analysis result received');
        }
        // Extract text content
        let extractedText = '';
        if (result.content) {
            extractedText = result.content;
        }
        // Extract tables
        const tables = [];
        if (result.tables) {
            for (const table of result.tables) {
                const tableData = {
                    rowCount: table.rowCount,
                    columnCount: table.columnCount,
                    cells: table.cells.map(cell => ({
                        content: cell.content,
                        rowIndex: cell.rowIndex,
                        columnIndex: cell.columnIndex,
                        confidence: cell.confidence || 0
                    }))
                };
                tables.push(tableData);
            }
        }
        // Extract key-value pairs
        const keyValuePairs = [];
        if (result.keyValuePairs) {
            for (const kvp of result.keyValuePairs) {
                keyValuePairs.push({
                    key: kvp.key?.content || '',
                    value: kvp.value?.content || '',
                    confidence: kvp.confidence
                });
            }
        }
        // Calculate average confidence
        const confidenceValues = [];
        if (result.pages) {
            for (const page of result.pages) {
                if (page.lines) {
                    for (const line of page.lines) {
                        const confidence = line.confidence;
                        if (confidence !== undefined) {
                            confidenceValues.push(confidence);
                        }
                    }
                }
            }
        }
        const averageConfidence = confidenceValues.length > 0
            ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length
            : 0;
        return {
            text: extractedText,
            tables,
            keyValuePairs,
            confidence: averageConfidence
        };
    }
    catch (error) {
        logger_1.logger.error('Document analysis failed', {
            error: error instanceof Error ? error.message : String(error),
            modelId
        });
        throw error;
    }
}
/**
 * Process document handler
 */
async function processDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Document processing started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Initialize services
        const serviceBusService = service_bus_1.ServiceBusEnhancedService.getInstance();
        await Promise.all([
            serviceBusService.initialize(),
            event_driven_cache_1.eventDrivenCache.initialize()
        ]);
        // Validate request body
        const body = await request.json();
        const { error, value } = processingSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const processingRequest = value;
        // Get document metadata
        const document = await database_1.db.readItem('documents', processingRequest.documentId, processingRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Check Redis cache for recent processing results
        const cacheKey = `doc-processing:${processingRequest.documentId}:${JSON.stringify(processingRequest)}`;
        const cachedResult = await redis_1.redis.getJson(cacheKey);
        if (cachedResult && cachedResult.timestamp && (Date.now() - cachedResult.timestamp) < 3600000) { // 1 hour cache
            logger_1.logger.info('Returning cached document processing result', {
                correlationId,
                documentId: processingRequest.documentId,
                cacheAge: Date.now() - cachedResult.timestamp
            });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { ...cachedResult.result, fromCache: true }
            }, request);
        }
        // Update document status to processing
        const updatedDocument = {
            ...document,
            id: document.id,
            status: DocumentStatus.PROCESSING,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Publish document processing started event
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Document.ProcessingStarted',
            subject: `documents/${processingRequest.documentId}/processing`,
            data: {
                documentId: processingRequest.documentId,
                userId: user.id,
                organizationId: user.tenantId,
                analysisType: processingRequest.analysisType,
                features: {
                    extractTables: processingRequest.extractTables,
                    extractKeyValuePairs: processingRequest.extractKeyValuePairs,
                    extractEntities: processingRequest.extractEntities
                },
                startedAt: new Date().toISOString(),
                correlationId
            }
        });
        // Send processing message to Service Bus for workflow orchestration
        await serviceBusService.sendToQueue('document-processing', {
            body: {
                documentId: processingRequest.documentId,
                action: 'process',
                analysisType: processingRequest.analysisType,
                userId: user.id,
                organizationId: user.tenantId,
                correlationId,
                timestamp: new Date().toISOString()
            },
            messageId: `doc-proc-${processingRequest.documentId}-${Date.now()}`,
            correlationId,
            subject: 'document.processing.started'
        });
        // Download document from blob storage
        const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
        if (!connectionString) {
            throw new Error('Azure Storage connection string not configured');
        }
        const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(connectionString);
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        const blobClient = containerClient.getBlobClient(document.blobName);
        const downloadResponse = await blobClient.download();
        if (!downloadResponse.readableStreamBody) {
            throw new Error('Failed to download document content');
        }
        // Convert stream to buffer
        const chunks = [];
        for await (const chunk of downloadResponse.readableStreamBody) {
            chunks.push(Buffer.from(chunk));
        }
        const documentBuffer = Buffer.concat(chunks);
        // Initialize Document Intelligence client
        const endpoint = process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT;
        const key = process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY;
        if (!endpoint || !key) {
            throw new Error('Azure Document Intelligence credentials not configured');
        }
        // Determine model to use
        let modelId = 'prebuilt-layout';
        if (processingRequest.customModelId) {
            modelId = processingRequest.customModelId;
        }
        else {
            switch (processingRequest.analysisType) {
                case 'invoice':
                    modelId = 'prebuilt-invoice';
                    break;
                case 'receipt':
                    modelId = 'prebuilt-receipt';
                    break;
                case 'identity':
                    modelId = 'prebuilt-idDocument';
                    break;
                case 'general':
                    modelId = 'prebuilt-document';
                    break;
                default:
                    modelId = 'prebuilt-layout';
            }
        }
        // Process document using enhanced document intelligence
        const features = [];
        if (processingRequest.extractTables)
            features.push('tables');
        if (processingRequest.extractKeyValuePairs)
            features.push('keyValuePairs');
        if (processingRequest.extractEntities)
            features.push('entities');
        const analysisResult = await enhanced_document_intelligence_1.enhancedDocumentIntelligence.analyzeDocument(documentBuffer, processingRequest.documentId, modelId, features);
        const processingTime = Date.now() - startTime;
        // Create processing result
        const result = {
            documentId: processingRequest.documentId,
            status: 'completed',
            extractedText: analysisResult.extractedText,
            tables: processingRequest.extractTables ? analysisResult.tables : undefined,
            keyValuePairs: processingRequest.extractKeyValuePairs ? analysisResult.keyValuePairs : undefined,
            entities: processingRequest.extractEntities ? analysisResult.entities : undefined,
            confidence: analysisResult.confidence,
            processingTime,
            modelUsed: analysisResult.modelUsed
        };
        // Update document status to processed with comprehensive analysis results
        const finalDocument = {
            ...document,
            id: document.id,
            status: DocumentStatus.PROCESSED,
            extractedText: analysisResult.extractedText,
            documentIntelligence: {
                lastAnalysisId: `analysis-${processingRequest.documentId}-${Date.now()}`,
                lastAnalyzedAt: new Date().toISOString(),
                layout: analysisResult.layout,
                tablesCount: analysisResult.tables.length,
                keyValuePairsCount: analysisResult.keyValuePairs.length,
                entitiesCount: analysisResult.entities.length,
                signaturesCount: analysisResult.signatures.length,
                barcodesCount: analysisResult.barcodes.length,
                formulasCount: analysisResult.formulas.length,
                confidence: analysisResult.confidence,
                hasStructuredData: analysisResult.tables.length > 0 || analysisResult.keyValuePairs.length > 0,
                metadata: analysisResult.metadata
            },
            updatedAt: new Date().toISOString(),
            updatedBy: user.id,
            processingResult: result
        };
        await database_1.db.updateItem('documents', finalDocument);
        // Cache the processing result
        await redis_1.redis.setJson(cacheKey, {
            result,
            timestamp: Date.now(),
            documentId: processingRequest.documentId,
            analysisResult: {
                confidence: analysisResult.confidence,
                modelUsed: analysisResult.modelUsed,
                processingTime: analysisResult.processingTime
            }
        }, 3600); // 1 hour cache
        // Invalidate related cache entries using Redis
        await redis_1.redis.del(`doc:${processingRequest.documentId}:content`);
        await redis_1.redis.del(`doc:${processingRequest.documentId}:metadata`);
        await redis_1.redis.del(`user:${user.id}:documents`);
        await redis_1.redis.del(`org:${user.tenantId}:documents`);
        // Publish document processing completed event
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Document.ProcessingCompleted',
            subject: `documents/${processingRequest.documentId}/completed`,
            data: {
                documentId: processingRequest.documentId,
                userId: user.id,
                organizationId: user.tenantId,
                analysisType: processingRequest.analysisType,
                results: {
                    confidence: analysisResult.confidence,
                    modelUsed: analysisResult.modelUsed,
                    processingTime: analysisResult.processingTime,
                    extractedTextLength: analysisResult.extractedText.length,
                    tablesCount: analysisResult.tables.length,
                    keyValuePairsCount: analysisResult.keyValuePairs.length,
                    entitiesCount: analysisResult.entities.length,
                    hasStructuredData: analysisResult.tables.length > 0 || analysisResult.keyValuePairs.length > 0
                },
                completedAt: new Date().toISOString(),
                correlationId
            }
        });
        // Send completion message to Service Bus for downstream processing
        await serviceBusService.sendToQueue('ai-operations', {
            body: {
                documentId: processingRequest.documentId,
                action: 'analyze-completed',
                analysisType: processingRequest.analysisType,
                results: {
                    confidence: analysisResult.confidence,
                    extractedTextLength: analysisResult.extractedText.length,
                    tablesCount: analysisResult.tables.length,
                    entitiesCount: analysisResult.entities.length
                },
                userId: user.id,
                organizationId: user.tenantId,
                correlationId,
                timestamp: new Date().toISOString()
            },
            messageId: `doc-complete-${processingRequest.documentId}-${Date.now()}`,
            correlationId,
            subject: 'document.processing.completed'
        });
        // Index document for RAG if substantial text content
        if (analysisResult.extractedText && analysisResult.extractedText.length > 500) {
            try {
                await rag_service_1.ragService.indexDocument({
                    documentId: processingRequest.documentId,
                    content: analysisResult.extractedText,
                    metadata: {
                        documentType: processingRequest.analysisType,
                        modelUsed: analysisResult.modelUsed,
                        confidence: analysisResult.confidence,
                        hasLayout: analysisResult.layout.pages.length > 0,
                        tablesCount: analysisResult.tables.length,
                        keyValuePairsCount: analysisResult.keyValuePairs.length,
                        entitiesCount: analysisResult.entities.length
                    }
                });
                logger_1.logger.info('Document indexed for RAG', {
                    documentId: processingRequest.documentId,
                    contentLength: analysisResult.extractedText.length
                });
            }
            catch (ragError) {
                logger_1.logger.warn('Failed to index document for RAG', {
                    documentId: processingRequest.documentId,
                    error: ragError instanceof Error ? ragError.message : String(ragError)
                });
            }
        }
        logger_1.logger.info("Document processing completed", {
            correlationId,
            documentId: processingRequest.documentId,
            processingTime,
            modelUsed: modelId,
            confidence: analysisResult.confidence
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: result
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Document processing failed", {
            correlationId,
            error: errorMessage,
            stack: error instanceof Error ? error.stack : undefined
        });
        // Publish document processing failed event
        try {
            await event_grid_integration_1.eventGridIntegration.publishEvent({
                eventType: 'Document.ProcessingFailed',
                subject: `documents/processing/failed`,
                data: {
                    documentId: request.documentId || 'unknown',
                    error: errorMessage,
                    correlationId,
                    failedAt: new Date().toISOString(),
                    processingTime: Date.now() - startTime
                }
            });
            // Send failure message to Service Bus for error handling
            const serviceBusService = service_bus_1.ServiceBusEnhancedService.getInstance();
            await serviceBusService.sendToQueue('system-monitoring', {
                body: {
                    eventType: 'document-processing-error',
                    documentId: request.documentId || 'unknown',
                    error: errorMessage,
                    correlationId,
                    timestamp: new Date().toISOString(),
                    severity: 'error'
                },
                messageId: `doc-error-${Date.now()}`,
                correlationId,
                subject: 'document.processing.error'
            });
        }
        catch (eventError) {
            logger_1.logger.error('Failed to publish error events', {
                correlationId,
                eventError: eventError instanceof Error ? eventError.message : String(eventError)
            });
        }
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                error: "Document processing failed",
                correlationId,
                message: errorMessage
            }
        }, request);
    }
}
functions_1.app.http('document-processing', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/process',
    handler: processDocument
});
//# sourceMappingURL=document-processing.js.map