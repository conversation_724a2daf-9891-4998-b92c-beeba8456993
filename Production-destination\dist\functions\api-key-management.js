"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiKey = createApiKey;
exports.listApiKeys = listApiKeys;
exports.revokeApiKey = revokeApiKey;
exports.validateApiKey = validateApiKey;
/**
 * API Key Management Function
 * Handles API key creation, management, and authentication
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// API key scopes enum
var ApiKeyScope;
(function (ApiKeyScope) {
    ApiKeyScope["READ"] = "READ";
    ApiKeyScope["WRITE"] = "WRITE";
    ApiKeyScope["DELETE"] = "DELETE";
    ApiKeyScope["ADMIN"] = "ADMIN";
    ApiKeyScope["DOCUMENTS_READ"] = "DOCUMENTS_READ";
    ApiKeyScope["DOCUMENTS_WRITE"] = "DOCUMENTS_WRITE";
    ApiKeyScope["WORKFLOWS_READ"] = "WORKFLOWS_READ";
    ApiKeyScope["WORKFLOWS_EXECUTE"] = "WORKFLOWS_EXECUTE";
    ApiKeyScope["ANALYTICS_READ"] = "ANALYTICS_READ";
    ApiKeyScope["WEBHOOKS_MANAGE"] = "WEBHOOKS_MANAGE";
})(ApiKeyScope || (ApiKeyScope = {}));
// Validation schemas
const createApiKeySchema = joi_1.default.object({
    name: joi_1.default.string().required().min(2).max(100),
    description: joi_1.default.string().max(500).optional(),
    scopes: joi_1.default.array().items(joi_1.default.string().valid(...Object.values(ApiKeyScope))).min(1).required(),
    organizationId: joi_1.default.string().uuid().required(),
    projectId: joi_1.default.string().uuid().optional(),
    expiresAt: joi_1.default.date().iso().optional(),
    rateLimits: joi_1.default.object({
        requestsPerMinute: joi_1.default.number().integer().min(1).max(10000).default(1000),
        requestsPerHour: joi_1.default.number().integer().min(1).max(100000).default(10000),
        requestsPerDay: joi_1.default.number().integer().min(1).max(1000000).default(100000)
    }).optional(),
    allowedIps: joi_1.default.array().items(joi_1.default.string().ip()).optional(),
    allowedDomains: joi_1.default.array().items(joi_1.default.string().domain()).optional()
});
const listApiKeysSchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(100).default(20),
    organizationId: joi_1.default.string().uuid().required(),
    isActive: joi_1.default.boolean().optional(),
    search: joi_1.default.string().max(100).optional()
});
/**
 * Create API key handler
 */
async function createApiKey(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create API key started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createApiKeySchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const apiKeyData = value;
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, apiKeyData.organizationId, 'active']);
        if (memberships.length === 0 || memberships[0].role !== 'ADMIN') {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Only organization admins can create API keys" }
            }, request);
        }
        // Generate API key
        const apiKeyId = (0, uuid_1.v4)();
        const keyValue = generateApiKey();
        const hashedKey = await hashApiKey(keyValue);
        // Create API key record
        const apiKey = {
            id: apiKeyId,
            name: apiKeyData.name,
            description: apiKeyData.description || "",
            keyHash: hashedKey,
            keyPrefix: keyValue.substring(0, 8) + '...',
            scopes: apiKeyData.scopes,
            organizationId: apiKeyData.organizationId,
            projectId: apiKeyData.projectId,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            expiresAt: apiKeyData.expiresAt,
            isActive: true,
            rateLimits: apiKeyData.rateLimits || {
                requestsPerMinute: 1000,
                requestsPerHour: 10000,
                requestsPerDay: 100000
            },
            allowedIps: apiKeyData.allowedIps || [],
            allowedDomains: apiKeyData.allowedDomains || [],
            usage: {
                totalRequests: 0,
                lastUsedAt: null,
                requestsToday: 0,
                requestsThisHour: 0,
                requestsThisMinute: 0
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('api-keys', apiKey);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "api_key_created",
            userId: user.id,
            organizationId: apiKeyData.organizationId,
            projectId: apiKeyData.projectId,
            apiKeyId,
            timestamp: new Date().toISOString(),
            details: {
                apiKeyName: apiKey.name,
                scopes: apiKey.scopes,
                expiresAt: apiKey.expiresAt,
                rateLimits: apiKey.rateLimits
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("API key created successfully", {
            correlationId,
            apiKeyId,
            userId: user.id,
            organizationId: apiKeyData.organizationId,
            scopes: apiKeyData.scopes
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: apiKeyId,
                name: apiKey.name,
                key: keyValue, // Only returned once during creation
                keyPrefix: apiKey.keyPrefix,
                scopes: apiKey.scopes,
                expiresAt: apiKey.expiresAt,
                rateLimits: apiKey.rateLimits,
                message: "API key created successfully. Save this key securely - it won't be shown again."
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create API key failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List API keys handler
 */
async function listApiKeys(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("List API keys started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listApiKeysSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, organizationId, isActive, search } = value;
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Build query
        let queryText = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [organizationId];
        if (isActive !== undefined) {
            queryText += ' AND c.isActive = @isActive';
            parameters.push(isActive);
        }
        if (search) {
            queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
            parameters.push(search);
        }
        // Add ordering
        queryText += ' ORDER BY c.createdAt DESC';
        // Get total count
        const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
        const countResult = await database_1.db.queryItems('api-keys', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;
        // Execute query
        const apiKeys = await database_1.db.queryItems('api-keys', paginatedQuery, parameters);
        // Remove sensitive information
        const sanitizedApiKeys = apiKeys.map((apiKey) => {
            const sanitized = { ...apiKey };
            delete sanitized.keyHash;
            return sanitized;
        });
        logger_1.logger.info("API keys listed successfully", {
            correlationId,
            userId: user.id,
            organizationId,
            count: apiKeys.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                organizationId,
                items: sanitizedApiKeys,
                total,
                page,
                limit,
                hasMore: page * limit < total
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List API keys failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Revoke API key handler
 */
async function revokeApiKey(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const apiKeyId = request.params.apiKeyId;
    if (!apiKeyId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'API Key ID is required' }
        }, request);
    }
    logger_1.logger.info("Revoke API key started", { correlationId, apiKeyId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get API key
        const apiKey = await database_1.db.readItem('api-keys', apiKeyId, apiKeyId);
        if (!apiKey) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "API key not found" }
            }, request);
        }
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, apiKey.organizationId, 'active']);
        if (memberships.length === 0 || memberships[0].role !== 'ADMIN') {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Only organization admins can revoke API keys" }
            }, request);
        }
        // Revoke API key
        const updatedApiKey = {
            ...apiKey,
            id: apiKeyId,
            isActive: false,
            revokedBy: user.id,
            revokedAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('api-keys', updatedApiKey);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "api_key_revoked",
            userId: user.id,
            organizationId: apiKey.organizationId,
            projectId: apiKey.projectId,
            apiKeyId,
            timestamp: new Date().toISOString(),
            details: {
                apiKeyName: apiKey.name,
                revokedBy: user.id
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("API key revoked successfully", {
            correlationId,
            apiKeyId,
            userId: user.id,
            organizationId: apiKey.organizationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: apiKeyId,
                name: apiKey.name,
                revokedAt: updatedApiKey.revokedAt,
                message: "API key revoked successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Revoke API key failed", {
            correlationId,
            apiKeyId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Generate API key
 */
function generateApiKey() {
    const prefix = 'hepz_';
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = prefix;
    for (let i = 0; i < 40; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
/**
 * Hash API key using bcrypt (production implementation)
 */
async function hashApiKey(apiKey) {
    const bcrypt = require('bcrypt');
    const saltRounds = 12; // High security for API keys
    try {
        const hashedKey = await bcrypt.hash(apiKey, saltRounds);
        logger_1.logger.info('API key hashed successfully', {
            keyLength: apiKey.length,
            hashLength: hashedKey.length
        });
        return hashedKey;
    }
    catch (error) {
        logger_1.logger.error('Failed to hash API key', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw new Error('API key hashing failed');
    }
}
/**
 * Validate API key (for authentication middleware)
 */
async function validateApiKey(apiKey) {
    const bcrypt = require('bcrypt');
    try {
        // Get all active API keys for comparison
        const keyQuery = 'SELECT * FROM c WHERE c.isActive = true AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
        const keys = await database_1.db.queryItems('api-keys', keyQuery, [new Date().toISOString()]);
        // Find matching key by comparing with bcrypt
        let matchedKey = null;
        for (const key of keys) {
            const keyData = key;
            try {
                const isMatch = await bcrypt.compare(apiKey, keyData.keyHash);
                if (isMatch) {
                    matchedKey = keyData;
                    break;
                }
            }
            catch (compareError) {
                logger_1.logger.warn('Failed to compare API key hash', {
                    keyId: keyData.id,
                    error: compareError instanceof Error ? compareError.message : String(compareError)
                });
                continue;
            }
        }
        if (!matchedKey) {
            logger_1.logger.warn('API key validation failed - no matching key found');
            return null;
        }
        // Update usage statistics
        const updatedKey = {
            ...matchedKey,
            id: matchedKey.id,
            usage: {
                ...matchedKey.usage,
                totalRequests: (matchedKey.usage?.totalRequests || 0) + 1,
                lastUsedAt: new Date().toISOString()
            },
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('api-keys', updatedKey);
        logger_1.logger.info('API key validated successfully', {
            keyId: matchedKey.id,
            organizationId: matchedKey.organizationId
        });
        return matchedKey;
    }
    catch (error) {
        logger_1.logger.error("API key validation failed", {
            error: error instanceof Error ? error.message : String(error)
        });
        return null;
    }
}
/**
 * Combined API keys handler
 */
async function handleApiKeys(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createApiKey(request, context);
        case 'GET':
            return await listApiKeys(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('api-keys', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'api-keys',
    handler: handleApiKeys
});
functions_1.app.http('api-key-revoke', {
    methods: ['DELETE', 'OPTIONS'],
    authLevel: 'function',
    route: 'api-keys/{apiKeyId}',
    handler: revokeApiKey
});
//# sourceMappingURL=api-key-management.js.map