{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "FUNCTIONS_WORKER_RUNTIME": "node", "FUNCTIONS_EXTENSION_VERSION": "~4", "WEBSITE_NODE_DEFAULT_VERSION": "20.18.3", "FUNCTIONS_NODE_BLOCK_ON_ENTRY_POINT_ERROR": "true", "AzureWebJobsFeatureFlags": "EnableWorkerIndexing", "FUNCTIONS_WORKER_PROCESS_COUNT": "1", "FUNCTIONS_WORKER_RUNTIME_VERSION": "~4", "WEBSITE_RUN_FROM_PACKAGE": "1", "WEBSITE_COMPUTE_MODE": "Dynamic", "WEBSITE_SKU": "Dynamic", "COSMOS_DB_ENDPOINT": "https://hepz.documents.azure.com:443/", "COSMOS_DB_KEY": "****************************************************************************************", "COSMOS_DB_DATABASE": "hepz", "COSMOS_DB_CONNECTION_STRING": "AccountEndpoint=https://hepz.documents.azure.com:443/;AccountKey=****************************************************************************************;", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "DOCUMENT_CONTAINER": "documents", "TEMPLATES_CONTAINER": "templates", "THUMBNAIL_CONTAINER": "thumbnails", "EXPORTS_CONTAINER": "exports", "PROCESSED_CONTAINER": "processed", "DEAD_LETTER_CONTAINER": "dead-letter-events", "EVENT_GRID_ENABLED": "true", "EVENT_GRID_TOPIC_ENDPOINT": "https://hepzeg.eastus-1.eventgrid.azure.net/api/events", "EVENT_GRID_TOPIC_KEY": "y4rzs8d6szrDnHSvaJkC9o0mmrusgGeuo7wLsjcT5ot1Ap4dlWDhJQQJ99BEACYeBjFXJ3w3AAABAZEGM1bk", "EVENT_GRID_TOPIC_NAME": "<PERSON><PERSON><PERSON><PERSON>", "EVENT_GRID_RETRY_ATTEMPTS": "3", "EVENT_GRID_TIMEOUT_MS": "30000", "SERVICE_BUS_CONNECTION_STRING": "Endpoint=sb://hepzbackend.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Nwgjqoxc4M0KxwM1vpB4ysHFL9P4bXAUS+ASbDQ9sBo=", "DEAD_LETTER_QUEUE_NAME": "dead-letter-queue", "MONITORING_TOPIC_NAME": "monitoring-events", "REDIS_ENABLED": "true", "REDIS_CONNECTION_STRING": "rediss://hepzbackend.eastus.redis.azure.net:10000", "REDIS_HOST": "hepzbackend.eastus.redis.azure.net", "AZURE_REDIS_HOST": "hepzbackend.eastus.redis.azure.net", "REDIS_PORT": "10000", "AZURE_REDIS_PORT": "10000", "REDIS_PASSWORD": "uYB4CoTMNKayqhchSzci7CEG6oqpeRhZVAzCaEnHa70=", "REDIS_DATABASE": "0", "REDIS_TLS": "true", "REDIS_CONNECT_TIMEOUT": "10000", "REDIS_COMMAND_TIMEOUT": "5000", "REDIS_MAX_RETRIES": "3", "REDIS_RETRY_DELAY": "1000", "REDIS_ENABLE_OFFLINE_QUEUE": "false", "SIGNALR_CONNECTION_STRING": "Endpoint=https://hepztech.service.signalr.net;AccessKey=9SSBeg7BwC71eF74gZgCTSASGF5kNSUMTMIQbmbBlbdmQ0RLKLbGJQQJ99BEACYeBjFXJ3w3AAAAASRSrRRE;Version=1.0;", "SIGNALR_HUB_NAME": "hepztech", "NOTIFICATION_HUB_CONNECTION_STRING": "Endpoint=sb://hepzdocs.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=cQEXAxQ2FZH/2YFaba3QQwboj8SkScjIzlWywTYsoLQ=", "NOTIFICATION_HUB_NAME": "<PERSON><PERSON>b", "AI_VISION_ENDPOINT": "https://docucontextocr.cognitiveservices.azure.com/", "AI_VISION_KEY": "4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk", "AI_IMAGE_ANALYSIS_ENABLED": "true", "AI_IMAGE_ANALYSIS_ENDPOINT": "https://docucontextocr.cognitiveservices.azure.com/", "AI_IMAGE_ANALYSIS_KEY": "4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk", "AI_IMAGE_ANALYSIS_API_VERSION": "2023-04-01-preview", "AI_DOCUMENT_INTELLIGENCE_ENDPOINT": "https://doucucontextdocuintell.cognitiveservices.azure.com/", "AI_DOCUMENT_INTELLIGENCE_KEY": "Ax4VsRswo2r5hR71casngjTLSRQEpvVvTLzViAuT8rAzSeDOpTlA", "AI_DOCUMENT_INTELLIGENCE_DEFAULT_MODEL_ID": "prebuilt-document", "AI_LLAMA_ENABLED": "true", "AI_LLAMA_ENDPOINT": "https://Llama-3-3-70B-Instruct-finkl.eastus.models.ai.azure.com", "AI_LLAMA_KEY": "k7aBugiIXIGrbRDtuZvivomeiNuWrrW8", "AI_LLAMA_DEFAULT_MODEL": "llama-3-3-70b-instruct", "AI_DEEPSEEK_R1_ENABLED": "true", "AI_DEEPSEEK_R1_ENDPOINT": "https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com", "AI_DEEPSEEK_R1_KEY": "QZoihs6NTgSoGhmeUIvRyOJBvkTDpUlj", "AI_DEEPSEEK_R1_DEFAULT_MODEL": "deepseek-r1-chat", "AI_DEEPSEEK_R1_DEFAULT_EMBEDDING_MODEL": "deepseek-embedding", "AZURE_DEEPSEEK_ENDPOINT": "https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com", "SEARCH_SERVICE_ENABLED": "true", "SEARCH_SERVICE_ENDPOINT": "https://hepzaisearch.search.windows.net", "SEARCH_SERVICE_KEY": "T4QAOne7RPThcrVQD8ytShLEcsHuIKG3eXGeDMINu8AzSeDkeHk6", "SEARCH_INDEX_NAME": "documents", "SEARCH_SEMANTIC_ENABLED": "true", "SEARCH_SEMANTIC_CONFIG": "default", "SEARCH_VECTOR_ENABLED": "true", "AZURE_AD_B2C_TENANT_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AZURE_AD_B2C_TENANT_ID": "4824d1a3-08f0-4377-aa1f-83ca62a2af46", "AZURE_AD_B2C_CLIENT_ID": "a2a369c1-cf18-40e8-b41b-102b02482c51", "AZURE_AD_B2C_CLIENT_SECRET": "****************************************", "AZURE_AD_B2C_AUTHORITY_DOMAIN": "hepzdocs.b2clogin.com", "AZURE_AD_B2C_SIGNIN_FLOW": "B2C_1_SI", "AZURE_AD_B2C_SIGNUP_FLOW": "B2C_1_SU", "AZURE_AD_B2C_PASSWORD_RESET_FLOW": "B2C_1_passwordreset1", "AZURE_AD_B2C_PROFILE_EDIT_FLOW": "B2C_1_profileedit1", "AZURE_AD_B2C_CUSTOM_ATTRIBUTES": "extension_SubscriptionPlan,extension_SubscriptionStatus", "POSTMARK_API_KEY": "************************************", "POSTMARK_FROM_EMAIL": "<EMAIL>", "DEFAULT_FROM_EMAIL": "<EMAIL>", "LEMONSQUEEZY_API_KEY": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "LEMONSQUEEZY_STORE_ID": "80076", "LEMONSQUEEZY_WEBHOOK_SECRET": "lemonmgmtsecret", "LEMONSQUEEZY_WEBHOOK_URL": "https://localhost:7071/api/webhooks/lemonsqueezy", "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=3b8fc07d-020b-42f6-aad9-cafdeec0c80c;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=253e34d3-fd53-4bac-b5a9-10006329b097", "SENTRY_ENABLED": "true", "SENTRY_DSN": "https://<EMAIL>/4509399086006352", "SENTRY_ENVIRONMENT": "production", "SENTRY_DEBUG": "false", "SENTRY_TRACES_SAMPLE_RATE": "1.0", "SENTRY_REPORTABLE_STATUS_CODES": "500,503,504", "LOG_LEVEL": "info", "PERFORMANCE_METRICS_ENABLED": "true", "PERFORMANCE_METRICS_INTERVAL": "60000", "DATABASE_MONITORING_ENABLED": "true", "SLOW_QUERY_THRESHOLD": "1000", "CACHE_ENABLED": "true", "ENABLE_CACHING": "true", "CACHE_TTL": "300000", "ENABLE_CIRCUIT_BREAKER": "true", "ENABLE_RATE_LIMITING": "true", "DEFAULT_RATE_LIMIT": "100", "DEFAULT_RATE_LIMIT_WINDOW": "60000", "ENABLE_DOCUMENT_VERSIONING": "true", "NODE_ENV": "production", "CORS_ORIGIN": "http://localhost:3000", "CORS_ALLOWED_ORIGINS": "http://localhost:3000,https://your-frontend-domain.com", "ALLOWED_ORIGINS": "http://localhost:3000,https://your-frontend-domain.com", "WEBHOOK_ENDPOINTS": "{\"default\":\"https://localhost:7071/api/webhooks\"}"}, "ConnectionStrings": {}}