/**
 * Event Grid Handlers for Azure Functions
 * Handles various Event Grid events and publishes events to Event Grid
 */
/**
 * Event types enum
 */
declare enum EventType {
    DOCUMENT_UPLOADED = "Document.Uploaded",
    DOCUMENT_PROCESSED = "Document.Processed",
    DOCUMENT_SHARED = "Document.Shared",
    ORGANIZATION_CREATED = "Organization.Created",
    ORGANIZATION_UPDATED = "Organization.Updated",
    ORGANIZATION_DELETED = "Organization.Deleted",
    USER_REGISTERED = "User.Registered",
    USER_UPDATED = "User.Updated",
    USER_DELETED = "User.Deleted",
    PROJECT_CREATED = "Project.Created",
    PROJECT_UPDATED = "Project.Updated",
    PROJECT_DELETED = "Project.Deleted",
    WORKFLOW_STARTED = "Workflow.Started",
    WORKFLOW_STEP_COMPLETED = "Workflow.StepCompleted",
    WORKFLOW_COMPLETED = "Workflow.Completed",
    NOTIFICATION_SENT = "Notification.Sent",
    ANALYTICS_GENERATED = "Analytics.Generated",
    SYSTEM_HEALTH_CHECK = "System.HealthCheck",
    PERFORMANCE_ALERT = "Performance.Alert"
}
/**
 * Publish event to Event Grid using enhanced integration service
 */
declare function publishEvent(eventType: EventType, subject: string, data: any, dataVersion?: string): Promise<void>;
/**
 * Enhanced event validation
 */
declare function validateEventGridEvent(event: any): boolean;
/**
 * Enhanced event processing with validation and error handling
 */
declare function processEventGridEventEnhanced(event: any): Promise<void>;
export { publishEvent, EventType, validateEventGridEvent, processEventGridEventEnhanced };
