"use strict";
/**
 * RAG Query Function
 * Handles Retrieval Augmented Generation queries for document-based AI reasoning
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ragQuery = ragQuery;
exports.getRagQueryHistory = getRagQueryHistory;
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const rag_service_1 = require("../shared/services/rag-service");
// Request validation schema
const ragQuerySchema = joi_1.default.object({
    query: joi_1.default.string().min(3).max(1000).required(),
    documentIds: joi_1.default.array().items(joi_1.default.string().uuid()).optional(),
    organizationId: joi_1.default.string().uuid().required(),
    projectId: joi_1.default.string().uuid().optional(),
    maxResults: joi_1.default.number().min(1).max(20).default(5),
    similarityThreshold: joi_1.default.number().min(0).max(1).default(0.7),
    includeMetadata: joi_1.default.boolean().default(true)
});
/**
 * RAG Query handler
 */
async function ragQuery(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = (0, uuid_1.v4)();
    const startTime = Date.now();
    try {
        logger_1.logger.info('RAG query request received', {
            correlationId,
            method: request.method,
            url: request.url
        });
        // Authenticate request
        const user = await (0, auth_1.authenticateRequest)(request);
        if (!user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Unauthorized" }
            }, request);
        }
        // Parse and validate request body
        let requestBody;
        try {
            const bodyText = await request.text();
            requestBody = JSON.parse(bodyText);
        }
        catch (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Invalid JSON in request body" }
            }, request);
        }
        const { error, value: ragQueryRequest } = ragQuerySchema.validate(requestBody);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Validation failed",
                    details: error.details.map(d => d.message)
                }
            }, request);
        }
        // Verify user has access to organization
        if (user.organizationId !== ragQueryRequest.organizationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Perform RAG query
        const ragResult = await rag_service_1.ragService.query(ragQueryRequest);
        // Store query history
        await storeQueryHistory(ragQueryRequest, user, ragResult, correlationId);
        const response = {
            queryId: correlationId,
            query: ragQueryRequest.query,
            answer: ragResult.answer,
            reasoning: ragResult.reasoning,
            sources: ragResult.sources,
            confidence: ragResult.confidence,
            tokensUsed: ragResult.tokensUsed,
            processingTime: ragResult.processingTime,
            totalProcessingTime: Date.now() - startTime,
            success: true
        };
        logger_1.logger.info('RAG query completed successfully', {
            correlationId,
            query: ragQueryRequest.query,
            sourcesFound: ragResult.sources.length,
            confidence: ragResult.confidence,
            processingTime: response.totalProcessingTime
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        logger_1.logger.error('RAG query failed', {
            correlationId,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                error: "Internal server error",
                correlationId
            }
        }, request);
    }
}
/**
 * Get RAG query history
 */
async function getRagQueryHistory(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = (0, uuid_1.v4)();
    try {
        // Authenticate request
        const user = await (0, auth_1.authenticateRequest)(request);
        if (!user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Unauthorized" }
            }, request);
        }
        // Get query parameters
        const organizationId = request.query.get('organizationId');
        const projectId = request.query.get('projectId');
        const limit = parseInt(request.query.get('limit') || '20');
        const offset = parseInt(request.query.get('offset') || '0');
        if (!organizationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "organizationId is required" }
            }, request);
        }
        // Verify user has access to organization
        if (user.organizationId !== organizationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Build query
        let cosmosQuery = 'SELECT * FROM c WHERE c.organizationId = @organizationId';
        const parameters = [{ name: '@organizationId', value: organizationId }];
        if (projectId) {
            cosmosQuery += ' AND c.projectId = @projectId';
            parameters.push({ name: '@projectId', value: projectId });
        }
        cosmosQuery += ' ORDER BY c.createdAt DESC';
        // Get query history
        const queryHistory = await database_1.db.queryItems('rag-query-history', cosmosQuery, parameters);
        // Apply pagination
        const paginatedHistory = queryHistory.slice(offset, offset + limit);
        const response = {
            queries: paginatedHistory.map(query => ({
                id: query.id,
                query: query.query,
                answer: query.answer?.substring(0, 200) + (query.answer?.length > 200 ? '...' : ''),
                confidence: query.confidence,
                sourcesCount: query.sourcesCount,
                createdAt: query.createdAt,
                processingTime: query.processingTime
            })),
            total: queryHistory.length,
            offset,
            limit,
            hasMore: offset + limit < queryHistory.length
        };
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        logger_1.logger.error('Failed to get RAG query history', {
            correlationId,
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                error: "Internal server error",
                correlationId
            }
        }, request);
    }
}
/**
 * Store query history for analytics and improvement
 */
async function storeQueryHistory(ragQueryRequest, user, ragResult, correlationId) {
    try {
        const queryHistory = {
            id: correlationId,
            userId: user.id,
            organizationId: ragQueryRequest.organizationId,
            projectId: ragQueryRequest.projectId,
            query: ragQueryRequest.query,
            answer: ragResult.answer,
            reasoning: ragResult.reasoning,
            confidence: ragResult.confidence,
            sourcesCount: ragResult.sources.length,
            sources: ragResult.sources.map((source) => ({
                documentId: source.documentId,
                documentName: source.documentName,
                relevanceScore: source.relevanceScore
            })),
            tokensUsed: ragResult.tokensUsed,
            processingTime: ragResult.processingTime,
            createdAt: new Date().toISOString(),
            tenantId: user.tenantId
        };
        await database_1.db.createItem('rag-query-history', queryHistory);
        logger_1.logger.info('RAG query history stored', {
            correlationId,
            userId: user.id,
            organizationId: ragQueryRequest.organizationId
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to store RAG query history', {
            correlationId,
            error: error instanceof Error ? error.message : String(error)
        });
        // Don't throw - query should succeed even if history storage fails
    }
}
// Register functions
functions_1.app.http('rag-query', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'rag/query',
    handler: ragQuery
});
functions_1.app.http('rag-query-history', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'rag/history',
    handler: getRagQueryHistory
});
//# sourceMappingURL=rag-query.js.map