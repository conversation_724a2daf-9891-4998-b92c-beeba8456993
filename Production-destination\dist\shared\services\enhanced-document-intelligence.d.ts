/**
 * Enhanced Document Intelligence Service
 * Production-ready implementation for comprehensive document analysis and storage
 */
export interface DocumentAnalysisResult {
    documentId: string;
    extractedText: string;
    layout: DocumentLayout;
    tables: DocumentTable[];
    keyValuePairs: KeyValuePair[];
    entities: DocumentEntity[];
    signatures: DocumentSignature[];
    barcodes: DocumentBarcode[];
    formulas: DocumentFormula[];
    metadata: DocumentMetadata;
    confidence: number;
    processingTime: number;
    modelUsed: string;
}
export interface DocumentLayout {
    pages: DocumentPage[];
    readingOrder: ReadingOrderElement[];
    styles: DocumentStyle[];
    languages: DetectedLanguage[];
}
export interface DocumentPage {
    pageNumber: number;
    width: number;
    height: number;
    angle: number;
    unit: string;
    lines: DocumentLine[];
    words: DocumentWord[];
    paragraphs: DocumentParagraph[];
    sections: DocumentSection[];
}
export interface DocumentLine {
    content: string;
    boundingBox: number[];
    confidence: number;
    words: DocumentWord[];
}
export interface DocumentWord {
    content: string;
    boundingBox: number[];
    confidence: number;
}
export interface DocumentParagraph {
    content: string;
    boundingBox: number[];
    role?: string;
    spans: TextSpan[];
}
export interface DocumentSection {
    spans: TextSpan[];
    elements: string[];
}
export interface DocumentTable {
    rowCount: number;
    columnCount: number;
    cells: DocumentTableCell[];
    boundingBox: number[];
    confidence: number;
}
export interface DocumentTableCell {
    content: string;
    rowIndex: number;
    columnIndex: number;
    rowSpan?: number;
    columnSpan?: number;
    boundingBox: number[];
    confidence: number;
    kind?: string;
}
export interface KeyValuePair {
    key: string;
    value: string;
    keyConfidence: number;
    valueConfidence: number;
    keyBoundingBox?: number[];
    valueBoundingBox?: number[];
}
export interface DocumentEntity {
    category: string;
    subCategory?: string;
    content: string;
    boundingBox: number[];
    confidence: number;
    offset: number;
    length: number;
}
export interface DocumentSignature {
    type: 'handwritten' | 'digital';
    confidence: number;
    boundingBox: number[];
    verified?: boolean;
}
export interface DocumentBarcode {
    kind: string;
    value: string;
    boundingBox: number[];
    confidence: number;
}
export interface DocumentFormula {
    kind: string;
    value: string;
    boundingBox: number[];
    confidence: number;
}
export interface DocumentMetadata {
    pageCount: number;
    documentSize: number;
    creationDate?: string;
    modificationDate?: string;
    author?: string;
    title?: string;
    subject?: string;
    keywords?: string[];
    producer?: string;
    creator?: string;
}
export interface ReadingOrderElement {
    content: string;
    boundingBox: number[];
    kind: string;
}
export interface DocumentStyle {
    isHandwritten?: boolean;
    spans: TextSpan[];
    confidence?: number;
}
export interface DetectedLanguage {
    locale: string;
    confidence: number;
    spans: TextSpan[];
}
export interface TextSpan {
    offset: number;
    length: number;
}
export declare class EnhancedDocumentIntelligenceService {
    private client;
    private initialized;
    constructor();
    initialize(): Promise<void>;
    /**
     * Perform comprehensive document analysis
     */
    analyzeDocument(documentBuffer: Buffer, documentId: string, modelId?: string, features?: string[]): Promise<DocumentAnalysisResult>;
    /**
     * Extract layout information
     */
    private extractLayout;
    /**
     * Extract tables
     */
    private extractTables;
    /**
     * Extract key-value pairs
     */
    private extractKeyValuePairs;
    /**
     * Extract entities
     */
    private extractEntities;
    /**
     * Extract signatures (placeholder - would need specific model)
     */
    private extractSignatures;
    /**
     * Extract barcodes (placeholder - would need specific model)
     */
    private extractBarcodes;
    /**
     * Extract formulas (placeholder - would need specific model)
     */
    private extractFormulas;
    /**
     * Extract metadata
     */
    private extractMetadata;
    /**
     * Calculate overall confidence
     */
    private calculateOverallConfidence;
    /**
     * Store analysis results in database
     */
    private storeAnalysisResults;
    /**
     * Index document for RAG
     */
    private indexDocumentForRAG;
    /**
     * Generate AI-powered insights
     */
    private generateDocumentInsights;
}
export declare const enhancedDocumentIntelligence: EnhancedDocumentIntelligenceService;
