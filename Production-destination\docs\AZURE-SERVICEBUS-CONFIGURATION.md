# 🚀 Azure Service Bus Configuration for Enhanced Workflow Functions

This document outlines the complete Azure Service Bus configuration for the enhanced workflow, signing, approval chain, and document collaboration functions.

## 📊 Configuration Summary

### Resource Details
- **Resource Group**: `docucontext`
- **Service Bus Namespace**: `hepzbackend`
- **Location**: `eastus`
- **Configuration Date**: June 3, 2025

## 🔧 Configured Queues

### Core Workflow Queues (Existing)
| Queue Name | Max Delivery Count | Lock Duration | Max Size (MB) | TTL | Dead Lettering |
|------------|-------------------|---------------|---------------|-----|----------------|
| `workflow-orchestration` | 10 | PT1M | 1024 | Infinite | false |
| `ai-operations` | 10 | PT1M | 1024 | Infinite | false |
| `scheduled-emails` | 10 | PT1M | 1024 | Infinite | false |
| `document-processing` | 10 | PT1M | 1024 | Infinite | false |
| `notification-delivery` | 10 | PT1M | 1024 | Infinite | false |

### Enhanced Workflow Queues (New)
| Queue Name | Max Delivery Count | Lock Duration | Max Size (MB) | TTL | Dead Lettering |
|------------|-------------------|---------------|---------------|-----|----------------|
| `approval-workflows` | 5 | PT2M | 2048 | P7D | true |
| `document-signing` | 5 | PT1M | 1024 | P7D | true |
| `collaboration-events` | 10 | PT30S | 1024 | P1D | false |
| `workflow-automation` | 10 | PT1M | 2048 | P14D | true |

## 📡 Configured Topics & Subscriptions

### Core Topics (Existing)
#### `document-collaboration`
- **Subscriptions**: `collaboration-processor`
- **Max Size**: 1024 MB
- **TTL**: Infinite
- **Duplicate Detection**: false

#### `analytics-events`
- **Subscriptions**: `analytics-aggregator`
- **Max Size**: 1024 MB
- **TTL**: Infinite
- **Duplicate Detection**: false

#### `monitoring-events`
- **Subscriptions**: `system-monitor`
- **Max Size**: 1024 MB
- **TTL**: Infinite
- **Duplicate Detection**: false

### Enhanced Topics (New)

#### `approval-events`
- **Max Size**: 2048 MB
- **TTL**: P7D (7 days)
- **Duplicate Detection**: true
- **Ordering**: true
- **Subscriptions**:
  - `approval-processor` (Lock: PT2M, Max Delivery: 5)
  - `approval-notifications` (Lock: PT1M, Max Delivery: 10)
  - `approval-analytics` (Lock: PT1M, Max Delivery: 10)

#### `signing-events`
- **Max Size**: 1024 MB
- **TTL**: P7D (7 days)
- **Duplicate Detection**: true
- **Ordering**: true
- **Subscriptions**:
  - `signing-processor` (Lock: PT1M, Max Delivery: 5)
  - `signing-notifications` (Lock: PT30S, Max Delivery: 10)
  - `signing-audit` (Lock: PT1M, Max Delivery: 10)

#### `workflow-events`
- **Max Size**: 2048 MB
- **TTL**: P14D (14 days)
- **Duplicate Detection**: true
- **Ordering**: true
- **Subscriptions**:
  - `workflow-processor` (Lock: PT1M, Max Delivery: 10)
  - `workflow-analytics` (Lock: PT1M, Max Delivery: 10)
  - `workflow-monitoring` (Lock: PT1M, Max Delivery: 10)

#### `real-time-events`
- **Max Size**: 1024 MB
- **TTL**: P1D (1 day)
- **Duplicate Detection**: false
- **Ordering**: false
- **Subscriptions**:
  - `signalr-processor` (Lock: PT30S, Max Delivery: 10)
  - `collaboration-sync` (Lock: PT30S, Max Delivery: 10)

## 🎯 Function Integration Mapping

### Document Signing (`document-sign.ts`)
- **Publishes to**: Event Grid → `documents/{id}/signed`
- **Sends to**: 
  - Queue: `notification-delivery`
  - Topic: `signing-events`
- **Caches in**: Redis (`signed-document:{id}`)

### Document Approval (`document-approval.ts`)
- **Publishes to**: Event Grid → `approvals/{id}/created`, `approvals/{id}/reviewed`
- **Sends to**: 
  - Queue: `workflow-orchestration`
  - Topic: `approval-events`
- **Caches in**: Redis (`approval:{id}`)
- **SignalR**: Real-time notifications to reviewers

### Real-time Collaboration (`real-time-collaboration.ts`)
- **Publishes to**: Event Grid → `documents/{id}/collaboration/session-created`
- **Sends to**: 
  - Topic: `document-collaboration`
  - Topic: `real-time-events`
- **SignalR**: Group management (`session-{id}`)
- **Caches in**: Redis (session data, messages)

### Workflow Automation (`workflow-automation.ts`)
- **Sends to**: 
  - Queue: `scheduled-emails` (email actions)
  - Queue: `notification-delivery` (notification actions)
  - Queue: `workflow-orchestration` (workflow actions)
  - Topic: `workflow-events`
- **SignalR**: Real-time workflow notifications

## 🔄 Message Flow Architecture

```
Enhanced Functions → Service Bus Queues/Topics → Service Bus Handlers → Processing
                  ↓
                Event Grid → External Systems
                  ↓
                Redis Cache → Quick Access
                  ↓
                SignalR → Real-time Updates
```

## 🛡️ Error Handling & Reliability

### Dead Letter Queues
- **Enhanced queues** have dead lettering enabled
- **Real-time queues** use immediate processing without dead lettering
- **Core queues** maintain existing configuration

### Retry Policies
- **Approval workflows**: 5 max deliveries with 2-minute lock
- **Document signing**: 5 max deliveries with 1-minute lock
- **Real-time events**: 10 max deliveries with 30-second lock
- **Workflow automation**: 10 max deliveries with 1-minute lock

### Monitoring
- All topics have dedicated monitoring subscriptions
- Analytics subscriptions for performance tracking
- Audit subscriptions for compliance

## 🚀 Deployment Status

✅ **All queues configured and active**
✅ **All topics configured and active**
✅ **Core subscriptions configured and active**
✅ **Enhanced subscriptions configured and active**
✅ **Functions updated to use new infrastructure**
✅ **Error handling and monitoring in place**

## 🔧 Management Commands

### List all queues
```bash
az servicebus queue list --resource-group docucontext --namespace-name hepzbackend --output table
```

### List all topics
```bash
az servicebus topic list --resource-group docucontext --namespace-name hepzbackend --output table
```

### List subscriptions for a topic
```bash
az servicebus topic subscription list --resource-group docucontext --namespace-name hepzbackend --topic-name approval-events --output table
```

### Monitor queue metrics
```bash
az servicebus queue show --resource-group docucontext --namespace-name hepzbackend --name approval-workflows
```

## 📈 Performance Optimization

- **Batched operations** enabled on all resources
- **Appropriate TTL** settings for different message types
- **Lock duration** optimized for processing time
- **Duplicate detection** enabled for critical workflows
- **Message ordering** enabled for sequential processes

---

**Configuration completed**: June 3, 2025
**Status**: ✅ Production Ready
**Next steps**: Monitor message flow and adjust settings based on usage patterns
