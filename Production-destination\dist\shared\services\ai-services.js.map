{"version": 3, "file": "ai-services.js", "sourceRoot": "", "sources": ["../../../src/shared/services/ai-services.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AAyCzC,8CAA8C;AAC9C,MAAa,iBAAiB;IAI5B;QACE,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,EAAE;YACnD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE;YACzC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,kBAAkB;YACrE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM;SACvD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9F,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAAkB;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG;gBACf;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO,CAAC,YAAY,IAAI,wHAAwH;iBAC1J;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,OAAO,CAAC,MAAM;iBACxB;aACF,CAAC;YAEF,0BAA0B;YAC1B,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;oBACpB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,yBAAyB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;iBACjE,CAAC,CAAC;YACL,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE;gBACjF,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,GAAG;aAC1B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBACxD,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBAC5C,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC;gBAC5C,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,cAAc;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAyB;QAChD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,IAAI,oBAAoB,CAAC;YAEnH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YAEjF,OAAO;gBACL,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;gBACrC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM;gBAC7C,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC;aAC7C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAAe;QACtC,oDAAoD;QACpD,MAAM,iBAAiB,GAAG;YACxB,4CAA4C;YAC5C,4BAA4B;YAC5B,2BAA2B;SAC5B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,mBAAmB,CAAC,MAAW;QACrC,yDAAyD;QACzD,IAAI,MAAM,CAAC,YAAY,KAAK,MAAM,EAAE,CAAC;YACnC,OAAO,GAAG,CAAC;QACb,CAAC;aAAM,IAAI,MAAM,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YAC5C,OAAO,GAAG,CAAC;QACb,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAjID,8CAiIC;AAED,wCAAwC;AACxC,MAAa,YAAY;IAIvB;QACE,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;YAC7C,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;YACnC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,wBAAwB;YACrE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,MAAM;SACjD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9F,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAkB;QACtC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG;gBACf;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO,CAAC,YAAY,IAAI,iHAAiH;iBACnJ;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,OAAO,CAAC,MAAM;iBACxB;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE;gBACjF,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI;aAC3B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,UAAU,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBAChE,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC;gBAC5C,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,cAAc;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,OAAe;QAC7C,oCAAoC;QACpC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC9C,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QACrD,MAAM,mBAAmB,GAAG,SAAS,GAAG,aAAa,CAAC;QAEtD,kBAAkB;QAClB,IAAI,OAAO,GAAG,GAAG,CAAC;QAElB,IAAI,SAAS,GAAG,EAAE;YAAE,OAAO,IAAI,GAAG,CAAC;QACnC,IAAI,mBAAmB,GAAG,EAAE,IAAI,mBAAmB,GAAG,EAAE;YAAE,OAAO,IAAI,GAAG,CAAC;QACzE,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,GAAG,CAAC,CAAC,gBAAgB;QAC5D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,GAAG,CAAC,CAAC,yBAAyB;QAEzE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAChC,CAAC;CACF;AAtFD,oCAsFC;AAED,oDAAoD;AACpD,MAAa,gBAAgB;IAK3B;QAFQ,gBAAW,GAAY,KAAK,CAAC;QAGnC,IAAI,CAAC,UAAU,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC1C,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAE7B,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;gBAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;aACxB,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,OAAkB,EAAE,OAA4B;QAC3E,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAExB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YAC5B,MAAM;YACN,OAAO;YACP,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,OAA4B;QAChE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAExB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAChC,MAAM;YACN,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,KAAc;QACnD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAExB,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;CACF;AA3DD,4CA2DC;AAED,4BAA4B;AACf,QAAA,UAAU,GAAG,IAAI,gBAAgB,EAAE,CAAC"}