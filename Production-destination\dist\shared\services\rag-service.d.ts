/**
 * RAG (Retrieval Augmented Generation) Service
 * Production-ready implementation using Azure AI Search for document-based AI reasoning and content generation
 */
export interface SearchDocument {
    id: string;
    documentId: string;
    title: string;
    content: string;
    contentVector: number[];
    chunkIndex: number;
    pageNumber?: number;
    section?: string;
    documentType?: string;
    organizationId: string;
    projectId?: string;
    createdAt: string;
    updatedAt: string;
    metadata: {
        fileName: string;
        contentType: string;
        size: number;
        author: string;
        tags: string[];
        wordCount: number;
        extractedAt: string;
    };
}
export interface SearchConfig {
    endpoint: string;
    key: string;
    indexName: string;
    semanticConfig?: string;
    vectorEnabled: boolean;
    semanticEnabled: boolean;
}
export interface RAGQuery {
    query: string;
    documentIds?: string[];
    organizationId: string;
    projectId?: string;
    maxResults?: number;
    similarityThreshold?: number;
    includeMetadata?: boolean;
}
export interface RAGResult {
    answer: string;
    reasoning?: string;
    sources: Array<{
        documentId: string;
        documentName: string;
        content: string;
        relevanceScore: number;
        pageNumber?: number;
        section?: string;
    }>;
    confidence: number;
    tokensUsed: number;
    processingTime: number;
}
export interface DocumentIndexRequest {
    documentId: string;
    content: string;
    metadata?: any;
    chunkSize?: number;
    overlapSize?: number;
}
export declare class RAGService {
    private initialized;
    private searchConfig;
    private readonly CHUNK_SIZE;
    private readonly OVERLAP_SIZE;
    private readonly SIMILARITY_THRESHOLD;
    constructor();
    initialize(): Promise<void>;
    /**
     * Test Azure AI Search connection
     */
    private testSearchConnection;
    /**
     * Index a document for RAG retrieval using Azure AI Search
     */
    indexDocument(request: DocumentIndexRequest): Promise<void>;
    /**
     * Ensure Azure AI Search index exists with proper schema
     */
    private ensureSearchIndex;
    /**
     * Index documents in Azure AI Search
     */
    private indexDocumentsInSearch;
    /**
     * Perform RAG query - retrieve relevant documents and generate answer using Azure AI Search
     */
    query(ragQuery: RAGQuery): Promise<RAGResult>;
    /**
     * Search relevant chunks using Azure AI Search
     */
    private searchRelevantChunks;
    /**
     * Split content into overlapping chunks
     */
    private splitIntoChunks;
    /**
     * Remove document from Azure AI Search index
     */
    removeDocumentFromIndex(documentId: string): Promise<void>;
    /**
     * Get document names for source attribution
     */
    private getDocumentNames;
}
export declare const ragService: RAGService;
