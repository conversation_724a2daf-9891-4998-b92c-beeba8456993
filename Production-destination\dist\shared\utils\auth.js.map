{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/shared/utils/auth.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BH,oCAWC;AAKD,sCAoGC;AAKD,kDAWC;AAKD,0BAEC;AAKD,sCAEC;AAKD,kCAmBC;AAjMD,kDAAoC;AACpC,+CAAiC;AACjC,qCAAkC;AAkBlC;;GAEG;AACH,SAAgB,YAAY,CAAC,OAAoB;IAC/C,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACxD,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACrC,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa,CAAC,KAAa;IAC/C,IAAI,CAAC;QACH,8CAA8C;QAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;QAEpD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3B,eAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACnD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uCAAuC;aAC/C,CAAC;QACJ,CAAC;QAED,kEAAkE;QAClE,IAAI,OAAY,CAAC;QACjB,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC;gBACrC,OAAO,EAAE,WAAW,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,QAAQ,sBAAsB;gBAC/F,KAAK,EAAE,IAAI;gBACX,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,MAAM,EAAE,aAAa;gBAClC,OAAO,EAAE,KAAK;gBACd,qBAAqB,EAAE,CAAC;aACzB,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAQ,CAAC;YAC7D,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBACvD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,yBAAyB;iBACjC,CAAC;YACJ,CAAC;YAED,4BAA4B;YAC5B,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC/D,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;YAEtC,6CAA6C;YAC7C,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE;gBACtC,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,WAAW,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,QAAQ,QAAQ;gBAChF,UAAU,EAAE,CAAC,OAAO,CAAC;aACtB,CAAQ,CAAC;YAEV,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACjD,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG;gBACvB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG;aACxB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,WAAW,EAAE,CAAC;YACrB,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBAChD,KAAK,EAAE,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;aAChF,CAAC,CAAC;YACH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qCAAqC;aAC7C,CAAC;QACJ,CAAC;QAED,+FAA+F;QAE/F,sCAAsC;QACtC,MAAM,IAAI,GAAgB;YACxB,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;YAC9D,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,kBAAkB;YACzE,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,IAAI,OAAO,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE;YACvF,QAAQ,EAAE,OAAO,CAAC,GAAG,IAAI,QAAQ;YACjC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,KAAK;YACvD,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;YAC1B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;SACvC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yCAAyC;aACjD,CAAC;QACJ,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1C,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC;YAC9B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC;SAC3C,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;SACL,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3G,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,yBAAyB;SACjC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CAAC,OAAoB;IAC5D,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAEpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kCAAkC;SAC1C,CAAC;IACJ,CAAC;IAED,OAAO,MAAM,aAAa,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,IAAiB,EAAE,YAAoB;IAC7D,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;AACrD,CAAC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,IAAiB,EAAE,kBAA0B;IACzE,OAAO,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAC;AACjE,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,OAAgF;IAC1G,OAAO,KAAK,EAAE,OAAoB,EAAE,OAAY,EAAE,EAAE;QAClD,MAAM,UAAU,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO;gBACL,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,QAAQ,EAAE;oBACR,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,UAAU,CAAC,KAAK,IAAI,yBAAyB;iBACvD;aACF,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC,CAAC;AACJ,CAAC"}