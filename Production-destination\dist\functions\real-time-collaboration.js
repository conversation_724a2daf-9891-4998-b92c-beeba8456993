"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCollaborationSession = createCollaborationSession;
exports.joinCollaborationSession = joinCollaborationSession;
/**
 * Real-Time Collaboration Function
 * Handles real-time document collaboration and editing
 * Migrated from old-arch/src/collaboration-service/session/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
const service_bus_1 = require("../shared/services/service-bus");
const event_driven_cache_1 = require("../shared/services/event-driven-cache");
// Collaboration types and enums
var SessionStatus;
(function (SessionStatus) {
    SessionStatus["ACTIVE"] = "ACTIVE";
    SessionStatus["INACTIVE"] = "INACTIVE";
    SessionStatus["ENDED"] = "ENDED";
})(SessionStatus || (SessionStatus = {}));
var OperationType;
(function (OperationType) {
    OperationType["INSERT"] = "INSERT";
    OperationType["DELETE"] = "DELETE";
    OperationType["RETAIN"] = "RETAIN";
    OperationType["FORMAT"] = "FORMAT";
})(OperationType || (OperationType = {}));
var CursorType;
(function (CursorType) {
    CursorType["SELECTION"] = "SELECTION";
    CursorType["CARET"] = "CARET";
})(CursorType || (CursorType = {}));
// Validation schemas
const createSessionSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    sessionName: Joi.string().min(1).max(100).optional(),
    maxParticipants: Joi.number().min(1).max(50).default(10),
    allowAnonymous: Joi.boolean().default(false),
    permissions: Joi.object({
        canEdit: Joi.boolean().default(true),
        canComment: Joi.boolean().default(true),
        canShare: Joi.boolean().default(false)
    }).optional()
});
const joinSessionSchema = Joi.object({
    sessionId: Joi.string().uuid().required(),
    displayName: Joi.string().min(1).max(50).optional()
});
const sendOperationSchema = Joi.object({
    sessionId: Joi.string().uuid().required(),
    operations: Joi.array().items(Joi.object({
        type: Joi.string().valid(...Object.values(OperationType)).required(),
        position: Joi.number().min(0).required(),
        content: Joi.string().optional(),
        length: Joi.number().min(0).optional(),
        attributes: Joi.object().optional()
    })).min(1).required(),
    revision: Joi.number().min(0).required(),
    clientId: Joi.string().required()
});
const updateCursorSchema = Joi.object({
    sessionId: Joi.string().uuid().required(),
    cursor: Joi.object({
        type: Joi.string().valid(...Object.values(CursorType)).required(),
        position: Joi.number().min(0).required(),
        length: Joi.number().min(0).optional(),
        userId: Joi.string().uuid().required()
    }).required()
});
/**
 * Create collaboration session handler
 */
async function createCollaborationSession(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create collaboration session started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Initialize services
        const serviceBusService = service_bus_1.ServiceBusEnhancedService.getInstance();
        await serviceBusService.initialize();
        // Validate request body
        const body = await request.json();
        const { error, value } = createSessionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const sessionRequest = value;
        // Get document and check access
        const document = await database_1.db.readItem('documents', sessionRequest.documentId, sessionRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Check if there's already an active session for this document
        const existingSession = await getActiveSessionForDocument(sessionRequest.documentId);
        if (existingSession) {
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    session: existingSession,
                    message: "Active session already exists for this document"
                }
            }, request);
        }
        // Create collaboration session
        const sessionId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const session = {
            id: sessionId,
            documentId: sessionRequest.documentId,
            sessionName: sessionRequest.sessionName || `${documentData.name} - Collaboration`,
            status: SessionStatus.ACTIVE,
            createdBy: user.id,
            createdAt: now,
            lastActivity: now,
            maxParticipants: sessionRequest.maxParticipants || 10,
            allowAnonymous: sessionRequest.allowAnonymous || false,
            permissions: {
                canEdit: sessionRequest.permissions?.canEdit ?? true,
                canComment: sessionRequest.permissions?.canComment ?? true,
                canShare: sessionRequest.permissions?.canShare ?? false
            },
            participants: [{
                    userId: user.id,
                    displayName: user.name || user.email,
                    joinedAt: now,
                    isActive: true
                }],
            currentRevision: 0,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('collaboration-sessions', session);
        // Store session in Redis for real-time access
        await redis_1.redis.setex(`session:${sessionId}`, 3600, JSON.stringify(session)); // 1 hour TTL
        // Initialize document state in Redis using enhanced service
        await redis_1.redis.setDocumentContent(sessionRequest.documentId, documentData.content || '', 3600);
        await redis_1.redis.set(`document:${sessionRequest.documentId}:revision`, '0');
        // Cache session metadata
        await redis_1.redis.setJson(`session:${sessionId}:metadata`, {
            sessionId,
            documentId: sessionRequest.documentId,
            createdBy: user.id,
            organizationId: documentData.organizationId,
            status: SessionStatus.ACTIVE,
            createdAt: now
        }, 3600);
        // Create SignalR group for the session
        try {
            await signalREnhanced.createGroup(`session-${sessionId}`, {
                sessionId,
                documentId: sessionRequest.documentId,
                documentName: documentData.name,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId,
                createdBy: user.id,
                createdAt: now
            });
            // Add session creator to the SignalR group
            await signalREnhanced.addToGroup(`session-${sessionId}`, user.id);
            logger_1.logger.info("SignalR group created for collaboration session", { sessionId, groupName: `session-${sessionId}` });
        }
        catch (signalRError) {
            logger_1.logger.warn("Failed to create SignalR group for session", { signalRError, sessionId });
        }
        // Publish Event Grid event for collaboration session created
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Document.CollaborationSessionCreated',
            subject: `documents/${sessionRequest.documentId}/collaboration/session-created`,
            data: {
                sessionId,
                documentId: sessionRequest.documentId,
                documentName: documentData.name,
                createdBy: user.id,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId,
                maxParticipants: sessionRequest.maxParticipants,
                permissions: sessionRequest.permissions,
                createdAt: now,
                correlationId
            }
        });
        // Send message to Service Bus for collaboration workflow
        await serviceBusService.sendToTopic('document-collaboration', {
            body: {
                sessionId,
                documentId: sessionRequest.documentId,
                action: 'session-created',
                createdBy: user.id,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId,
                maxParticipants: sessionRequest.maxParticipants,
                correlationId,
                timestamp: now
            },
            messageId: `collab-session-${sessionId}-${Date.now()}`,
            correlationId,
            subject: 'collaboration.session.created'
        });
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "collaboration_session_created",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: sessionRequest.documentId,
            timestamp: now,
            details: {
                sessionId,
                sessionName: session.sessionName,
                documentName: documentData.name,
                maxParticipants: session.maxParticipants
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'CollaborationSessionCreated',
            aggregateId: sessionId,
            aggregateType: 'CollaborationSession',
            version: 1,
            data: {
                session,
                document: documentData,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        // Emit cache invalidation event for document-related caches
        await event_driven_cache_1.eventDrivenCache.processInvalidationEvent({
            eventType: 'document.updated',
            resourceId: sessionRequest.documentId,
            organizationId: documentData.organizationId,
            userId: user.id,
            timestamp: new Date(),
            metadata: { sessionId, action: 'session_created' }
        });
        logger_1.logger.info("Collaboration session created successfully", {
            correlationId,
            sessionId,
            documentId: sessionRequest.documentId,
            documentName: documentData.name,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                session: {
                    id: sessionId,
                    documentId: sessionRequest.documentId,
                    sessionName: session.sessionName,
                    status: SessionStatus.ACTIVE,
                    participants: session.participants,
                    permissions: session.permissions,
                    currentRevision: 0,
                    createdAt: now
                },
                message: "Collaboration session created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create collaboration session failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Join collaboration session handler
 */
async function joinCollaborationSession(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Join collaboration session started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = joinSessionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const joinRequest = value;
        // Get session from Redis first, then database
        let session = await getSessionFromRedis(joinRequest.sessionId);
        if (!session) {
            const sessionDoc = await database_1.db.readItem('collaboration-sessions', joinRequest.sessionId, joinRequest.sessionId);
            if (!sessionDoc) {
                return (0, cors_1.addCorsHeaders)({
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Collaboration session not found" }
                }, request);
            }
            session = sessionDoc;
        }
        // Check if session is active
        if (session.status !== SessionStatus.ACTIVE) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Collaboration session is not active" }
            }, request);
        }
        // Check if user is already in session
        const existingParticipant = session.participants.find((p) => p.userId === user.id);
        if (existingParticipant) {
            // Update participant as active
            existingParticipant.isActive = true;
            existingParticipant.displayName = joinRequest.displayName || existingParticipant.displayName;
        }
        else {
            // Check participant limit
            const activeParticipants = session.participants.filter((p) => p.isActive);
            if (activeParticipants.length >= session.maxParticipants) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Session has reached maximum participants" }
                }, request);
            }
            // Add new participant
            session.participants.push({
                userId: user.id,
                displayName: joinRequest.displayName || user.name || user.email,
                joinedAt: new Date().toISOString(),
                isActive: true
            });
        }
        // Update session
        session.lastActivity = new Date().toISOString();
        await database_1.db.updateItem('collaboration-sessions', session);
        await redis_1.redis.setex(`session:${joinRequest.sessionId}`, 3600, JSON.stringify(session));
        // Add user to SignalR group for real-time collaboration
        try {
            await signalREnhanced.addToGroup(`session-${joinRequest.sessionId}`, user.id);
            logger_1.logger.info("User added to SignalR collaboration group", {
                sessionId: joinRequest.sessionId,
                userId: user.id,
                groupName: `session-${joinRequest.sessionId}`
            });
        }
        catch (signalRError) {
            logger_1.logger.warn("Failed to add user to SignalR group", { signalRError, sessionId: joinRequest.sessionId, userId: user.id });
        }
        // Get current document content and revision with database fallback
        const currentContent = await redis_1.redis.getDocumentContent(session.documentId, true) || '';
        const currentRevision = parseInt(await redis_1.redis.get(`document:${session.documentId}:revision`) || '0');
        // Broadcast participant joined event
        await broadcastToSession(joinRequest.sessionId, {
            type: 'participant_joined',
            participant: {
                userId: user.id,
                displayName: joinRequest.displayName || user.name || user.email
            },
            timestamp: new Date().toISOString()
        });
        // Emit cache invalidation event for session-related caches
        await event_driven_cache_1.eventDrivenCache.processInvalidationEvent({
            eventType: 'user.updated',
            resourceId: user.id,
            organizationId: session.organizationId,
            userId: user.id,
            timestamp: new Date(),
            metadata: { sessionId: joinRequest.sessionId, action: 'session_joined' }
        });
        logger_1.logger.info("User joined collaboration session successfully", {
            correlationId,
            sessionId: joinRequest.sessionId,
            userId: user.id,
            participantCount: session.participants.filter((p) => p.isActive).length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                session: {
                    id: session.id,
                    documentId: session.documentId,
                    sessionName: session.sessionName,
                    participants: session.participants.filter((p) => p.isActive),
                    permissions: session.permissions,
                    currentRevision,
                    currentContent
                },
                message: "Joined collaboration session successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Join collaboration session failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        // Check if user is the owner
        if (document.createdBy === userId) {
            return true;
        }
        // Check organization membership
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
async function getActiveSessionForDocument(documentId) {
    try {
        const query = 'SELECT * FROM c WHERE c.documentId = @docId AND c.status = @status';
        const sessions = await database_1.db.queryItems('collaboration-sessions', query, [documentId, SessionStatus.ACTIVE]);
        return sessions.length > 0 ? sessions[0] : null;
    }
    catch (error) {
        logger_1.logger.error('Failed to get active session for document', { error, documentId });
        return null;
    }
}
async function getSessionFromRedis(sessionId) {
    try {
        // Use enhanced Redis service with database fallback
        return await redis_1.redis.getSession(sessionId, true);
    }
    catch (error) {
        logger_1.logger.error('Failed to get session from Redis with fallback', { error, sessionId });
        return null;
    }
}
async function broadcastToSession(sessionId, message) {
    try {
        // Production implementation using SignalR Enhanced Service
        const messageId = (0, uuid_1.v4)();
        const fullMessage = {
            id: messageId,
            ...message,
            timestamp: new Date().toISOString()
        };
        // Broadcast to SignalR group for real-time updates
        await signalREnhanced.sendToGroup(`session-${sessionId}`, 'sessionUpdate', fullMessage);
        // Store message in Redis for message history and offline users
        await redis_1.redis.lpush(`session:${sessionId}:messages`, JSON.stringify(fullMessage));
        // Keep only last 100 messages
        await redis_1.redis.ltrim(`session:${sessionId}:messages`, 0, 99);
        // Update session activity timestamp in Redis
        await redis_1.redis.setex(`session:${sessionId}:lastActivity`, 3600, new Date().toISOString());
        logger_1.logger.debug('Message broadcasted to session', { sessionId, messageType: message.type });
    }
    catch (error) {
        logger_1.logger.error('Failed to broadcast to session', { error, sessionId, messageType: message.type });
    }
}
// Register functions
functions_1.app.http('collaboration-session-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'collaboration/sessions',
    handler: createCollaborationSession
});
functions_1.app.http('collaboration-session-join', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'collaboration/sessions/join',
    handler: joinCollaborationSession
});
//# sourceMappingURL=real-time-collaboration.js.map