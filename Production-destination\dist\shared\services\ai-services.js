"use strict";
/**
 * AI Services Integration
 * Production-ready implementations for DeepSeek R1, Llama, and other AI services
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiServices = exports.AIServiceManager = exports.LlamaService = exports.DeepSeekR1Service = void 0;
const logger_1 = require("../utils/logger");
// DeepSeek R1 Service - Advanced Reasoning AI
class DeepSeekR1Service {
    constructor() {
        this.config = {
            endpoint: process.env.AI_DEEPSEEK_R1_ENDPOINT || '',
            key: process.env.AI_DEEPSEEK_R1_KEY || '',
            model: process.env.AI_DEEPSEEK_R1_DEFAULT_MODEL || 'deepseek-r1-chat',
            enabled: process.env.AI_DEEPSEEK_R1_ENABLED === 'true'
        };
    }
    async initialize() {
        if (!this.config.enabled) {
            logger_1.logger.warn('DeepSeek R1 service is disabled');
            return;
        }
        try {
            // Initialize Azure OpenAI client for DeepSeek R1
            const { OpenAIClient, AzureKeyCredential } = require('@azure/openai');
            this.client = new OpenAIClient(this.config.endpoint, new AzureKeyCredential(this.config.key));
            logger_1.logger.info('DeepSeek R1 service initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize DeepSeek R1 service', { error });
            throw error;
        }
    }
    async reason(request) {
        if (!this.client) {
            await this.initialize();
        }
        const startTime = Date.now();
        try {
            const messages = [
                {
                    role: 'system',
                    content: request.systemPrompt || 'You are DeepSeek R1, an advanced reasoning AI. Think step by step and provide detailed reasoning for your conclusions.'
                },
                {
                    role: 'user',
                    content: request.prompt
                }
            ];
            // Add context if provided
            if (request.context && request.context.length > 0) {
                messages.splice(1, 0, {
                    role: 'system',
                    content: `Context information:\n${request.context.join('\n\n')}`
                });
            }
            const response = await this.client.getChatCompletions(this.config.model, messages, {
                maxTokens: request.maxTokens || 4000,
                temperature: request.temperature || 0.7,
                topP: request.topP || 0.9
            });
            const choice = response.choices[0];
            const processingTime = Date.now() - startTime;
            return {
                content: choice.message.content,
                reasoning: this.extractReasoning(choice.message.content),
                confidence: this.calculateConfidence(choice),
                tokensUsed: response.usage?.totalTokens || 0,
                model: this.config.model,
                processingTime
            };
        }
        catch (error) {
            logger_1.logger.error('DeepSeek R1 reasoning failed', { error, request: request.prompt });
            throw error;
        }
    }
    async generateEmbeddings(request) {
        if (!this.client) {
            await this.initialize();
        }
        try {
            const embeddingModel = request.model || process.env.AI_DEEPSEEK_R1_DEFAULT_EMBEDDING_MODEL || 'deepseek-embedding';
            const response = await this.client.getEmbeddings(embeddingModel, [request.text]);
            return {
                embedding: response.data[0].embedding,
                dimensions: response.data[0].embedding.length,
                model: embeddingModel,
                tokensUsed: response.usage?.totalTokens || 0
            };
        }
        catch (error) {
            logger_1.logger.error('DeepSeek R1 embedding generation failed', { error });
            throw error;
        }
    }
    extractReasoning(content) {
        // Extract reasoning steps from DeepSeek R1 response
        const reasoningPatterns = [
            /Let me think step by step:(.*?)(?=\n\n|$)/s,
            /Reasoning:(.*?)(?=\n\n|$)/s,
            /Analysis:(.*?)(?=\n\n|$)/s
        ];
        for (const pattern of reasoningPatterns) {
            const match = content.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }
        return '';
    }
    calculateConfidence(choice) {
        // Calculate confidence based on response characteristics
        if (choice.finishReason === 'stop') {
            return 0.9;
        }
        else if (choice.finishReason === 'length') {
            return 0.7;
        }
        return 0.5;
    }
}
exports.DeepSeekR1Service = DeepSeekR1Service;
// Llama Service - Content Generation AI
class LlamaService {
    constructor() {
        this.config = {
            endpoint: process.env.AI_LLAMA_ENDPOINT || '',
            key: process.env.AI_LLAMA_KEY || '',
            model: process.env.AI_LLAMA_DEFAULT_MODEL || 'llama-3-3-70b-instruct',
            enabled: process.env.AI_LLAMA_ENABLED === 'true'
        };
    }
    async initialize() {
        if (!this.config.enabled) {
            logger_1.logger.warn('Llama service is disabled');
            return;
        }
        try {
            const { OpenAIClient, AzureKeyCredential } = require('@azure/openai');
            this.client = new OpenAIClient(this.config.endpoint, new AzureKeyCredential(this.config.key));
            logger_1.logger.info('Llama service initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Llama service', { error });
            throw error;
        }
    }
    async generateContent(request) {
        if (!this.client) {
            await this.initialize();
        }
        const startTime = Date.now();
        try {
            const messages = [
                {
                    role: 'system',
                    content: request.systemPrompt || 'You are Llama, a helpful AI assistant specialized in content generation. Create high-quality, engaging content.'
                },
                {
                    role: 'user',
                    content: request.prompt
                }
            ];
            const response = await this.client.getChatCompletions(this.config.model, messages, {
                maxTokens: request.maxTokens || 2000,
                temperature: request.temperature || 0.8,
                topP: request.topP || 0.95
            });
            const choice = response.choices[0];
            const processingTime = Date.now() - startTime;
            return {
                content: choice.message.content,
                confidence: this.calculateContentQuality(choice.message.content),
                tokensUsed: response.usage?.totalTokens || 0,
                model: this.config.model,
                processingTime
            };
        }
        catch (error) {
            logger_1.logger.error('Llama content generation failed', { error, request: request.prompt });
            throw error;
        }
    }
    calculateContentQuality(content) {
        // Simple content quality assessment
        const wordCount = content.split(/\s+/).length;
        const sentenceCount = content.split(/[.!?]+/).length;
        const avgWordsPerSentence = wordCount / sentenceCount;
        // Quality factors
        let quality = 0.5;
        if (wordCount > 50)
            quality += 0.1;
        if (avgWordsPerSentence > 10 && avgWordsPerSentence < 25)
            quality += 0.2;
        if (content.includes('\n'))
            quality += 0.1; // Has structure
        if (!/(.)\1{3,}/.test(content))
            quality += 0.1; // No repetitive patterns
        return Math.min(quality, 1.0);
    }
}
exports.LlamaService = LlamaService;
// AI Service Manager - Orchestrates all AI services
class AIServiceManager {
    constructor() {
        this.initialized = false;
        this.deepSeekR1 = new DeepSeekR1Service();
        this.llama = new LlamaService();
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            await Promise.all([
                this.deepSeekR1.initialize(),
                this.llama.initialize()
            ]);
            this.initialized = true;
            logger_1.logger.info('AI Service Manager initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize AI Service Manager', { error });
            throw error;
        }
    }
    async reason(prompt, context, options) {
        await this.initialize();
        return this.deepSeekR1.reason({
            prompt,
            context,
            ...options
        });
    }
    async generateContent(prompt, options) {
        await this.initialize();
        return this.llama.generateContent({
            prompt,
            ...options
        });
    }
    async generateEmbeddings(text, model) {
        await this.initialize();
        return this.deepSeekR1.generateEmbeddings({ text, model });
    }
    getDeepSeekR1() {
        return this.deepSeekR1;
    }
    getLlama() {
        return this.llama;
    }
}
exports.AIServiceManager = AIServiceManager;
// Export singleton instance
exports.aiServices = new AIServiceManager();
//# sourceMappingURL=ai-services.js.map