"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.listAuditLogs = listAuditLogs;
exports.exportAuditLogs = exportAuditLogs;
exports.getAuditStatistics = getAuditStatistics;
/**
 * Audit Log Function
 * Handles audit trail and compliance logging
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Audit event types enum
var AuditEventType;
(function (AuditEventType) {
    AuditEventType["USER_LOGIN"] = "USER_LOGIN";
    AuditEventType["USER_LOGOUT"] = "USER_LOGOUT";
    AuditEventType["DOCUMENT_CREATED"] = "DOCUMENT_CREATED";
    AuditEventType["DOCUMENT_VIEWED"] = "DOCUMENT_VIEWED";
    AuditEventType["DOCUMENT_UPDATED"] = "DOCUMENT_UPDATED";
    AuditEventType["DOCUMENT_DELETED"] = "DOCUMENT_DELETED";
    AuditEventType["DOCUMENT_SHARED"] = "DOCUMENT_SHARED";
    AuditEventType["PERMISSION_GRANTED"] = "PERMISSION_GRANTED";
    AuditEventType["PERMISSION_REVOKED"] = "PERMISSION_REVOKED";
    AuditEventType["WORKFLOW_EXECUTED"] = "WORKFLOW_EXECUTED";
    AuditEventType["DATA_EXPORTED"] = "DATA_EXPORTED";
    AuditEventType["SETTINGS_CHANGED"] = "SETTINGS_CHANGED";
    AuditEventType["SECURITY_EVENT"] = "SECURITY_EVENT";
})(AuditEventType || (AuditEventType = {}));
// Validation schemas
const listAuditLogsSchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(100).default(20),
    eventType: joi_1.default.string().valid(...Object.values(AuditEventType)).optional(),
    userId: joi_1.default.string().uuid().optional(),
    resourceType: joi_1.default.string().optional(),
    resourceId: joi_1.default.string().uuid().optional(),
    organizationId: joi_1.default.string().uuid().optional(),
    dateFrom: joi_1.default.date().iso().optional(),
    dateTo: joi_1.default.date().iso().optional(),
    ipAddress: joi_1.default.string().ip().optional(),
    search: joi_1.default.string().max(100).optional()
});
const exportAuditLogsSchema = joi_1.default.object({
    format: joi_1.default.string().valid('csv', 'json', 'pdf').default('csv'),
    eventType: joi_1.default.string().valid(...Object.values(AuditEventType)).optional(),
    userId: joi_1.default.string().uuid().optional(),
    organizationId: joi_1.default.string().uuid().optional(),
    dateFrom: joi_1.default.date().iso().required(),
    dateTo: joi_1.default.date().iso().required(),
    includeDetails: joi_1.default.boolean().default(true)
});
/**
 * List audit logs handler
 */
async function listAuditLogs(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("List audit logs started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check if user has audit access (admin only)
        if (!user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied. Admin privileges required." }
            }, request);
        }
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listAuditLogsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, eventType, userId, resourceType, resourceId, organizationId, dateFrom, dateTo, ipAddress, search } = value;
        // Build query
        let queryText = 'SELECT * FROM c WHERE 1=1';
        const parameters = [];
        // Add tenant isolation
        if (user.tenantId) {
            queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
            parameters.push(user.tenantId);
        }
        // Add filters
        if (eventType) {
            queryText += ' AND c.eventType = @eventType';
            parameters.push(eventType);
        }
        if (userId) {
            queryText += ' AND c.userId = @userId';
            parameters.push(userId);
        }
        if (resourceType) {
            queryText += ' AND c.resourceType = @resourceType';
            parameters.push(resourceType);
        }
        if (resourceId) {
            queryText += ' AND c.resourceId = @resourceId';
            parameters.push(resourceId);
        }
        if (organizationId) {
            queryText += ' AND c.organizationId = @organizationId';
            parameters.push(organizationId);
        }
        if (dateFrom) {
            queryText += ' AND c.timestamp >= @dateFrom';
            parameters.push(dateFrom);
        }
        if (dateTo) {
            queryText += ' AND c.timestamp <= @dateTo';
            parameters.push(dateTo);
        }
        if (ipAddress) {
            queryText += ' AND c.ipAddress = @ipAddress';
            parameters.push(ipAddress);
        }
        if (search) {
            queryText += ' AND (CONTAINS(LOWER(c.description), LOWER(@search)) OR CONTAINS(LOWER(c.details), LOWER(@search)))';
            parameters.push(search);
        }
        // Add ordering
        queryText += ' ORDER BY c.timestamp DESC';
        // Get total count
        const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
        const countResult = await database_1.db.queryItems('audit-logs', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;
        // Execute query
        const auditLogs = await database_1.db.queryItems('audit-logs', paginatedQuery, parameters);
        // Enrich audit logs with user information
        const enrichedLogs = await Promise.all(auditLogs.map(async (log) => {
            let userName = 'System';
            let userEmail = '';
            if (log.userId) {
                try {
                    const logUser = await database_1.db.readItem('users', log.userId, log.userId);
                    if (logUser) {
                        userName = logUser.name || logUser.email || 'Unknown User';
                        userEmail = logUser.email || '';
                    }
                }
                catch (error) {
                    // User might not exist anymore
                }
            }
            return {
                ...log,
                userName,
                userEmail
            };
        }));
        logger_1.logger.info("Audit logs listed successfully", {
            correlationId,
            userId: user.id,
            count: auditLogs.length,
            page,
            limit,
            eventType,
            organizationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                items: enrichedLogs,
                total,
                page,
                limit,
                hasMore: page * limit < total,
                filters: {
                    eventType,
                    userId,
                    resourceType,
                    resourceId,
                    organizationId,
                    dateFrom,
                    dateTo
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List audit logs failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Export audit logs handler
 */
async function exportAuditLogs(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Export audit logs started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check if user has audit access (admin only)
        if (!user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied. Admin privileges required." }
            }, request);
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = exportAuditLogsSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { format, eventType, userId, organizationId, dateFrom, dateTo, includeDetails } = value;
        // Build query for export
        let queryText = 'SELECT * FROM c WHERE c.timestamp >= @dateFrom AND c.timestamp <= @dateTo';
        const parameters = [dateFrom, dateTo];
        // Add tenant isolation
        if (user.tenantId) {
            queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
            parameters.push(user.tenantId);
        }
        // Add filters
        if (eventType) {
            queryText += ' AND c.eventType = @eventType';
            parameters.push(eventType);
        }
        if (userId) {
            queryText += ' AND c.userId = @userId';
            parameters.push(userId);
        }
        if (organizationId) {
            queryText += ' AND c.organizationId = @organizationId';
            parameters.push(organizationId);
        }
        // Add ordering
        queryText += ' ORDER BY c.timestamp DESC';
        // Execute query (limit to 10000 records for export)
        const limitedQuery = `${queryText} OFFSET 0 LIMIT 10000`;
        const auditLogs = await database_1.db.queryItems('audit-logs', limitedQuery, parameters);
        // Generate export data based on format
        let exportData;
        let contentType;
        let filename;
        switch (format) {
            case 'csv':
                exportData = generateCSV(auditLogs, includeDetails);
                contentType = 'text/csv';
                filename = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
                break;
            case 'json':
                exportData = JSON.stringify(auditLogs, null, 2);
                contentType = 'application/json';
                filename = `audit-logs-${new Date().toISOString().split('T')[0]}.json`;
                break;
            case 'pdf':
                // Production: Generate actual PDF using PDFKit
                exportData = await generatePDFContent(auditLogs, includeDetails);
                contentType = 'application/pdf';
                filename = `audit-logs-${new Date().toISOString().split('T')[0]}.pdf`;
                break;
            default:
                exportData = generateCSV(auditLogs, includeDetails);
                contentType = 'text/csv';
                filename = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
        }
        // Log export activity
        logger_1.logger.info("Audit logs exported", {
            correlationId,
            userId: user.id,
            format,
            recordCount: auditLogs.length,
            dateFrom,
            dateTo
        });
        // Create audit record for the export itself
        await database_1.db.createItem('audit-logs', {
            id: (0, uuid_1.v4)(),
            eventType: AuditEventType.DATA_EXPORTED,
            userId: user.id,
            description: `Audit logs exported in ${format} format`,
            details: {
                format,
                recordCount: auditLogs.length,
                dateFrom,
                dateTo,
                filters: { eventType, userId, organizationId }
            },
            timestamp: new Date().toISOString(),
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            organizationId: user.tenantId,
            tenantId: user.tenantId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: {
                'Content-Type': contentType,
                'Content-Disposition': `attachment; filename="${filename}"`
            },
            body: exportData
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Export audit logs failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Generate CSV content
 */
function generateCSV(auditLogs, includeDetails) {
    const headers = ['Timestamp', 'Event Type', 'User ID', 'Description', 'IP Address', 'User Agent'];
    if (includeDetails) {
        headers.push('Details');
    }
    const rows = [headers.join(',')];
    auditLogs.forEach(log => {
        const row = [
            log.timestamp,
            log.eventType,
            log.userId || '',
            `"${(log.description || '').replace(/"/g, '""')}"`,
            log.ipAddress || '',
            `"${(log.userAgent || '').replace(/"/g, '""')}"`
        ];
        if (includeDetails) {
            row.push(`"${JSON.stringify(log.details || {}).replace(/"/g, '""')}"`);
        }
        rows.push(row.join(','));
    });
    return rows.join('\n');
}
/**
 * Generate PDF content (simplified)
 */
async function generatePDFContent(auditLogs, includeDetails) {
    const PDFDocument = require('pdfkit');
    return new Promise((resolve, reject) => {
        try {
            const doc = new PDFDocument({
                size: 'A4',
                margin: 50,
                info: {
                    Title: 'Audit Log Report',
                    Author: 'HEPZ Platform',
                    Subject: 'System Audit Logs',
                    Creator: 'HEPZ Audit Service'
                }
            });
            const chunks = [];
            doc.on('data', (chunk) => chunks.push(chunk));
            doc.on('end', () => resolve(Buffer.concat(chunks)));
            doc.on('error', reject);
            // Header
            doc.fontSize(20)
                .font('Helvetica-Bold')
                .text('Audit Log Report', { align: 'center' });
            doc.moveDown();
            doc.fontSize(12)
                .font('Helvetica')
                .text(`Generated: ${new Date().toISOString()}`, { align: 'center' })
                .text(`Total Records: ${auditLogs.length}`, { align: 'center' });
            doc.moveDown(2);
            // Table headers
            doc.fontSize(10).font('Helvetica-Bold');
            const tableTop = doc.y;
            doc.text('Timestamp', 50, tableTop);
            doc.text('Event Type', 150, tableTop);
            doc.text('User', 250, tableTop);
            doc.text('Description', 320, tableTop);
            if (includeDetails) {
                doc.text('Details', 450, tableTop);
            }
            // Draw header line
            doc.moveTo(50, tableTop + 15).lineTo(550, tableTop + 15).stroke();
            // Table content
            doc.font('Helvetica');
            let currentY = tableTop + 25;
            auditLogs.forEach((log) => {
                // Check if we need a new page
                if (currentY > 700) {
                    doc.addPage();
                    currentY = 50;
                }
                const rowHeight = includeDetails && log.details ? 40 : 20;
                // Timestamp
                doc.text(new Date(log.timestamp).toLocaleString(), 50, currentY, {
                    width: 95, height: rowHeight, ellipsis: true
                });
                // Event Type
                doc.text(log.eventType || 'N/A', 150, currentY, {
                    width: 95, height: rowHeight, ellipsis: true
                });
                // User
                doc.text(log.userId || 'System', 250, currentY, {
                    width: 65, height: rowHeight, ellipsis: true
                });
                // Description
                doc.text(log.description || 'N/A', 320, currentY, {
                    width: 125, height: rowHeight, ellipsis: true
                });
                // Details (if included)
                if (includeDetails && log.details) {
                    const detailsText = typeof log.details === 'object'
                        ? JSON.stringify(log.details).substring(0, 100) + '...'
                        : String(log.details).substring(0, 100);
                    doc.text(detailsText, 450, currentY, {
                        width: 95, height: rowHeight, ellipsis: true
                    });
                }
                currentY += rowHeight;
            });
            // Footer
            doc.fontSize(8)
                .text(`Report generated by HEPZ Platform on ${new Date().toLocaleString()}`, 50, doc.page.height - 50, { align: 'center' });
            doc.end();
        }
        catch (error) {
            reject(error);
        }
    });
}
/**
 * Get audit statistics handler
 */
async function getAuditStatistics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get audit statistics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check if user has audit access (admin only)
        if (!user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied. Admin privileges required." }
            }, request);
        }
        // Get statistics for the last 30 days
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
        // Total events
        const totalQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.timestamp >= @date';
        const totalResult = await database_1.db.queryItems('audit-logs', totalQuery, [thirtyDaysAgo]);
        const totalEvents = Number(totalResult[0]) || 0;
        // Events by type
        const eventTypeQuery = 'SELECT c.eventType, COUNT(1) as count FROM c WHERE c.timestamp >= @date GROUP BY c.eventType';
        const eventTypeResult = await database_1.db.queryItems('audit-logs', eventTypeQuery, [thirtyDaysAgo]);
        // Top users
        const topUsersQuery = 'SELECT c.userId, COUNT(1) as count FROM c WHERE c.timestamp >= @date AND c.userId != null GROUP BY c.userId ORDER BY COUNT(1) DESC OFFSET 0 LIMIT 10';
        const topUsersResult = await database_1.db.queryItems('audit-logs', topUsersQuery, [thirtyDaysAgo]);
        // Security events
        const securityQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.timestamp >= @date AND c.eventType = @eventType';
        const securityResult = await database_1.db.queryItems('audit-logs', securityQuery, [thirtyDaysAgo, AuditEventType.SECURITY_EVENT]);
        const securityEvents = Number(securityResult[0]) || 0;
        const statistics = {
            period: '30 days',
            totalEvents,
            securityEvents,
            eventsByType: eventTypeResult,
            topUsers: topUsersResult,
            generatedAt: new Date().toISOString()
        };
        logger_1.logger.info("Audit statistics retrieved successfully", {
            correlationId,
            userId: user.id,
            totalEvents,
            securityEvents
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: statistics
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get audit statistics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('audit-logs-list', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'audit/logs',
    handler: listAuditLogs
});
functions_1.app.http('audit-logs-export', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'audit/logs/export',
    handler: exportAuditLogs
});
functions_1.app.http('audit-statistics', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'audit/statistics',
    handler: getAuditStatistics
});
//# sourceMappingURL=audit-log.js.map