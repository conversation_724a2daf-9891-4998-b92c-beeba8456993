#!/bin/bash

# Azure Enhanced Services Setup Script
# This script configures Event Grid, Redis, and Service Bus resources for the enhanced functions

# Configuration variables
RESOURCE_GROUP="docucontext"
LOCATION="eastus"
SUBSCRIPTION_ID=$(az account show --query id -o tsv)

# Service Bus Configuration
SERVICE_BUS_NAMESPACE="hepzbackend"

# Redis Configuration  
REDIS_NAME="hepzbackend"

# Event Grid Configuration
EVENT_GRID_TOPIC="hepz-events"

# Function App Configuration
FUNCTION_APP_NAME="hepzlogic"

echo "🚀 Starting Azure Enhanced Services Configuration..."
echo "Resource Group: $RESOURCE_GROUP"
echo "Location: $LOCATION"
echo "Service Bus Namespace: $SERVICE_BUS_NAMESPACE"
echo "Redis Name: $REDIS_NAME"
echo "Event Grid Topic: $EVENT_GRID_TOPIC"
echo "Function App: $FUNCTION_APP_NAME"

# 1. Configure Service Bus Queues and Topics
echo "📨 Configuring Service Bus queues and topics..."

# Create analytics-events queue (if not exists)
echo "Creating analytics-events queue..."
az servicebus queue create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name analytics-events \
  --max-size 1024 \
  --default-message-time-to-live P14D \
  --enable-dead-lettering-on-message-expiration true \
  --max-delivery-count 10

# Create user-events topic for user-related events
echo "Creating user-events topic..."
az servicebus topic create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name user-events \
  --max-size 1024 \
  --default-message-time-to-live P14D \
  --enable-duplicate-detection true

# Create subscription for user events
echo "Creating user-events subscription..."
az servicebus topic subscription create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --topic-name user-events \
  --name user-processor \
  --max-delivery-count 10 \
  --enable-dead-lettering-on-message-expiration true

# Create project-events topic for project-related events
echo "Creating project-events topic..."
az servicebus topic create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name project-events \
  --max-size 1024 \
  --default-message-time-to-live P14D \
  --enable-duplicate-detection true

# Create subscription for project events
echo "Creating project-events subscription..."
az servicebus topic subscription create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --topic-name project-events \
  --name project-processor \
  --max-delivery-count 10 \
  --enable-dead-lettering-on-message-expiration true

# Create organization-events topic for organization-related events
echo "Creating organization-events topic..."
az servicebus topic create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name organization-events \
  --max-size 1024 \
  --default-message-time-to-live P14D \
  --enable-duplicate-detection true

# Create subscription for organization events
echo "Creating organization-events subscription..."
az servicebus topic subscription create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --topic-name organization-events \
  --name organization-processor \
  --max-delivery-count 10 \
  --enable-dead-lettering-on-message-expiration true

# Create thumbnail-processing queue for thumbnail generation
echo "Creating thumbnail-processing queue..."
az servicebus queue create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name thumbnail-processing \
  --max-size 1024 \
  --default-message-time-to-live P7D \
  --enable-dead-lettering-on-message-expiration true \
  --max-delivery-count 5

# Create data-migration queue for migration operations
echo "Creating data-migration queue..."
az servicebus queue create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name data-migration \
  --max-size 2048 \
  --default-message-time-to-live P30D \
  --enable-dead-lettering-on-message-expiration true \
  --max-delivery-count 3

# Create enterprise-integration queue for external system sync
echo "Creating enterprise-integration queue..."
az servicebus queue create \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name enterprise-integration \
  --max-size 1024 \
  --default-message-time-to-live P14D \
  --enable-dead-lettering-on-message-expiration true \
  --max-delivery-count 5

echo "✅ Service Bus configuration completed!"

# 2. Configure Event Grid Topic
echo "⚡ Configuring Event Grid..."

# Create Event Grid Topic
echo "Creating Event Grid topic..."
az eventgrid topic create \
  --resource-group $RESOURCE_GROUP \
  --name $EVENT_GRID_TOPIC \
  --location $LOCATION \
  --input-schema EventGridSchema

# Get Event Grid Topic endpoint and key
EVENT_GRID_ENDPOINT=$(az eventgrid topic show \
  --resource-group $RESOURCE_GROUP \
  --name $EVENT_GRID_TOPIC \
  --query endpoint -o tsv)

EVENT_GRID_KEY=$(az eventgrid topic key list \
  --resource-group $RESOURCE_GROUP \
  --name $EVENT_GRID_TOPIC \
  --query key1 -o tsv)

echo "Event Grid Topic Endpoint: $EVENT_GRID_ENDPOINT"
echo "Event Grid Topic Key: [HIDDEN]"

echo "✅ Event Grid configuration completed!"

# Create Event Grid Subscriptions for Function App
echo "Creating Event Grid subscriptions..."

# Create subscription for user events
az eventgrid event-subscription create \
  --source-resource-id "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC" \
  --name user-events-subscription \
  --endpoint-type azurefunction \
  --endpoint "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FUNCTION_APP_NAME/functions/event-grid-handlers" \
  --included-event-types "Microsoft.EventGrid.SubscriptionValidationEvent" "user.created" "user.updated" "user.deleted" \
  --max-delivery-attempts 10 \
  --event-ttl 1440

# Create subscription for project events
az eventgrid event-subscription create \
  --source-resource-id "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC" \
  --name project-events-subscription \
  --endpoint-type azurefunction \
  --endpoint "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FUNCTION_APP_NAME/functions/event-grid-handlers" \
  --included-event-types "Microsoft.EventGrid.SubscriptionValidationEvent" "project.created" "project.updated" "project.deleted" \
  --max-delivery-attempts 10 \
  --event-ttl 1440

# Create subscription for organization events
az eventgrid event-subscription create \
  --source-resource-id "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC" \
  --name organization-events-subscription \
  --endpoint-type azurefunction \
  --endpoint "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FUNCTION_APP_NAME/functions/event-grid-handlers" \
  --included-event-types "Microsoft.EventGrid.SubscriptionValidationEvent" "organization.created" "organization.updated" "organization.deleted" \
  --max-delivery-attempts 10 \
  --event-ttl 1440

echo "✅ Event Grid subscriptions created!"

# 3. Configure Redis Cache (if not already configured)
echo "🔴 Configuring Redis Cache..."

# Check if Redis cache exists
REDIS_EXISTS=$(az redis show --resource-group $RESOURCE_GROUP --name $REDIS_NAME --query name -o tsv 2>/dev/null || echo "")

if [ -z "$REDIS_EXISTS" ]; then
  echo "Creating Redis cache..."
  az redis create \
    --resource-group $RESOURCE_GROUP \
    --name $REDIS_NAME \
    --location $LOCATION \
    --sku Standard \
    --vm-size c1 \
    --enable-non-ssl-port false \
    --minimum-tls-version 1.2
else
  echo "Redis cache already exists: $REDIS_NAME"
fi

# Get Redis connection details
REDIS_HOSTNAME=$(az redis show \
  --resource-group $RESOURCE_GROUP \
  --name $REDIS_NAME \
  --query hostName -o tsv)

REDIS_PORT=$(az redis show \
  --resource-group $RESOURCE_GROUP \
  --name $REDIS_NAME \
  --query sslPort -o tsv)

REDIS_KEY=$(az redis list-keys \
  --resource-group $RESOURCE_GROUP \
  --name $REDIS_NAME \
  --query primaryKey -o tsv)

echo "Redis Hostname: $REDIS_HOSTNAME"
echo "Redis Port: $REDIS_PORT"
echo "Redis Key: [HIDDEN]"

echo "✅ Redis configuration completed!"

# 4. Update Function App Configuration
echo "⚙️ Updating Function App configuration..."

# Get Service Bus connection string
SERVICE_BUS_CONNECTION=$(az servicebus namespace authorization-rule keys list \
  --resource-group $RESOURCE_GROUP \
  --namespace-name $SERVICE_BUS_NAMESPACE \
  --name RootManageSharedAccessKey \
  --query primaryConnectionString -o tsv)

# Update Function App settings
echo "Updating Function App settings..."
az functionapp config appsettings set \
  --resource-group $RESOURCE_GROUP \
  --name $FUNCTION_APP_NAME \
  --settings \
    "EVENT_GRID_TOPIC_ENDPOINT=$EVENT_GRID_ENDPOINT" \
    "EVENT_GRID_TOPIC_KEY=$EVENT_GRID_KEY" \
    "REDIS_HOSTNAME=$REDIS_HOSTNAME" \
    "REDIS_PORT=$REDIS_PORT" \
    "REDIS_KEY=$REDIS_KEY" \
    "REDIS_CONNECTION_STRING=$REDIS_HOSTNAME:$REDIS_PORT,password=$REDIS_KEY,ssl=True,abortConnect=False" \
    "SERVICE_BUS_CONNECTION_STRING=$SERVICE_BUS_CONNECTION"

echo "✅ Function App configuration updated!"

echo ""
echo "🎉 Azure Enhanced Services Configuration Complete!"
echo ""
echo "📋 Configuration Summary:"
echo "========================"
echo "Service Bus Namespace: $SERVICE_BUS_NAMESPACE"
echo "Event Grid Topic: $EVENT_GRID_TOPIC"
echo "Redis Cache: $REDIS_NAME"
echo "Function App: $FUNCTION_APP_NAME"
echo ""
echo "📨 Service Bus Queues Created:"
echo "- analytics-events"
echo "- thumbnail-processing"
echo "- data-migration"
echo "- enterprise-integration"
echo ""
echo "📨 Service Bus Topics Created:"
echo "- user-events (subscription: user-processor)"
echo "- project-events (subscription: project-processor)"
echo "- organization-events (subscription: organization-processor)"
echo ""
echo "⚡ Event Grid Topic: $EVENT_GRID_TOPIC"
echo "🔴 Redis Cache: $REDIS_NAME"
echo ""
echo "🔧 Next Steps:"
echo "1. Verify all resources are created successfully"
echo "2. Test Event Grid event publishing"
echo "3. Test Redis connectivity"
echo "4. Test Service Bus message sending"
echo "5. Monitor Function App logs for any configuration issues"
echo ""
echo "💡 Tip: Use 'az functionapp logs tail' to monitor Function App logs"
