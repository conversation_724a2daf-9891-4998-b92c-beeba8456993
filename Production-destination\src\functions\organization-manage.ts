/**
 * Organization Management Function
 * Handles retrieving, updating, and deleting organizations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { serviceBusEnhanced } from '../shared/services/service-bus';
import { publishEvent, EventType } from './event-grid-handlers';

// Organization tiers enum
enum OrganizationTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE'
}

// Validation schemas
const updateOrganizationSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  description: Joi.string().max(500).optional(),
  tier: Joi.string().valid(...Object.values(OrganizationTier)).optional(),
  settings: Joi.object({
    allowedDocumentTypes: Joi.array().items(Joi.string()).optional(),
    maxFileSize: Joi.number().integer().min(1024).optional(),
    features: Joi.object({
      aiAnalysis: Joi.boolean().optional(),
      advancedWorkflows: Joi.boolean().optional(),
      bulkProcessing: Joi.boolean().optional(),
      apiAccess: Joi.boolean().optional()
    }).optional()
  }).optional()
});

/**
 * Organization management handler
 */
export async function organizationManage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const organizationId = request.params.organizationId;

  if (!organizationId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Organization ID is required' }
    }, request);
  }

  logger.info("Organization management started", { correlationId, organizationId, method: request.method });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get organization
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user is a member of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    const membership = memberships[0];

    if (request.method === 'GET') {
      // Get organization details with enriched data

      // Get member count
      const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
      const memberCountResult = await db.queryItems('organization-members', memberCountQuery, [organizationId, 'active']);
      const memberCount = Number(memberCountResult[0]) || 0;

      // Get project count
      const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
      const projectCountResult = await db.queryItems('projects', projectCountQuery, [organizationId]);
      const projectCount = Number(projectCountResult[0]) || 0;

      // Get document count
      const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
      const documentCountResult = await db.queryItems('documents', documentCountQuery, [organizationId]);
      const documentCount = Number(documentCountResult[0]) || 0;

      // Calculate actual storage usage
      const storageUsed = await calculateOrganizationStorageUsage(organizationId);

      // Enrich organization with statistics
      const enrichedOrganization = {
        ...(organization as any),
        memberCount,
        projectCount,
        documentCount,
        storageUsed,
        userRole: (membership as any).role
      };

      // Cache organization data in Redis
      await redis.setJson(`org:${organizationId}:details`, enrichedOrganization, 1800); // 30 minutes cache

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: enrichedOrganization
      }, request);

    } else if (request.method === 'PATCH') {
      // Update organization

      // Check if user has admin role
      if ((membership as any).role !== 'ADMIN') {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Only organization admins can update organization settings" }
        }, request);
      }

      // Validate request body
      const body = await request.json();
      const { error, value } = updateOrganizationSchema.validate(body);

      if (error) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: {
            error: 'Validation Error',
            message: error.details.map(d => d.message).join(', ')
          }
        }, request);
      }

      const updateData = value;

      // Update organization
      const updatedOrganization = {
        ...(organization as any),
        id: organizationId,
        ...updateData,
        updatedBy: user.id,
        updatedAt: new Date().toISOString()
      };

      // If tier is being updated, update feature settings
      if (updateData.tier) {
        updatedOrganization.settings = {
          ...updatedOrganization.settings,
          maxProjects: updateData.tier === OrganizationTier.FREE ? 3 :
                      updateData.tier === OrganizationTier.PROFESSIONAL ? 10 :
                      updateData.tier === OrganizationTier.ENTERPRISE ? 100 : 3,
          features: {
            ...updatedOrganization.settings.features,
            aiAnalysis: updateData.tier !== OrganizationTier.FREE,
            advancedWorkflows: updateData.tier === OrganizationTier.ENTERPRISE,
            bulkProcessing: updateData.tier !== OrganizationTier.FREE,
            apiAccess: updateData.tier === OrganizationTier.ENTERPRISE
          }
        };
      }

      await db.updateItem('organizations', updatedOrganization);

      // Invalidate Redis cache
      await redis.del(`org:${organizationId}:details`);
      await redis.del(`user:${user.id}:organizations`);

      // Publish Event Grid event
      await publishEvent(
        EventType.ORGANIZATION_UPDATED,
        `organizations/${organizationId}/updated`,
        {
          organizationId,
          updatedFields: Object.keys(updateData),
          previousTier: (organization as any).tier,
          newTier: updateData.tier || (organization as any).tier,
          updatedBy: user.id,
          timestamp: new Date().toISOString()
        }
      );

      // Send Service Bus message for workflow orchestration
      await serviceBusEnhanced.sendToQueue('analytics-events', {
        body: {
          eventType: 'organization_updated',
          organizationId,
          updatedBy: user.id,
          updatedFields: Object.keys(updateData),
          timestamp: new Date().toISOString()
        },
        messageId: `org-update-${organizationId}-${Date.now()}`,
        correlationId: `org-${organizationId}`,
        subject: 'organization.updated'
      });

      // Create activity record
      await db.createItem('activities', {
        id: uuidv4(),
        type: "organization_updated",
        userId: user.id,
        organizationId,
        timestamp: new Date().toISOString(),
        details: {
          updatedFields: Object.keys(updateData),
          previousTier: (organization as any).tier,
          newTier: updateData.tier || (organization as any).tier
        },
        tenantId: user.tenantId
      });

      logger.info("Organization updated successfully", {
        correlationId,
        organizationId,
        userId: user.id,
        updatedFields: Object.keys(updateData)
      });

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          id: organizationId,
          message: "Organization updated successfully"
        }
      }, request);

    } else if (request.method === 'DELETE') {
      // Delete organization

      // Check if user has admin role
      if ((membership as any).role !== 'ADMIN') {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Only organization admins can delete organizations" }
        }, request);
      }

      // Check if organization has projects
      const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
      const projectCountResult = await db.queryItems('projects', projectCountQuery, [organizationId]);
      const projectCount = Number(projectCountResult[0]) || 0;

      if (projectCount > 0) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: {
            error: "Cannot delete organization with existing projects",
            message: `Organization has ${projectCount} projects. Please delete all projects first.`
          }
        }, request);
      }

      // Delete organization members
      const allMembersQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
      const allMembers = await db.queryItems('organization-members', allMembersQuery, [organizationId]);

      for (const member of allMembers) {
        await db.deleteItem('organization-members', (member as any).id, (member as any).id);
      }

      // Delete organization
      await db.deleteItem('organizations', organizationId, organizationId);

      // Invalidate Redis cache
      await redis.del(`org:${organizationId}:details`);
      await redis.del(`user:${user.id}:organizations`);

      // Publish Event Grid event
      await publishEvent(
        EventType.ORGANIZATION_DELETED,
        `organizations/${organizationId}/deleted`,
        {
          organizationId,
          organizationName: (organization as any).name,
          memberCount: allMembers.length,
          deletedBy: user.id,
          timestamp: new Date().toISOString()
        }
      );

      // Send Service Bus message for cleanup workflows
      await serviceBusEnhanced.sendToQueue('analytics-events', {
        body: {
          eventType: 'organization_deleted',
          organizationId,
          organizationName: (organization as any).name,
          memberCount: allMembers.length,
          deletedBy: user.id,
          timestamp: new Date().toISOString()
        },
        messageId: `org-delete-${organizationId}-${Date.now()}`,
        correlationId: `org-${organizationId}`,
        subject: 'organization.deleted'
      });

      // Create activity record
      await db.createItem('activities', {
        id: uuidv4(),
        type: "organization_deleted",
        userId: user.id,
        organizationId,
        timestamp: new Date().toISOString(),
        details: {
          organizationName: (organization as any).name,
          memberCount: allMembers.length
        },
        tenantId: user.tenantId
      });

      logger.info("Organization deleted successfully", {
        correlationId,
        organizationId,
        userId: user.id
      });

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          message: "Organization deleted successfully"
        }
      }, request);

    } else {
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Method not allowed" }
      }, request);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Organization management failed", {
      correlationId,
      organizationId,
      method: request.method,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate actual storage usage for an organization
 */
async function calculateOrganizationStorageUsage(organizationId: string): Promise<number> {
  try {
    // Get all documents for the organization
    const documentsQuery = 'SELECT c.size FROM c WHERE c.organizationId = @orgId AND c.size != null';
    const documents = await db.queryItems('documents', documentsQuery, [organizationId]);

    // Calculate total storage in bytes
    const totalBytes = documents.reduce((total: number, doc: any) => {
      return total + (doc.size || 0);
    }, 0);

    // Convert to GB and round to 2 decimal places
    const totalGB = Math.round((totalBytes / (1024 * 1024 * 1024)) * 100) / 100;

    logger.info('Organization storage calculated', {
      organizationId,
      totalBytes,
      totalGB,
      documentCount: documents.length
    });

    return totalGB;
  } catch (error) {
    logger.error('Failed to calculate organization storage usage', {
      error: error instanceof Error ? error.message : String(error),
      organizationId
    });
    return 0;
  }
}

// Register functions
app.http('organization-manage', {
  methods: ['GET', 'PATCH', 'DELETE', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}',
  handler: organizationManage
});
