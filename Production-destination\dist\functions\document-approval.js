"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDocumentApproval = createDocumentApproval;
exports.reviewDocument = reviewDocument;
/**
 * Document Approval Function
 * Handles document approval workflows, review processes, and approval tracking
 * Migrated from old-arch/src/document-service/approval/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
const event_grid_handlers_1 = require("./event-grid-handlers");
const service_bus_1 = require("../shared/services/service-bus");
const redis_1 = require("../shared/services/redis");
const signalr_1 = require("../shared/services/signalr");
// Approval types and enums
var ApprovalStatus;
(function (ApprovalStatus) {
    ApprovalStatus["PENDING"] = "PENDING";
    ApprovalStatus["APPROVED"] = "APPROVED";
    ApprovalStatus["REJECTED"] = "REJECTED";
    ApprovalStatus["CANCELLED"] = "CANCELLED";
})(ApprovalStatus || (ApprovalStatus = {}));
var ApprovalType;
(function (ApprovalType) {
    ApprovalType["SINGLE"] = "SINGLE";
    ApprovalType["SEQUENTIAL"] = "SEQUENTIAL";
    ApprovalType["PARALLEL"] = "PARALLEL";
    ApprovalType["MAJORITY"] = "MAJORITY";
    ApprovalType["UNANIMOUS"] = "UNANIMOUS";
})(ApprovalType || (ApprovalType = {}));
var ReviewerStatus;
(function (ReviewerStatus) {
    ReviewerStatus["PENDING"] = "PENDING";
    ReviewerStatus["APPROVED"] = "APPROVED";
    ReviewerStatus["REJECTED"] = "REJECTED";
    ReviewerStatus["ABSTAINED"] = "ABSTAINED";
})(ReviewerStatus || (ReviewerStatus = {}));
// Validation schemas
const createApprovalSchema = joi_1.default.object({
    documentId: joi_1.default.string().uuid().required(),
    approvalType: joi_1.default.string().valid(...Object.values(ApprovalType)).default(ApprovalType.SINGLE),
    reviewers: joi_1.default.array().items(joi_1.default.object({
        userId: joi_1.default.string().uuid().required(),
        order: joi_1.default.number().min(1).optional(),
        required: joi_1.default.boolean().default(true),
        role: joi_1.default.string().optional()
    })).min(1).required(),
    dueDate: joi_1.default.string().isoDate().optional(),
    message: joi_1.default.string().max(1000).optional(),
    requireComments: joi_1.default.boolean().default(false),
    allowDelegation: joi_1.default.boolean().default(false),
    autoApproveAfter: joi_1.default.number().min(1).max(30).optional(), // Days
    metadata: joi_1.default.object().optional()
});
const reviewDocumentSchema = joi_1.default.object({
    approvalId: joi_1.default.string().uuid().required(),
    decision: joi_1.default.string().valid('APPROVED', 'REJECTED', 'ABSTAINED').required(),
    comments: joi_1.default.string().max(2000).optional(),
    attachments: joi_1.default.array().items(joi_1.default.object({
        fileName: joi_1.default.string().required(),
        fileUrl: joi_1.default.string().uri().required(),
        fileSize: joi_1.default.number().optional()
    })).optional(),
    delegateTo: joi_1.default.string().uuid().optional()
});
/**
 * Create document approval handler
 */
async function createDocumentApproval(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create document approval started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createApprovalSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const approvalRequest = value;
        // Verify document exists and user has permission
        const document = await database_1.db.readItem('documents', approvalRequest.documentId, approvalRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check if user can create approval for this document
        const canCreateApproval = await checkApprovalPermission(documentData, user);
        if (!canCreateApproval) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to create approval for this document" }
            }, request);
        }
        // Check if document already has pending approval
        const existingApprovalQuery = 'SELECT * FROM c WHERE c.documentId = @docId AND c.status = @status';
        const existingApprovals = await database_1.db.queryItems('document-approvals', existingApprovalQuery, [approvalRequest.documentId, ApprovalStatus.PENDING]);
        if (existingApprovals.length > 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document already has a pending approval" }
            }, request);
        }
        // Validate reviewers exist
        const reviewerIds = approvalRequest.reviewers.map(r => r.userId);
        const reviewerValidation = await validateReviewers(reviewerIds);
        if (!reviewerValidation.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: `Invalid reviewers: ${reviewerValidation.invalidIds.join(', ')}` }
            }, request);
        }
        // Create approval record
        const approvalId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        // Prepare reviewers with status
        const reviewersWithStatus = approvalRequest.reviewers.map((reviewer, index) => ({
            ...reviewer,
            id: (0, uuid_1.v4)(),
            status: ReviewerStatus.PENDING,
            order: reviewer.order || index + 1,
            assignedAt: now,
            reviewedAt: null,
            decision: null,
            comments: null,
            attachments: []
        }));
        const approval = {
            id: approvalId,
            documentId: approvalRequest.documentId,
            documentName: documentData.name,
            approvalType: approvalRequest.approvalType,
            status: ApprovalStatus.PENDING,
            reviewers: reviewersWithStatus,
            dueDate: approvalRequest.dueDate,
            message: approvalRequest.message,
            requireComments: approvalRequest.requireComments,
            allowDelegation: approvalRequest.allowDelegation,
            autoApproveAfter: approvalRequest.autoApproveAfter,
            metadata: approvalRequest.metadata,
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            completedAt: null,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('document-approvals', approval);
        // Update document status
        const updatedDocument = {
            ...documentData,
            status: 'PENDING_APPROVAL',
            approvalId,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_approval_created",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: approvalRequest.documentId,
            timestamp: now,
            details: {
                approvalId,
                documentName: documentData.name,
                approvalType: approvalRequest.approvalType,
                reviewerCount: reviewersWithStatus.length,
                dueDate: approvalRequest.dueDate
            },
            tenantId: user.tenantId
        });
        // Cache approval data in Redis for quick access
        try {
            await redis_1.redis.setex(`approval:${approvalId}`, 7200, // 2 hours cache
            JSON.stringify({
                id: approvalId,
                documentId: approvalRequest.documentId,
                documentName: documentData.name,
                status: ApprovalStatus.PENDING,
                approvalType: approvalRequest.approvalType,
                reviewerCount: reviewersWithStatus.length,
                createdBy: user.id,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId
            }));
        }
        catch (redisError) {
            logger_1.logger.warn("Failed to cache approval data", { redisError });
        }
        // Publish Event Grid event for approval creation
        try {
            await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.WORKFLOW_STARTED, `approvals/${approvalId}/created`, {
                approvalId,
                documentId: approvalRequest.documentId,
                documentName: documentData.name,
                approvalType: approvalRequest.approvalType,
                reviewerCount: reviewersWithStatus.length,
                createdBy: user.id,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId,
                dueDate: approvalRequest.dueDate,
                timestamp: now
            });
        }
        catch (eventError) {
            logger_1.logger.warn("Failed to publish approval created event", { eventError });
        }
        // Send approval workflow to Service Bus for processing
        try {
            await service_bus_1.serviceBusEnhanced.sendToQueue('workflow-orchestration', {
                body: {
                    type: 'approval_workflow_started',
                    approvalId,
                    documentId: approvalRequest.documentId,
                    approvalType: approvalRequest.approvalType,
                    reviewers: reviewersWithStatus.map(r => ({
                        userId: r.userId,
                        order: r.order,
                        required: r.required
                    })),
                    organizationId: documentData.organizationId,
                    projectId: documentData.projectId,
                    createdBy: user.id,
                    timestamp: now
                },
                messageId: `approval-workflow-${approvalId}-${Date.now()}`,
                correlationId,
                applicationProperties: {
                    approvalId,
                    documentId: approvalRequest.documentId,
                    organizationId: documentData.organizationId,
                    source: 'document-approval'
                }
            });
        }
        catch (serviceBusError) {
            logger_1.logger.warn("Failed to send approval workflow to Service Bus", { serviceBusError });
        }
        // Send real-time notifications via SignalR
        try {
            for (const reviewer of reviewersWithStatus) {
                await signalr_1.signalREnhanced.sendToUser(reviewer.userId, {
                    target: 'approvalAssigned',
                    arguments: [{
                            approvalId,
                            documentId: approvalRequest.documentId,
                            documentName: documentData.name,
                            approvalType: approvalRequest.approvalType,
                            dueDate: approvalRequest.dueDate,
                            message: approvalRequest.message,
                            assignedBy: user.id,
                            timestamp: now
                        }]
                });
            }
        }
        catch (signalRError) {
            logger_1.logger.warn("Failed to send real-time notifications", { signalRError });
        }
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentApprovalCreated',
            aggregateId: approvalId,
            aggregateType: 'DocumentApproval',
            version: 1,
            data: {
                approval,
                document: documentData,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        // Send notifications to reviewers
        await notifyReviewers(approval, documentData, user, 'ASSIGNED');
        logger_1.logger.info("Document approval created successfully", {
            correlationId,
            approvalId,
            documentId: approvalRequest.documentId,
            documentName: documentData.name,
            approvalType: approvalRequest.approvalType,
            reviewerCount: reviewersWithStatus.length,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: approvalId,
                documentId: approvalRequest.documentId,
                documentName: documentData.name,
                approvalType: approvalRequest.approvalType,
                status: ApprovalStatus.PENDING,
                reviewers: reviewersWithStatus.map(r => ({
                    id: r.id,
                    userId: r.userId,
                    order: r.order,
                    required: r.required,
                    status: r.status,
                    assignedAt: r.assignedAt
                })),
                dueDate: approvalRequest.dueDate,
                createdBy: user.id,
                createdAt: now,
                message: "Document approval created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create document approval failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Review document handler
 */
async function reviewDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Review document started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = reviewDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const reviewRequest = value;
        // Get approval record
        const approval = await database_1.db.readItem('document-approvals', reviewRequest.approvalId, reviewRequest.approvalId);
        if (!approval) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Approval not found" }
            }, request);
        }
        const approvalData = approval;
        // Check if approval is still pending
        if (approvalData.status !== ApprovalStatus.PENDING) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Approval is no longer pending" }
            }, request);
        }
        // Find reviewer in approval
        const reviewerIndex = approvalData.reviewers.findIndex((r) => r.userId === user.id && r.status === ReviewerStatus.PENDING);
        if (reviewerIndex === -1) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "You are not authorized to review this document or have already reviewed it" }
            }, request);
        }
        // Check if comments are required
        if (approvalData.requireComments && !reviewRequest.comments) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Comments are required for this approval" }
            }, request);
        }
        const now = new Date().toISOString();
        // Update reviewer status
        const updatedReviewers = [...approvalData.reviewers];
        updatedReviewers[reviewerIndex] = {
            ...updatedReviewers[reviewerIndex],
            status: reviewRequest.decision,
            decision: reviewRequest.decision,
            comments: reviewRequest.comments,
            attachments: reviewRequest.attachments || [],
            reviewedAt: now,
            reviewedBy: user.id
        };
        // Check if approval is complete
        const approvalResult = calculateApprovalResult(approvalData.approvalType, updatedReviewers);
        // Update approval record
        const updatedApproval = {
            ...approvalData,
            reviewers: updatedReviewers,
            status: approvalResult.status,
            completedAt: approvalResult.isComplete ? now : null,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('document-approvals', updatedApproval);
        // Update document status if approval is complete
        if (approvalResult.isComplete) {
            const document = await database_1.db.readItem('documents', approvalData.documentId, approvalData.documentId);
            if (document) {
                const documentData = document;
                const updatedDocument = {
                    ...documentData,
                    status: approvalResult.status === ApprovalStatus.APPROVED ? 'APPROVED' : 'REJECTED',
                    approvalCompletedAt: now,
                    updatedAt: now,
                    updatedBy: user.id
                };
                await database_1.db.updateItem('documents', updatedDocument);
            }
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_reviewed",
            userId: user.id,
            organizationId: approvalData.organizationId,
            projectId: approvalData.projectId,
            documentId: approvalData.documentId,
            timestamp: now,
            details: {
                approvalId: reviewRequest.approvalId,
                decision: reviewRequest.decision,
                documentName: approvalData.documentName,
                hasComments: !!reviewRequest.comments,
                isComplete: approvalResult.isComplete,
                finalStatus: approvalResult.status
            },
            tenantId: user.tenantId
        });
        // Update Redis cache with review status
        try {
            await redis_1.redis.setex(`approval:${reviewRequest.approvalId}`, 7200, // 2 hours cache
            JSON.stringify({
                id: reviewRequest.approvalId,
                documentId: approvalData.documentId,
                documentName: approvalData.documentName,
                status: approvalResult.status,
                approvalType: approvalData.approvalType,
                reviewerCount: updatedReviewers.length,
                completedReviews: updatedReviewers.filter(r => r.status !== ReviewerStatus.PENDING).length,
                isComplete: approvalResult.isComplete,
                lastReviewedBy: user.id,
                lastReviewedAt: now,
                organizationId: approvalData.organizationId,
                projectId: approvalData.projectId
            }));
        }
        catch (redisError) {
            logger_1.logger.warn("Failed to update approval cache", { redisError });
        }
        // Publish Event Grid event for review completion
        try {
            await (0, event_grid_handlers_1.publishEvent)(approvalResult.isComplete ? event_grid_handlers_1.EventType.WORKFLOW_COMPLETED : event_grid_handlers_1.EventType.WORKFLOW_STEP_COMPLETED, `approvals/${reviewRequest.approvalId}/reviewed`, {
                approvalId: reviewRequest.approvalId,
                documentId: approvalData.documentId,
                documentName: approvalData.documentName,
                reviewerId: user.id,
                decision: reviewRequest.decision,
                isComplete: approvalResult.isComplete,
                finalStatus: approvalResult.status,
                organizationId: approvalData.organizationId,
                projectId: approvalData.projectId,
                timestamp: now
            });
        }
        catch (eventError) {
            logger_1.logger.warn("Failed to publish review event", { eventError });
        }
        // Send workflow update to Service Bus
        try {
            await service_bus_1.serviceBusEnhanced.sendToQueue('workflow-orchestration', {
                body: {
                    type: 'approval_review_completed',
                    approvalId: reviewRequest.approvalId,
                    documentId: approvalData.documentId,
                    reviewerId: user.id,
                    decision: reviewRequest.decision,
                    isComplete: approvalResult.isComplete,
                    finalStatus: approvalResult.status,
                    organizationId: approvalData.organizationId,
                    projectId: approvalData.projectId,
                    timestamp: now
                },
                messageId: `approval-review-${reviewRequest.approvalId}-${user.id}-${Date.now()}`,
                correlationId,
                applicationProperties: {
                    approvalId: reviewRequest.approvalId,
                    documentId: approvalData.documentId,
                    reviewerId: user.id,
                    organizationId: approvalData.organizationId,
                    source: 'document-approval'
                }
            });
        }
        catch (serviceBusError) {
            logger_1.logger.warn("Failed to send review update to Service Bus", { serviceBusError });
        }
        // Send real-time notifications via SignalR
        try {
            // Notify document owner
            await signalr_1.signalREnhanced.sendToUser(approvalData.createdBy, {
                target: 'approvalReviewed',
                arguments: [{
                        approvalId: reviewRequest.approvalId,
                        documentId: approvalData.documentId,
                        documentName: approvalData.documentName,
                        reviewerId: user.id,
                        decision: reviewRequest.decision,
                        isComplete: approvalResult.isComplete,
                        finalStatus: approvalResult.status,
                        timestamp: now
                    }]
            });
            // Notify other reviewers if approval is complete
            if (approvalResult.isComplete) {
                const otherReviewers = updatedReviewers.filter(r => r.userId !== user.id);
                for (const reviewer of otherReviewers) {
                    await signalr_1.signalREnhanced.sendToUser(reviewer.userId, {
                        target: 'approvalCompleted',
                        arguments: [{
                                approvalId: reviewRequest.approvalId,
                                documentId: approvalData.documentId,
                                documentName: approvalData.documentName,
                                finalStatus: approvalResult.status,
                                completedBy: user.id,
                                timestamp: now
                            }]
                    });
                }
            }
        }
        catch (signalRError) {
            logger_1.logger.warn("Failed to send real-time review notifications", { signalRError });
        }
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentReviewed',
            aggregateId: reviewRequest.approvalId,
            aggregateType: 'DocumentApproval',
            version: 1,
            data: {
                approval: updatedApproval,
                review: {
                    reviewerId: user.id,
                    decision: reviewRequest.decision,
                    comments: reviewRequest.comments,
                    reviewedAt: now
                },
                isComplete: approvalResult.isComplete
            },
            userId: user.id,
            organizationId: approvalData.organizationId,
            tenantId: user.tenantId
        });
        // Send notifications
        await notifyApprovalUpdate(updatedApproval, user, reviewRequest.decision, approvalResult.isComplete);
        logger_1.logger.info("Document reviewed successfully", {
            correlationId,
            approvalId: reviewRequest.approvalId,
            documentId: approvalData.documentId,
            decision: reviewRequest.decision,
            isComplete: approvalResult.isComplete,
            finalStatus: approvalResult.status,
            reviewedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                approvalId: reviewRequest.approvalId,
                documentId: approvalData.documentId,
                documentName: approvalData.documentName,
                decision: reviewRequest.decision,
                reviewedAt: now,
                isComplete: approvalResult.isComplete,
                finalStatus: approvalResult.status,
                message: "Document reviewed successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Review document failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkApprovalPermission(document, user) {
    // Document owner can create approval
    if (document.createdBy === user.id) {
        return true;
    }
    // Check organization/project permissions
    if (document.organizationId === user.tenantId) {
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, document.organizationId, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === 'OWNER' || membership.role === 'ADMIN';
        }
    }
    return false;
}
async function validateReviewers(reviewerIds) {
    try {
        const invalidIds = [];
        for (const reviewerId of reviewerIds) {
            const user = await database_1.db.readItem('users', reviewerId, reviewerId);
            if (!user) {
                invalidIds.push(reviewerId);
            }
        }
        return {
            valid: invalidIds.length === 0,
            invalidIds
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to validate reviewers', { error, reviewerIds });
        return { valid: false, invalidIds: reviewerIds };
    }
}
function calculateApprovalResult(approvalType, reviewers) {
    const approvedCount = reviewers.filter(r => r.status === ReviewerStatus.APPROVED).length;
    const rejectedCount = reviewers.filter(r => r.status === ReviewerStatus.REJECTED).length;
    const pendingCount = reviewers.filter(r => r.status === ReviewerStatus.PENDING).length;
    const requiredReviewers = reviewers.filter(r => r.required).length;
    switch (approvalType) {
        case ApprovalType.SINGLE:
            if (approvedCount > 0)
                return { status: ApprovalStatus.APPROVED, isComplete: true };
            if (rejectedCount > 0)
                return { status: ApprovalStatus.REJECTED, isComplete: true };
            break;
        case ApprovalType.UNANIMOUS:
            if (rejectedCount > 0)
                return { status: ApprovalStatus.REJECTED, isComplete: true };
            if (approvedCount === requiredReviewers)
                return { status: ApprovalStatus.APPROVED, isComplete: true };
            break;
        case ApprovalType.MAJORITY:
            const majorityThreshold = Math.ceil(requiredReviewers / 2);
            if (approvedCount >= majorityThreshold)
                return { status: ApprovalStatus.APPROVED, isComplete: true };
            if (rejectedCount >= majorityThreshold)
                return { status: ApprovalStatus.REJECTED, isComplete: true };
            break;
        case ApprovalType.SEQUENTIAL:
            // Check if current reviewer in sequence has completed
            const nextPendingReviewer = reviewers
                .filter(r => r.required)
                .sort((a, b) => a.order - b.order)
                .find(r => r.status === ReviewerStatus.PENDING);
            if (!nextPendingReviewer) {
                // All required reviewers have completed
                if (rejectedCount > 0)
                    return { status: ApprovalStatus.REJECTED, isComplete: true };
                return { status: ApprovalStatus.APPROVED, isComplete: true };
            }
            break;
        case ApprovalType.PARALLEL:
            if (pendingCount === 0) {
                // All reviewers have completed
                if (rejectedCount > 0)
                    return { status: ApprovalStatus.REJECTED, isComplete: true };
                return { status: ApprovalStatus.APPROVED, isComplete: true };
            }
            break;
    }
    return { status: ApprovalStatus.PENDING, isComplete: false };
}
async function notifyReviewers(approval, document, creator, _action) {
    try {
        for (const reviewer of approval.reviewers) {
            if (reviewer.status === ReviewerStatus.PENDING) {
                await notification_1.notificationService.sendNotification({
                    userId: reviewer.userId,
                    type: 'DOCUMENT_APPROVAL_ASSIGNED',
                    title: 'Document approval required',
                    message: `${creator.name || creator.email} has requested your approval for "${document.name}".`,
                    priority: 'high',
                    metadata: {
                        approvalId: approval.id,
                        documentId: approval.documentId,
                        documentName: document.name,
                        dueDate: approval.dueDate,
                        approvalType: approval.approvalType,
                        organizationId: document.organizationId
                    },
                    organizationId: document.organizationId,
                    projectId: document.projectId
                });
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to notify reviewers', { error, approvalId: approval.id });
    }
}
async function notifyApprovalUpdate(approval, reviewer, decision, isComplete) {
    try {
        // Notify approval creator
        await notification_1.notificationService.sendNotification({
            userId: approval.createdBy,
            type: 'DOCUMENT_APPROVAL_UPDATED',
            title: `Document ${decision.toLowerCase()}`,
            message: `${reviewer.name || reviewer.email} has ${decision.toLowerCase()} the document "${approval.documentName}".`,
            priority: 'normal',
            metadata: {
                approvalId: approval.id,
                documentId: approval.documentId,
                documentName: approval.documentName,
                decision,
                isComplete,
                reviewedBy: reviewer.id,
                organizationId: approval.organizationId
            },
            organizationId: approval.organizationId,
            projectId: approval.projectId
        });
        // If complete, notify all reviewers
        if (isComplete) {
            for (const rev of approval.reviewers) {
                if (rev.userId !== reviewer.id) {
                    await notification_1.notificationService.sendNotification({
                        userId: rev.userId,
                        type: 'DOCUMENT_APPROVAL_COMPLETED',
                        title: `Document approval ${approval.status.toLowerCase()}`,
                        message: `The approval for "${approval.documentName}" has been ${approval.status.toLowerCase()}.`,
                        priority: 'normal',
                        metadata: {
                            approvalId: approval.id,
                            documentId: approval.documentId,
                            documentName: approval.documentName,
                            finalStatus: approval.status,
                            organizationId: approval.organizationId
                        },
                        organizationId: approval.organizationId,
                        projectId: approval.projectId
                    });
                }
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to notify approval update', { error, approvalId: approval.id });
    }
}
// Register functions
functions_1.app.http('document-approval-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/approvals',
    handler: createDocumentApproval
});
functions_1.app.http('document-review', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/approvals/review',
    handler: reviewDocument
});
//# sourceMappingURL=document-approval.js.map