/**
 * User Profile Management Function
 * Handles user profile updates, avatar management, and profile settings
 * Migrated from old-arch/src/user-service/profile/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient, BlobSASPermissions } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';

import { redis } from '../shared/services/redis';
import { serviceBusEnhanced } from '../shared/services/service-bus';
import { publishEvent, EventType } from './event-grid-handlers';

// Validation schemas
const updateProfileSchema = Joi.object({
  firstName: Joi.string().min(1).max(50).optional(),
  lastName: Joi.string().min(1).max(50).optional(),
  displayName: Joi.string().min(1).max(100).optional(),
  jobTitle: Joi.string().max(100).optional(),
  department: Joi.string().max(100).optional(),
  phoneNumber: Joi.string().max(20).optional(),
  bio: Joi.string().max(500).optional(),
  location: Joi.string().max(100).optional(),
  website: Joi.string().uri().optional(),
  socialLinks: Joi.object({
    linkedin: Joi.string().uri().optional(),
    twitter: Joi.string().uri().optional(),
    github: Joi.string().uri().optional()
  }).optional(),
  skills: Joi.array().items(Joi.string().max(50)).max(20).optional(),
  interests: Joi.array().items(Joi.string().max(50)).max(20).optional()
});

const uploadAvatarSchema = Joi.object({
  fileName: Joi.string().required(),
  contentType: Joi.string().required(),
  fileSize: Joi.number().max(5 * 1024 * 1024).required() // 5MB max
});

interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  jobTitle?: string;
  department?: string;
  phoneNumber?: string;
  bio?: string;
  location?: string;
  website?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
  skills?: string[];
  interests?: string[];
}

interface UploadAvatarRequest {
  fileName: string;
  contentType: string;
  fileSize: number;
}

/**
 * Get user profile handler
 */
export async function getUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const targetUserId = request.params.userId;

  logger.info("Get user profile started", { correlationId, targetUserId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;
    const userId = targetUserId || user.id;

    // Check if user can view this profile
    const canViewProfile = userId === user.id || user.roles?.includes('admin');
    if (!canViewProfile) {
      // Check if users are in the same organization
      const userOrgs = await getUserOrganizations(user.id);
      const targetUserOrgs = await getUserOrganizations(userId);
      const hasSharedOrg = userOrgs.some(org => targetUserOrgs.includes(org));

      if (!hasSharedOrg) {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Access denied to user profile" }
        }, request);
      }
    }

    // Try to get user profile from Redis cache first
    let profileData: any = null;

    try {
      const cachedProfile = await redis.getJson(`user:${userId}:profile`);
      if (cachedProfile) {
        profileData = cachedProfile;
        logger.info("User profile retrieved from cache", { userId, correlationId });
      }
    } catch (error) {
      logger.warn("Failed to retrieve user profile from cache", { userId, error });
    }

    // If not in cache, get from database
    if (!profileData) {
      const userProfile = await db.readItem('users', userId, userId);
      if (!userProfile) {
        return addCorsHeaders({
          status: 404,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "User not found" }
        }, request);
      }

      profileData = userProfile as any;

      // Cache the profile for future requests
      try {
        await redis.setJson(`user:${userId}:profile`, profileData, 3600); // 1 hour cache
      } catch (error) {
        logger.warn("Failed to cache user profile", { userId, error });
      }
    }

    // Return profile data (excluding sensitive information for other users)
    const isOwnProfile = userId === user.id;
    const responseData = {
      id: profileData.id,
      email: isOwnProfile ? profileData.email : undefined,
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      displayName: profileData.displayName,
      jobTitle: profileData.jobTitle,
      department: profileData.department,
      phoneNumber: isOwnProfile ? profileData.phoneNumber : undefined,
      bio: profileData.bio,
      location: profileData.location,
      website: profileData.website,
      socialLinks: profileData.socialLinks,
      skills: profileData.skills || [],
      interests: profileData.interests || [],
      avatarUrl: profileData.avatarUrl,
      createdAt: profileData.createdAt,
      updatedAt: profileData.updatedAt,
      lastLoginAt: isOwnProfile ? profileData.lastLoginAt : undefined,
      isOnline: profileData.isOnline || false,
      lastSeenAt: profileData.lastSeenAt,
      organizationIds: isOwnProfile ? profileData.organizationIds : undefined,
      roles: isOwnProfile ? profileData.roles : undefined
    };

    logger.info("User profile retrieved successfully", {
      correlationId,
      userId,
      isOwnProfile,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: responseData
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get user profile failed", {
      correlationId,
      targetUserId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update user profile handler
 */
export async function updateUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update user profile started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateProfileSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const profileUpdates: UpdateProfileRequest = value;

    // Get current user profile
    const currentProfile = await db.readItem('users', user.id, user.id);
    if (!currentProfile) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User profile not found" }
      }, request);
    }

    const currentData = currentProfile as any;
    const now = new Date().toISOString();

    // Update display name if first/last name changed
    let displayName = profileUpdates.displayName;
    if ((profileUpdates.firstName || profileUpdates.lastName) && !displayName) {
      const firstName = profileUpdates.firstName || currentData.firstName;
      const lastName = profileUpdates.lastName || currentData.lastName;
      displayName = `${firstName} ${lastName}`.trim();
    }

    // Create updated profile
    const updatedProfile = {
      ...currentData,
      ...profileUpdates,
      displayName: displayName || currentData.displayName,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('users', updatedProfile);

    // Invalidate Redis cache
    await redis.del(`user:${user.id}:profile`);
    await redis.del(`user:${user.id}:organizations`);

    // Publish Event Grid event
    await publishEvent(
      EventType.USER_UPDATED,
      `users/${user.id}/updated`,
      {
        userId: user.id,
        updatedFields: Object.keys(profileUpdates),
        previousData: {
          displayName: currentData.displayName,
          jobTitle: currentData.jobTitle,
          department: currentData.department
        },
        newData: {
          displayName: updatedProfile.displayName,
          jobTitle: updatedProfile.jobTitle,
          department: updatedProfile.department
        },
        timestamp: now
      }
    );

    // Send Service Bus message for analytics
    await serviceBusEnhanced.sendToQueue('analytics-events', {
      body: {
        eventType: 'user_profile_updated',
        userId: user.id,
        updatedFields: Object.keys(profileUpdates),
        timestamp: now
      },
      messageId: `user-update-${user.id}-${Date.now()}`,
      correlationId: `user-${user.id}`,
      subject: 'user.profile.updated'
    });

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "user_profile_updated",
      userId: user.id,
      timestamp: now,
      details: {
        updatedFields: Object.keys(profileUpdates),
        displayNameChanged: displayName !== currentData.displayName
      },
      tenantId: user.tenantId
    });

    // Send notification for significant changes
    if (profileUpdates.displayName || profileUpdates.jobTitle || profileUpdates.department) {
      await notificationService.sendNotification({
        userId: user.id,
        type: 'PROFILE_UPDATED',
        title: 'Profile updated successfully',
        message: 'Your profile information has been updated.',
        priority: 'normal',
        metadata: {
          updatedFields: Object.keys(profileUpdates)
        }
      });
    }

    logger.info("User profile updated successfully", {
      correlationId,
      userId: user.id,
      updatedFields: Object.keys(profileUpdates)
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: updatedProfile.id,
        firstName: updatedProfile.firstName,
        lastName: updatedProfile.lastName,
        displayName: updatedProfile.displayName,
        jobTitle: updatedProfile.jobTitle,
        department: updatedProfile.department,
        phoneNumber: updatedProfile.phoneNumber,
        bio: updatedProfile.bio,
        location: updatedProfile.location,
        website: updatedProfile.website,
        socialLinks: updatedProfile.socialLinks,
        skills: updatedProfile.skills,
        interests: updatedProfile.interests,
        updatedAt: updatedProfile.updatedAt,
        message: "Profile updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update user profile failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Upload avatar handler
 */
export async function uploadAvatar(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Upload avatar started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = uploadAvatarSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const uploadRequest: UploadAvatarRequest = value;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(uploadRequest.contentType)) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed." }
      }, request);
    }

    // Generate avatar blob name
    const fileExtension = uploadRequest.fileName.split('.').pop();
    const avatarBlobName = `avatars/${user.id}/${uuidv4()}.${fileExtension}`;

    // Generate upload URL
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.AVATAR_CONTAINER || "avatars"
    );
    const blobClient = containerClient.getBlockBlobClient(avatarBlobName);

    // Generate SAS URL for upload
    const permissions = new BlobSASPermissions();
    permissions.write = true;
    permissions.create = true;

    const uploadUrl = await blobClient.generateSasUrl({
      permissions,
      expiresOn: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes
    });

    // Generate public URL for the avatar
    const avatarUrl = blobClient.url;

    logger.info("Avatar upload URL generated successfully", {
      correlationId,
      userId: user.id,
      avatarBlobName,
      fileSize: uploadRequest.fileSize
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        uploadUrl,
        avatarUrl,
        blobName: avatarBlobName,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        message: "Upload URL generated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Upload avatar failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function getUserOrganizations(userId: string): Promise<string[]> {
  try {
    const membershipQuery = 'SELECT c.organizationId FROM c WHERE c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [userId, 'ACTIVE']);
    return memberships.map((m: any) => m.organizationId);
  } catch (error) {
    logger.error('Failed to get user organizations', { error, userId });
    return [];
  }
}

// Register functions
app.http('user-profile-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/{userId?}/profile',
  handler: getUserProfile
});

app.http('user-profile-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/profile/update',
  handler: updateUserProfile
});

app.http('user-avatar-upload', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/avatar/upload',
  handler: uploadAvatar
});
