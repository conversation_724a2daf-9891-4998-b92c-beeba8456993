/**
 * RAG Query Function
 * Handles Retrieval Augmented Generation queries for document-based AI reasoning
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { ragService } from '../shared/services/rag-service';

// Request validation schema
const ragQuerySchema = Joi.object({
  query: Joi.string().min(3).max(1000).required(),
  documentIds: Joi.array().items(Joi.string().uuid()).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  maxResults: Joi.number().min(1).max(20).default(5),
  similarityThreshold: Joi.number().min(0).max(1).default(0.7),
  includeMetadata: Joi.boolean().default(true)
});

/**
 * RAG Query handler
 */
export async function ragQuery(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = uuidv4();
  const startTime = Date.now();

  try {
    logger.info('RAG query request received', {
      correlationId,
      method: request.method,
      url: request.url
    });

    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Unauthorized", message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse and validate request body
    let requestBody;
    try {
      const bodyText = await request.text();
      requestBody = JSON.parse(bodyText);
    } catch (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid JSON in request body" }
      }, request);
    }

    const { error, value: ragQueryRequest } = ragQuerySchema.validate(requestBody);
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: "Validation failed", 
          details: error.details.map(d => d.message) 
        }
      }, request);
    }

    // Verify user has access to organization
    if (user.organizationId !== ragQueryRequest.organizationId) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Perform RAG query
    const ragResult = await ragService.query(ragQueryRequest);

    // Store query history
    await storeQueryHistory(ragQueryRequest, user, ragResult, correlationId);

    const response = {
      queryId: correlationId,
      query: ragQueryRequest.query,
      answer: ragResult.answer,
      reasoning: ragResult.reasoning,
      sources: ragResult.sources,
      confidence: ragResult.confidence,
      tokensUsed: ragResult.tokensUsed,
      processingTime: ragResult.processingTime,
      totalProcessingTime: Date.now() - startTime,
      success: true
    };

    logger.info('RAG query completed successfully', {
      correlationId,
      query: ragQueryRequest.query,
      sourcesFound: ragResult.sources.length,
      confidence: ragResult.confidence,
      processingTime: response.totalProcessingTime
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    logger.error('RAG query failed', {
      correlationId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { 
        error: "Internal server error",
        correlationId
      }
    }, request);
  }
}

/**
 * Get RAG query history
 */
export async function getRagQueryHistory(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = uuidv4();

  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Unauthorized", message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get query parameters
    const organizationId = request.query.get('organizationId');
    const projectId = request.query.get('projectId');
    const limit = parseInt(request.query.get('limit') || '20');
    const offset = parseInt(request.query.get('offset') || '0');

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "organizationId is required" }
      }, request);
    }

    // Verify user has access to organization
    if (user.organizationId !== organizationId) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Build query
    let cosmosQuery = 'SELECT * FROM c WHERE c.organizationId = @organizationId';
    const parameters = [{ name: '@organizationId', value: organizationId }];

    if (projectId) {
      cosmosQuery += ' AND c.projectId = @projectId';
      parameters.push({ name: '@projectId', value: projectId });
    }

    cosmosQuery += ' ORDER BY c.createdAt DESC';

    // Get query history
    const queryHistory = await db.queryItems('rag-query-history', cosmosQuery, parameters);

    // Apply pagination
    const paginatedHistory = queryHistory.slice(offset, offset + limit);

    const response = {
      queries: paginatedHistory.map((query: any) => ({
        id: query.id,
        query: query.query,
        answer: query.answer?.substring(0, 200) + (query.answer?.length > 200 ? '...' : ''),
        confidence: query.confidence,
        sourcesCount: query.sourcesCount,
        createdAt: query.createdAt,
        processingTime: query.processingTime
      })),
      total: queryHistory.length,
      offset,
      limit,
      hasMore: offset + limit < queryHistory.length
    };

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    logger.error('Failed to get RAG query history', {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { 
        error: "Internal server error",
        correlationId
      }
    }, request);
  }
}

/**
 * Store query history for analytics and improvement
 */
async function storeQueryHistory(
  ragQueryRequest: any,
  user: any,
  ragResult: any,
  correlationId: string
): Promise<void> {
  try {
    const queryHistory = {
      id: correlationId,
      userId: user.id,
      organizationId: ragQueryRequest.organizationId,
      projectId: ragQueryRequest.projectId,
      query: ragQueryRequest.query,
      answer: ragResult.answer,
      reasoning: ragResult.reasoning,
      confidence: ragResult.confidence,
      sourcesCount: ragResult.sources.length,
      sources: ragResult.sources.map((source: any) => ({
        documentId: source.documentId,
        documentName: source.documentName,
        relevanceScore: source.relevanceScore
      })),
      tokensUsed: ragResult.tokensUsed,
      processingTime: ragResult.processingTime,
      createdAt: new Date().toISOString(),
      tenantId: user.tenantId
    };

    await db.createItem('rag-query-history', queryHistory);

    logger.info('RAG query history stored', {
      correlationId,
      userId: user.id,
      organizationId: ragQueryRequest.organizationId
    });

  } catch (error) {
    logger.error('Failed to store RAG query history', {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    // Don't throw - query should succeed even if history storage fails
  }
}

// Register functions
app.http('rag-query', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'rag/query',
  handler: ragQuery
});

app.http('rag-query-history', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'rag/history',
  handler: getRagQueryHistory
});
