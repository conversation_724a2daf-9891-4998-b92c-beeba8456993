# Azure Enhanced Services Setup Script (PowerShell)
# This script configures Event Grid, Redis, and Service Bus resources for the enhanced functions

# Configuration variables
$ResourceGroup = "docucontext"
$Location = "eastus"
$SubscriptionId = (az account show --query id -o tsv)

# Service Bus Configuration
$ServiceBusNamespace = "hepzbackend"

# Redis Configuration  
$RedisName = "hepzbackend"

# Event Grid Configuration
$EventGridTopic = "hepz-events"

# Function App Configuration
$FunctionAppName = "hepzlogic"

Write-Host "🚀 Starting Azure Enhanced Services Configuration..." -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Cyan
Write-Host "Location: $Location" -ForegroundColor Cyan
Write-Host "Service Bus Namespace: $ServiceBusNamespace" -ForegroundColor Cyan
Write-Host "Redis Name: $RedisName" -ForegroundColor Cyan
Write-Host "Event Grid Topic: $EventGridTopic" -ForegroundColor Cyan
Write-Host "Function App: $FunctionAppName" -ForegroundColor Cyan

# 1. Configure Service Bus Queues and Topics
Write-Host "📨 Configuring Service Bus queues and topics..." -ForegroundColor Yellow

# Create analytics-events queue
Write-Host "Creating analytics-events queue..."
az servicebus queue create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name analytics-events `
  --max-size 1024 `
  --default-message-time-to-live P14D `
  --enable-dead-lettering-on-message-expiration true `
  --max-delivery-count 10

# Create user-events topic
Write-Host "Creating user-events topic..."
az servicebus topic create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name user-events `
  --max-size 1024 `
  --default-message-time-to-live P14D `
  --enable-duplicate-detection true

# Create subscription for user events
Write-Host "Creating user-events subscription..."
az servicebus topic subscription create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --topic-name user-events `
  --name user-processor `
  --max-delivery-count 10 `
  --enable-dead-lettering-on-message-expiration true

# Create project-events topic
Write-Host "Creating project-events topic..."
az servicebus topic create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name project-events `
  --max-size 1024 `
  --default-message-time-to-live P14D `
  --enable-duplicate-detection true

# Create subscription for project events
Write-Host "Creating project-events subscription..."
az servicebus topic subscription create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --topic-name project-events `
  --name project-processor `
  --max-delivery-count 10 `
  --enable-dead-lettering-on-message-expiration true

# Create organization-events topic
Write-Host "Creating organization-events topic..."
az servicebus topic create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name organization-events `
  --max-size 1024 `
  --default-message-time-to-live P14D `
  --enable-duplicate-detection true

# Create subscription for organization events
Write-Host "Creating organization-events subscription..."
az servicebus topic subscription create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --topic-name organization-events `
  --name organization-processor `
  --max-delivery-count 10 `
  --enable-dead-lettering-on-message-expiration true

# Create thumbnail-processing queue
Write-Host "Creating thumbnail-processing queue..."
az servicebus queue create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name thumbnail-processing `
  --max-size 1024 `
  --default-message-time-to-live P7D `
  --enable-dead-lettering-on-message-expiration true `
  --max-delivery-count 5

# Create data-migration queue
Write-Host "Creating data-migration queue..."
az servicebus queue create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name data-migration `
  --max-size 2048 `
  --default-message-time-to-live P30D `
  --enable-dead-lettering-on-message-expiration true `
  --max-delivery-count 3

# Create enterprise-integration queue
Write-Host "Creating enterprise-integration queue..."
az servicebus queue create `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name enterprise-integration `
  --max-size 1024 `
  --default-message-time-to-live P14D `
  --enable-dead-lettering-on-message-expiration true `
  --max-delivery-count 5

Write-Host "✅ Service Bus configuration completed!" -ForegroundColor Green

# 2. Configure Event Grid Topic
Write-Host "⚡ Configuring Event Grid..." -ForegroundColor Yellow

# Create Event Grid Topic
Write-Host "Creating Event Grid topic..."
az eventgrid topic create `
  --resource-group $ResourceGroup `
  --name $EventGridTopic `
  --location $Location `
  --input-schema EventGridSchema

# Get Event Grid Topic endpoint and key
$EventGridEndpoint = az eventgrid topic show `
  --resource-group $ResourceGroup `
  --name $EventGridTopic `
  --query endpoint -o tsv

$EventGridKey = az eventgrid topic key list `
  --resource-group $ResourceGroup `
  --name $EventGridTopic `
  --query key1 -o tsv

Write-Host "Event Grid Topic Endpoint: $EventGridEndpoint" -ForegroundColor Cyan
Write-Host "Event Grid Topic Key: [HIDDEN]" -ForegroundColor Cyan

# Create Event Grid Subscriptions
Write-Host "Creating Event Grid subscriptions..."

# User events subscription
az eventgrid event-subscription create `
  --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopic" `
  --name user-events-subscription `
  --endpoint-type azurefunction `
  --endpoint "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Web/sites/$FunctionAppName/functions/event-grid-handlers" `
  --included-event-types "Microsoft.EventGrid.SubscriptionValidationEvent" "user.created" "user.updated" "user.deleted" `
  --max-delivery-attempts 10 `
  --event-ttl 1440

# Project events subscription
az eventgrid event-subscription create `
  --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopic" `
  --name project-events-subscription `
  --endpoint-type azurefunction `
  --endpoint "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Web/sites/$FunctionAppName/functions/event-grid-handlers" `
  --included-event-types "Microsoft.EventGrid.SubscriptionValidationEvent" "project.created" "project.updated" "project.deleted" `
  --max-delivery-attempts 10 `
  --event-ttl 1440

# Organization events subscription
az eventgrid event-subscription create `
  --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopic" `
  --name organization-events-subscription `
  --endpoint-type azurefunction `
  --endpoint "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Web/sites/$FunctionAppName/functions/event-grid-handlers" `
  --included-event-types "Microsoft.EventGrid.SubscriptionValidationEvent" "organization.created" "organization.updated" "organization.deleted" `
  --max-delivery-attempts 10 `
  --event-ttl 1440

Write-Host "✅ Event Grid configuration completed!" -ForegroundColor Green

# 3. Configure Redis Cache
Write-Host "🔴 Configuring Redis Cache..." -ForegroundColor Yellow

# Check if Redis cache exists
$RedisExists = ""
try {
    $RedisExists = az redis show --resource-group $ResourceGroup --name $RedisName --query name -o tsv 2>$null
} catch {
    $RedisExists = ""
}

if ([string]::IsNullOrEmpty($RedisExists)) {
    Write-Host "Creating Redis cache..."
    az redis create `
      --resource-group $ResourceGroup `
      --name $RedisName `
      --location $Location `
      --sku Standard `
      --vm-size c1 `
      --enable-non-ssl-port false `
      --minimum-tls-version 1.2
} else {
    Write-Host "Redis cache already exists: $RedisName" -ForegroundColor Yellow
}

# Get Redis connection details
$RedisHostname = az redis show `
  --resource-group $ResourceGroup `
  --name $RedisName `
  --query hostName -o tsv

$RedisPort = az redis show `
  --resource-group $ResourceGroup `
  --name $RedisName `
  --query sslPort -o tsv

$RedisKey = az redis list-keys `
  --resource-group $ResourceGroup `
  --name $RedisName `
  --query primaryKey -o tsv

Write-Host "Redis Hostname: $RedisHostname" -ForegroundColor Cyan
Write-Host "Redis Port: $RedisPort" -ForegroundColor Cyan
Write-Host "Redis Key: [HIDDEN]" -ForegroundColor Cyan

Write-Host "✅ Redis configuration completed!" -ForegroundColor Green

# 4. Update Function App Configuration
Write-Host "⚙️ Updating Function App configuration..." -ForegroundColor Yellow

# Get Service Bus connection string
$ServiceBusConnection = az servicebus namespace authorization-rule keys list `
  --resource-group $ResourceGroup `
  --namespace-name $ServiceBusNamespace `
  --name RootManageSharedAccessKey `
  --query primaryConnectionString -o tsv

# Update Function App settings
Write-Host "Updating Function App settings..."
az functionapp config appsettings set `
  --resource-group $ResourceGroup `
  --name $FunctionAppName `
  --settings `
    "EVENT_GRID_TOPIC_ENDPOINT=$EventGridEndpoint" `
    "EVENT_GRID_TOPIC_KEY=$EventGridKey" `
    "REDIS_HOSTNAME=$RedisHostname" `
    "REDIS_PORT=$RedisPort" `
    "REDIS_KEY=$RedisKey" `
    "REDIS_CONNECTION_STRING=$RedisHostname`:$RedisPort,password=$RedisKey,ssl=True,abortConnect=False" `
    "SERVICE_BUS_CONNECTION_STRING=$ServiceBusConnection"

Write-Host "✅ Function App configuration updated!" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Azure Enhanced Services Configuration Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Configuration Summary:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host "Service Bus Namespace: $ServiceBusNamespace" -ForegroundColor White
Write-Host "Event Grid Topic: $EventGridTopic" -ForegroundColor White
Write-Host "Redis Cache: $RedisName" -ForegroundColor White
Write-Host "Function App: $FunctionAppName" -ForegroundColor White
Write-Host ""
Write-Host "📨 Service Bus Queues Created:" -ForegroundColor Cyan
Write-Host "- analytics-events" -ForegroundColor White
Write-Host "- thumbnail-processing" -ForegroundColor White
Write-Host "- data-migration" -ForegroundColor White
Write-Host "- enterprise-integration" -ForegroundColor White
Write-Host ""
Write-Host "📨 Service Bus Topics Created:" -ForegroundColor Cyan
Write-Host "- user-events (subscription: user-processor)" -ForegroundColor White
Write-Host "- project-events (subscription: project-processor)" -ForegroundColor White
Write-Host "- organization-events (subscription: organization-processor)" -ForegroundColor White
Write-Host ""
Write-Host "⚡ Event Grid Topic: $EventGridTopic" -ForegroundColor White
Write-Host "🔴 Redis Cache: $RedisName" -ForegroundColor White
