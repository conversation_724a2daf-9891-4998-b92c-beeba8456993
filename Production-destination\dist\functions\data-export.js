"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDataExport = createDataExport;
exports.getExportStatus = getExportStatus;
/**
 * Data Export Function
 * Handles data export operations for various formats and sources
 * Migrated from old-arch/src/analytics-service/export/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Export types and enums
var ExportFormat;
(function (ExportFormat) {
    ExportFormat["CSV"] = "CSV";
    ExportFormat["EXCEL"] = "EXCEL";
    ExportFormat["JSON"] = "JSON";
    ExportFormat["PDF"] = "PDF";
    ExportFormat["XML"] = "XML";
})(ExportFormat || (ExportFormat = {}));
var ExportType;
(function (ExportType) {
    ExportType["DOCUMENTS"] = "DOCUMENTS";
    ExportType["USERS"] = "USERS";
    ExportType["ACTIVITIES"] = "ACTIVITIES";
    ExportType["WORKFLOWS"] = "WORKFLOWS";
    ExportType["ANALYTICS"] = "ANALYTICS";
    ExportType["AUDIT_LOGS"] = "AUDIT_LOGS";
    ExportType["CUSTOM"] = "CUSTOM";
})(ExportType || (ExportType = {}));
var ExportStatus;
(function (ExportStatus) {
    ExportStatus["PENDING"] = "PENDING";
    ExportStatus["PROCESSING"] = "PROCESSING";
    ExportStatus["COMPLETED"] = "COMPLETED";
    ExportStatus["FAILED"] = "FAILED";
    ExportStatus["EXPIRED"] = "EXPIRED";
})(ExportStatus || (ExportStatus = {}));
// Validation schemas
const createExportSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).required(),
    type: joi_1.default.string().valid(...Object.values(ExportType)).required(),
    format: joi_1.default.string().valid(...Object.values(ExportFormat)).required(),
    organizationId: joi_1.default.string().uuid().required(),
    projectId: joi_1.default.string().uuid().optional(),
    filters: joi_1.default.object({
        dateRange: joi_1.default.object({
            startDate: joi_1.default.string().isoDate().required(),
            endDate: joi_1.default.string().isoDate().required()
        }).optional(),
        userIds: joi_1.default.array().items(joi_1.default.string().uuid()).optional(),
        documentTypes: joi_1.default.array().items(joi_1.default.string()).optional(),
        categories: joi_1.default.array().items(joi_1.default.string()).optional(),
        tags: joi_1.default.array().items(joi_1.default.string()).optional(),
        status: joi_1.default.array().items(joi_1.default.string()).optional(),
        includeDeleted: joi_1.default.boolean().default(false),
        includeArchived: joi_1.default.boolean().default(false)
    }).optional(),
    options: joi_1.default.object({
        includeMetadata: joi_1.default.boolean().default(true),
        includeContent: joi_1.default.boolean().default(false),
        includeVersions: joi_1.default.boolean().default(false),
        includeComments: joi_1.default.boolean().default(false),
        includeActivities: joi_1.default.boolean().default(false),
        compression: joi_1.default.boolean().default(false),
        password: joi_1.default.string().min(8).optional(),
        maxRecords: joi_1.default.number().min(1).max(100000).optional()
    }).optional(),
    schedule: joi_1.default.object({
        enabled: joi_1.default.boolean().default(false),
        frequency: joi_1.default.string().valid('daily', 'weekly', 'monthly').optional(),
        dayOfWeek: joi_1.default.number().min(0).max(6).optional(),
        dayOfMonth: joi_1.default.number().min(1).max(31).optional(),
        time: joi_1.default.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional()
    }).optional()
});
/**
 * Create data export handler
 */
async function createDataExport(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create data export started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createExportSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const exportRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(exportRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check export permissions
        const hasExportPermission = await checkExportPermissions(exportRequest.type, user);
        if (!hasExportPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions for this export type" }
            }, request);
        }
        // Check export limits
        const canExport = await checkExportLimits(exportRequest.organizationId);
        if (!canExport.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canExport.reason }
            }, request);
        }
        // Create export
        const exportId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const dataExport = {
            id: exportId,
            name: exportRequest.name,
            type: exportRequest.type,
            format: exportRequest.format,
            status: ExportStatus.PENDING,
            organizationId: exportRequest.organizationId,
            projectId: exportRequest.projectId,
            filters: exportRequest.filters || {},
            options: {
                includeMetadata: true,
                includeContent: false,
                includeVersions: false,
                includeComments: false,
                includeActivities: false,
                compression: false,
                ...exportRequest.options
            },
            schedule: exportRequest.schedule,
            progress: {
                percentage: 0,
                currentStep: 'Initializing'
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('data-exports', dataExport);
        // Start export processing asynchronously
        await processExportAsync(dataExport);
        // Create audit log entry
        await database_1.db.createItem('audit-logs', {
            id: (0, uuid_1.v4)(),
            eventType: 'DATA_EXPORTED',
            userId: user.id,
            organizationId: exportRequest.organizationId,
            description: `Data export created: ${exportRequest.name}`,
            severity: 'MEDIUM',
            resourceType: 'data-export',
            resourceId: exportId,
            details: {
                exportType: exportRequest.type,
                exportFormat: exportRequest.format,
                includeContent: exportRequest.options?.includeContent || false
            },
            timestamp: now,
            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            tenantId: user.tenantId
        });
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "data_export_created",
            userId: user.id,
            organizationId: exportRequest.organizationId,
            projectId: exportRequest.projectId,
            timestamp: now,
            details: {
                exportId,
                exportName: exportRequest.name,
                exportType: exportRequest.type,
                exportFormat: exportRequest.format,
                isScheduled: !!exportRequest.schedule?.enabled
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DataExportCreated',
            aggregateId: exportId,
            aggregateType: 'DataExport',
            version: 1,
            data: {
                export: dataExport,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: exportRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Data export created successfully", {
            correlationId,
            exportId,
            exportName: exportRequest.name,
            exportType: exportRequest.type,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: exportId,
                name: exportRequest.name,
                type: exportRequest.type,
                format: exportRequest.format,
                status: ExportStatus.PENDING,
                estimatedDuration: estimateExportDuration(exportRequest.type, exportRequest.options),
                createdAt: now,
                message: "Data export created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create data export failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get export status handler
 */
async function getExportStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const exportId = request.params.exportId;
    logger_1.logger.info("Get export status started", { correlationId, exportId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!exportId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Export ID is required" }
            }, request);
        }
        // Get export
        const dataExport = await database_1.db.readItem('data-exports', exportId, exportId);
        if (!dataExport) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Export not found" }
            }, request);
        }
        const exportData = dataExport;
        // Check access
        const hasAccess = await checkOrganizationAccess(exportData.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to export" }
            }, request);
        }
        logger_1.logger.info("Export status retrieved successfully", {
            correlationId,
            exportId,
            status: exportData.status,
            progress: exportData.progress?.percentage,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: exportData.id,
                name: exportData.name,
                type: exportData.type,
                format: exportData.format,
                status: exportData.status,
                progress: exportData.progress,
                results: exportData.results,
                createdAt: exportData.createdAt,
                updatedAt: exportData.updatedAt
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get export status failed", {
            correlationId,
            exportId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkExportPermissions(exportType, user) {
    try {
        // Check if user has export permissions based on type
        const sensitiveExports = [ExportType.AUDIT_LOGS, ExportType.USERS];
        if (sensitiveExports.includes(exportType)) {
            return user.roles?.includes('admin') || user.roles?.includes('auditor');
        }
        return true; // Allow other export types for all authenticated users
    }
    catch (error) {
        logger_1.logger.error('Failed to check export permissions', { error, exportType, userId: user.id });
        return false;
    }
}
async function checkExportLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxExportsPerMonth: 5, maxConcurrentExports: 1 },
            'PROFESSIONAL': { maxExportsPerMonth: 50, maxConcurrentExports: 3 },
            'ENTERPRISE': { maxExportsPerMonth: -1, maxConcurrentExports: 10 } // Unlimited monthly
        };
        const limit = limits[tier] || limits['FREE'];
        // Check concurrent exports
        const activeExportsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status IN (@processing, @pending)';
        const activeExportsResult = await database_1.db.queryItems('data-exports', activeExportsQuery, [organizationId, ExportStatus.PROCESSING, ExportStatus.PENDING]);
        const activeExports = Number(activeExportsResult[0]) || 0;
        if (activeExports >= limit.maxConcurrentExports) {
            return {
                allowed: false,
                reason: `Maximum concurrent exports limit reached (${limit.maxConcurrentExports})`
            };
        }
        if (limit.maxExportsPerMonth === -1) {
            return { allowed: true };
        }
        // Check monthly usage
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const monthlyUsageQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
        const usageResult = await database_1.db.queryItems('data-exports', monthlyUsageQuery, [organizationId, startOfMonth.toISOString()]);
        const monthlyUsage = Number(usageResult[0]) || 0;
        if (monthlyUsage >= limit.maxExportsPerMonth) {
            return {
                allowed: false,
                reason: `Monthly export limit reached (${limit.maxExportsPerMonth})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check export limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function estimateExportDuration(exportType, options) {
    // Simplified estimation - in production, use historical data
    const baseTimeMinutes = {
        [ExportType.DOCUMENTS]: 10,
        [ExportType.USERS]: 2,
        [ExportType.ACTIVITIES]: 15,
        [ExportType.WORKFLOWS]: 5,
        [ExportType.ANALYTICS]: 8,
        [ExportType.AUDIT_LOGS]: 20,
        [ExportType.CUSTOM]: 12
    };
    let baseTime = baseTimeMinutes[exportType] || 10;
    // Adjust based on options
    if (options?.includeContent)
        baseTime *= 2;
    if (options?.includeVersions)
        baseTime *= 1.5;
    if (options?.includeComments)
        baseTime *= 1.3;
    if (options?.includeActivities)
        baseTime *= 1.4;
    return `${Math.ceil(baseTime)} minutes`;
}
async function processExportAsync(dataExport) {
    try {
        // Update status to processing
        const updatedExport = {
            ...dataExport,
            id: dataExport.id,
            status: ExportStatus.PROCESSING,
            progress: {
                percentage: 10,
                currentStep: 'Collecting data'
            },
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('data-exports', updatedExport);
        // Simulate export processing (in production, this would be actual export generation)
        setTimeout(async () => {
            try {
                const exportData = await generateExportData(dataExport);
                const downloadUrl = await uploadExportToStorage(dataExport, exportData);
                const completedExport = {
                    ...updatedExport,
                    id: dataExport.id,
                    status: ExportStatus.COMPLETED,
                    progress: {
                        percentage: 100,
                        currentStep: 'Completed'
                    },
                    results: {
                        downloadUrl,
                        fileSize: exportData.length,
                        recordCount: exportData.recordCount || 0,
                        generatedAt: new Date().toISOString(),
                        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
                    },
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('data-exports', completedExport);
                logger_1.logger.info('Export processed successfully', { exportId: dataExport.id });
            }
            catch (error) {
                const failedExport = {
                    ...updatedExport,
                    id: dataExport.id,
                    status: ExportStatus.FAILED,
                    progress: {
                        percentage: 0,
                        currentStep: 'Failed'
                    },
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('data-exports', failedExport);
                logger_1.logger.error('Export processing failed', { exportId: dataExport.id, error });
            }
        }, 10000); // 10 second delay for demo
    }
    catch (error) {
        logger_1.logger.error('Failed to start export processing', { exportId: dataExport.id, error });
    }
}
async function generateExportData(dataExport) {
    try {
        logger_1.logger.info('Starting production data export', {
            exportId: dataExport.id,
            exportType: dataExport.type,
            format: dataExport.format,
            organizationId: dataExport.organizationId
        });
        let exportData;
        // Production export implementation based on type
        const exportType = dataExport.type;
        switch (exportType) {
            case ExportType.DOCUMENTS:
            case 'documents':
                exportData = await exportDocuments(dataExport);
                break;
            case ExportType.USERS:
            case 'users':
                exportData = await exportUsers(dataExport);
                break;
            case ExportType.ANALYTICS:
            case 'analytics':
                exportData = await exportAnalytics(dataExport);
                break;
            case 'workflows':
                exportData = await exportWorkflows(dataExport);
                break;
            case 'projects':
                exportData = await exportProjects(dataExport);
                break;
            case 'activities':
                exportData = await exportActivities(dataExport);
                break;
            case 'templates':
                exportData = await exportTemplates(dataExport);
                break;
            default:
                throw new Error(`Unsupported export type: ${dataExport.type}`);
        }
        // Format data based on requested format
        const formattedData = await formatExportData(exportData, dataExport.format);
        // Add export metadata
        const exportPackage = {
            exportId: dataExport.id,
            exportName: dataExport.name,
            exportType: dataExport.type,
            format: dataExport.format,
            generatedAt: new Date().toISOString(),
            organizationId: dataExport.organizationId,
            projectId: dataExport.projectId,
            metadata: {
                recordCount: Array.isArray(exportData) ? exportData.length : Object.keys(exportData).length,
                dataSize: JSON.stringify(exportData).length,
                filters: dataExport.filters,
                dateRange: dataExport.filters?.dateRange
            },
            data: formattedData
        };
        logger_1.logger.info('Production data export completed', {
            exportId: dataExport.id,
            recordCount: exportPackage.metadata.recordCount,
            dataSize: exportPackage.metadata.dataSize,
            format: dataExport.format
        });
        return Buffer.from(JSON.stringify(exportPackage, null, 2));
    }
    catch (error) {
        logger_1.logger.error('Production data export failed', {
            error: error instanceof Error ? error.message : String(error),
            exportId: dataExport.id,
            exportType: dataExport.type
        });
        throw error;
    }
}
async function uploadExportToStorage(dataExport, data) {
    try {
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient("exports");
        const fileName = `${dataExport.id}-${Date.now()}.${dataExport.format.toLowerCase()}`;
        const blobClient = containerClient.getBlobClient(fileName);
        await blobClient.getBlockBlobClient().upload(data, data.length);
        return blobClient.url;
    }
    catch (error) {
        logger_1.logger.error('Failed to upload export to storage', { exportId: dataExport.id, error });
        throw error;
    }
}
/**
 * Export documents data
 */
async function exportDocuments(dataExport) {
    try {
        let documentsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        // Add project filter if specified
        if (dataExport.projectId) {
            documentsQuery += ' AND c.projectId = @projectId';
            parameters.push({ name: '@projectId', value: dataExport.projectId });
        }
        // Add date range filter if specified
        const dateRange = dataExport.filters?.dateRange;
        if (dateRange?.startDate) {
            documentsQuery += ' AND c.createdAt >= @startDate';
            parameters.push({ name: '@startDate', value: dateRange.startDate });
        }
        if (dateRange?.endDate) {
            documentsQuery += ' AND c.createdAt <= @endDate';
            parameters.push({ name: '@endDate', value: dateRange.endDate });
        }
        // Add content type filter if specified
        if (dataExport.filters?.contentTypes && dataExport.filters.contentTypes.length > 0) {
            documentsQuery += ' AND c.contentType IN (@contentTypes)';
            parameters.push({ name: '@contentTypes', value: dataExport.filters.contentTypes });
        }
        documentsQuery += ' ORDER BY c.createdAt DESC';
        const documents = await database_1.db.queryItems('documents', documentsQuery, parameters);
        // Sanitize sensitive data for export
        const sanitizedDocuments = documents.map(doc => {
            const docData = doc;
            return {
                id: docData.id,
                name: docData.name,
                description: docData.description,
                contentType: docData.contentType,
                size: docData.size,
                createdAt: docData.createdAt,
                updatedAt: docData.updatedAt,
                createdBy: docData.createdBy,
                projectId: docData.projectId,
                organizationId: docData.organizationId,
                tags: docData.tags || [],
                status: docData.status,
                version: docData.version,
                accessCount: docData.accessCount || 0,
                lastAccessedAt: docData.lastAccessedAt,
                // Exclude sensitive fields like blobPath, sasTokens, etc.
                exportMetadata: {
                    exportedAt: new Date().toISOString(),
                    originalSize: docData.size,
                    hasContent: !!docData.blobPath
                }
            };
        });
        logger_1.logger.info('Documents export data collected', {
            organizationId: dataExport.organizationId,
            projectId: dataExport.projectId,
            documentCount: sanitizedDocuments.length
        });
        return sanitizedDocuments;
    }
    catch (error) {
        logger_1.logger.error('Failed to export documents', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Export users data
 */
async function exportUsers(dataExport) {
    try {
        const usersQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        const users = await database_1.db.queryItems('users', usersQuery, parameters);
        // Sanitize sensitive data for export
        const sanitizedUsers = users.map(user => {
            const userData = user;
            return {
                id: userData.id,
                email: userData.email,
                name: userData.name,
                firstName: userData.firstName,
                lastName: userData.lastName,
                role: userData.role,
                status: userData.status,
                createdAt: userData.createdAt,
                updatedAt: userData.updatedAt,
                lastLoginAt: userData.lastLoginAt,
                organizationId: userData.organizationId,
                preferences: userData.preferences,
                // Exclude sensitive fields like password, apiKeys, refreshTokens, etc.
                exportMetadata: {
                    exportedAt: new Date().toISOString(),
                    sensitiveDataRemoved: true
                }
            };
        });
        logger_1.logger.info('Users export data collected', {
            organizationId: dataExport.organizationId,
            userCount: sanitizedUsers.length
        });
        return sanitizedUsers;
    }
    catch (error) {
        logger_1.logger.error('Failed to export users', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Export analytics data
 */
async function exportAnalytics(dataExport) {
    try {
        let analyticsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        // Add project filter if specified
        if (dataExport.projectId) {
            analyticsQuery += ' AND c.projectId = @projectId';
            parameters.push({ name: '@projectId', value: dataExport.projectId });
        }
        // Add date range filter if specified
        const dateRange = dataExport.filters?.dateRange;
        if (dateRange?.startDate) {
            analyticsQuery += ' AND c.timestamp >= @startDate';
            parameters.push({ name: '@startDate', value: dateRange.startDate });
        }
        if (dateRange?.endDate) {
            analyticsQuery += ' AND c.timestamp <= @endDate';
            parameters.push({ name: '@endDate', value: dateRange.endDate });
        }
        analyticsQuery += ' ORDER BY c.timestamp DESC';
        // Export analytics events
        const analyticsEvents = await database_1.db.queryItems('analytics-events', analyticsQuery, parameters);
        // Export aggregated analytics
        const aggregatedAnalytics = await database_1.db.queryItems('analytics-aggregated', analyticsQuery, parameters);
        const analyticsData = {
            events: analyticsEvents,
            aggregated: aggregatedAnalytics,
            summary: {
                totalEvents: analyticsEvents.length,
                totalAggregated: aggregatedAnalytics.length,
                dateRange: dataExport.filters?.dateRange,
                exportedAt: new Date().toISOString()
            }
        };
        logger_1.logger.info('Analytics export data collected', {
            organizationId: dataExport.organizationId,
            projectId: dataExport.projectId,
            eventsCount: analyticsEvents.length,
            aggregatedCount: aggregatedAnalytics.length
        });
        return analyticsData;
    }
    catch (error) {
        logger_1.logger.error('Failed to export analytics', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Export workflows data
 */
async function exportWorkflows(dataExport) {
    try {
        let workflowsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        if (dataExport.projectId) {
            workflowsQuery += ' AND c.projectId = @projectId';
            parameters.push({ name: '@projectId', value: dataExport.projectId });
        }
        const workflows = await database_1.db.queryItems('workflows', workflowsQuery, parameters);
        // Include workflow execution history if requested
        const workflowsWithHistory = await Promise.all(workflows.map(async (workflow) => {
            const workflowData = workflow;
            let executionsQuery = 'SELECT * FROM c WHERE c.workflowId = @workflowId';
            const executionParams = [{ name: '@workflowId', value: workflowData.id }];
            const dateRange = dataExport.filters?.dateRange;
            if (dateRange?.startDate) {
                executionsQuery += ' AND c.startedAt >= @startDate';
                executionParams.push({ name: '@startDate', value: dateRange.startDate });
            }
            if (dateRange?.endDate) {
                executionsQuery += ' AND c.startedAt <= @endDate';
                executionParams.push({ name: '@endDate', value: dateRange.endDate });
            }
            const executions = await database_1.db.queryItems('workflow-executions', executionsQuery, executionParams);
            return {
                ...(workflowData),
                executionHistory: executions,
                exportMetadata: {
                    exportedAt: new Date().toISOString(),
                    executionCount: executions.length
                }
            };
        }));
        logger_1.logger.info('Workflows export data collected', {
            organizationId: dataExport.organizationId,
            projectId: dataExport.projectId,
            workflowCount: workflows.length
        });
        return workflowsWithHistory;
    }
    catch (error) {
        logger_1.logger.error('Failed to export workflows', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Export projects data
 */
async function exportProjects(dataExport) {
    try {
        const projectsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        const projects = await database_1.db.queryItems('projects', projectsQuery, parameters);
        // Include project statistics
        const projectsWithStats = await Promise.all(projects.map(async (project) => {
            const projectData = project;
            // Get document count
            const docQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const docCount = await database_1.db.queryItems('documents', docQuery, [{ name: '@projectId', value: projectData.id }]);
            // Get workflow count
            const workflowQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const workflowCount = await database_1.db.queryItems('workflows', workflowQuery, [{ name: '@projectId', value: projectData.id }]);
            return {
                ...(projectData),
                statistics: {
                    documentCount: docCount[0] || 0,
                    workflowCount: workflowCount[0] || 0
                },
                exportMetadata: {
                    exportedAt: new Date().toISOString()
                }
            };
        }));
        logger_1.logger.info('Projects export data collected', {
            organizationId: dataExport.organizationId,
            projectCount: projects.length
        });
        return projectsWithStats;
    }
    catch (error) {
        logger_1.logger.error('Failed to export projects', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Export activities data
 */
async function exportActivities(dataExport) {
    try {
        let activitiesQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        if (dataExport.projectId) {
            activitiesQuery += ' AND c.projectId = @projectId';
            parameters.push({ name: '@projectId', value: dataExport.projectId });
        }
        const dateRange = dataExport.filters?.dateRange;
        if (dateRange?.startDate) {
            activitiesQuery += ' AND c.timestamp >= @startDate';
            parameters.push({ name: '@startDate', value: dateRange.startDate });
        }
        if (dateRange?.endDate) {
            activitiesQuery += ' AND c.timestamp <= @endDate';
            parameters.push({ name: '@endDate', value: dateRange.endDate });
        }
        activitiesQuery += ' ORDER BY c.timestamp DESC';
        const activities = await database_1.db.queryItems('activities', activitiesQuery, parameters);
        logger_1.logger.info('Activities export data collected', {
            organizationId: dataExport.organizationId,
            projectId: dataExport.projectId,
            activityCount: activities.length
        });
        return activities;
    }
    catch (error) {
        logger_1.logger.error('Failed to export activities', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Export templates data
 */
async function exportTemplates(dataExport) {
    try {
        const templatesQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [{ name: '@orgId', value: dataExport.organizationId }];
        const templates = await database_1.db.queryItems('templates', templatesQuery, parameters);
        logger_1.logger.info('Templates export data collected', {
            organizationId: dataExport.organizationId,
            templateCount: templates.length
        });
        return templates;
    }
    catch (error) {
        logger_1.logger.error('Failed to export templates', {
            error: error instanceof Error ? error.message : String(error),
            organizationId: dataExport.organizationId
        });
        throw error;
    }
}
/**
 * Format export data based on requested format
 */
async function formatExportData(data, format) {
    try {
        switch (format.toLowerCase()) {
            case 'json':
                return data;
            case 'csv':
                return convertToCSV(data);
            case 'excel':
                return await convertToExcel(data);
            case 'xml':
                return convertToXML(data);
            default:
                logger_1.logger.warn('Unsupported export format, defaulting to JSON', { format });
                return data;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to format export data', {
            error: error instanceof Error ? error.message : String(error),
            format
        });
        return data; // Return original data if formatting fails
    }
}
/**
 * Convert data to CSV format
 */
function convertToCSV(data) {
    try {
        if (!Array.isArray(data)) {
            data = [data];
        }
        if (data.length === 0) {
            return '';
        }
        // Get all unique keys from all objects
        const allKeys = new Set();
        data.forEach((item) => {
            Object.keys(item).forEach(key => allKeys.add(key));
        });
        const headers = Array.from(allKeys);
        const csvRows = [headers.join(',')];
        data.forEach((item) => {
            const row = headers.map(header => {
                const value = item[header];
                if (value === null || value === undefined) {
                    return '';
                }
                // Escape commas and quotes in CSV
                const stringValue = String(value).replace(/"/g, '""');
                return `"${stringValue}"`;
            });
            csvRows.push(row.join(','));
        });
        return csvRows.join('\n');
    }
    catch (error) {
        logger_1.logger.error('Failed to convert to CSV', {
            error: error instanceof Error ? error.message : String(error)
        });
        return JSON.stringify(data);
    }
}
/**
 * Convert data to Excel format using ExcelJS (production implementation)
 */
async function convertToExcel(data) {
    const ExcelJS = require('exceljs');
    try {
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'HEPZ Platform';
        workbook.lastModifiedBy = 'HEPZ Export Service';
        workbook.created = new Date();
        workbook.modified = new Date();
        // Create worksheet
        const worksheet = workbook.addWorksheet('Export Data', {
            properties: {
                tabColor: { argb: 'FF0066CC' }
            }
        });
        if (Array.isArray(data) && data.length > 0) {
            // Get headers from first object
            const headers = Object.keys(data[0]);
            // Add header row with styling
            const headerRow = worksheet.addRow(headers);
            headerRow.eachCell((cell, _colNumber) => {
                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF0066CC' }
                };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
            });
            // Add data rows
            data.forEach((item) => {
                const row = worksheet.addRow(headers.map(header => {
                    const value = item[header];
                    // Handle different data types
                    if (value instanceof Date) {
                        return value.toISOString();
                    }
                    else if (typeof value === 'object' && value !== null) {
                        return JSON.stringify(value);
                    }
                    else {
                        return value;
                    }
                }));
                // Style data rows
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            });
            // Auto-fit columns
            worksheet.columns.forEach((column, index) => {
                const header = headers[index];
                let maxLength = header.length;
                // Find the maximum length in this column
                data.forEach((item) => {
                    const value = String(item[header] || '');
                    if (value.length > maxLength) {
                        maxLength = Math.min(value.length, 50); // Cap at 50 characters
                    }
                });
                column.width = Math.max(maxLength + 2, 10); // Minimum width of 10
            });
            // Add summary row if data has numeric fields
            const numericFields = headers.filter(header => data.some((item) => typeof item[header] === 'number'));
            if (numericFields.length > 0) {
                worksheet.addRow([]); // Empty row
                const summaryRow = worksheet.addRow(['SUMMARY', ...headers.slice(1).map(header => {
                        if (numericFields.includes(header)) {
                            const sum = data.reduce((acc, item) => acc + (typeof item[header] === 'number' ? item[header] : 0), 0);
                            return `SUM: ${sum}`;
                        }
                        return '';
                    })]);
                summaryRow.eachCell((cell) => {
                    cell.font = { bold: true };
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFF0F0F0' }
                    };
                });
            }
        }
        else {
            // Handle empty data
            worksheet.addRow(['No data available']);
        }
        // Generate buffer
        const buffer = await workbook.xlsx.writeBuffer();
        logger_1.logger.info('Excel file generated successfully', {
            rowCount: Array.isArray(data) ? data.length : 0,
            worksheetName: 'Export Data'
        });
        return buffer;
    }
    catch (error) {
        logger_1.logger.error('Failed to generate Excel file', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw new Error('Excel generation failed');
    }
}
/**
 * Convert data to XML format
 */
function convertToXML(data) {
    try {
        function objectToXML(obj, rootName = 'item') {
            let xml = `<${rootName}>`;
            for (const [key, value] of Object.entries(obj)) {
                if (value === null || value === undefined) {
                    xml += `<${key}></${key}>`;
                }
                else if (typeof value === 'object' && !Array.isArray(value)) {
                    xml += `<${key}>${objectToXML(value, 'item')}</${key}>`;
                }
                else if (Array.isArray(value)) {
                    xml += `<${key}>`;
                    value.forEach(item => {
                        xml += objectToXML(item, 'item');
                    });
                    xml += `</${key}>`;
                }
                else {
                    // Escape XML special characters
                    const escapedValue = String(value)
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/"/g, '&quot;')
                        .replace(/'/g, '&apos;');
                    xml += `<${key}>${escapedValue}</${key}>`;
                }
            }
            xml += `</${rootName}>`;
            return xml;
        }
        let xmlOutput = '<?xml version="1.0" encoding="UTF-8"?>\n<export>\n';
        if (Array.isArray(data)) {
            data.forEach(item => {
                xmlOutput += objectToXML(item, 'record') + '\n';
            });
        }
        else {
            xmlOutput += objectToXML(data, 'record') + '\n';
        }
        xmlOutput += '</export>';
        return xmlOutput;
    }
    catch (error) {
        logger_1.logger.error('Failed to convert to XML', {
            error: error instanceof Error ? error.message : String(error)
        });
        return JSON.stringify(data);
    }
}
// Register functions
functions_1.app.http('data-export-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'exports',
    handler: createDataExport
});
functions_1.app.http('data-export-status', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'exports/{exportId}/status',
    handler: getExportStatus
});
//# sourceMappingURL=data-export.js.map