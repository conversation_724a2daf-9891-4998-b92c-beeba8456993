/**
 * Comprehensive Document Management Function
 * Demonstrates the complete document processing pipeline with AI services
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { enhancedDocumentIntelligence } from '../shared/services/enhanced-document-intelligence';
import { aiServices } from '../shared/services/ai-services';
import { ragService } from '../shared/services/rag-service';


// Request validation schema
const documentAnalysisSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  analysisOptions: Joi.object({
    extractLayout: Joi.boolean().default(true),
    extractTables: Joi.boolean().default(true),
    extractKeyValuePairs: Joi.boolean().default(true),
    extractEntities: Joi.boolean().default(true),
    generateSummary: Joi.boolean().default(true),
    performClassification: Joi.boolean().default(true),
    analyzeSentiment: Joi.boolean().default(false),
    indexForRAG: Joi.boolean().default(true),
    generateInsights: Joi.boolean().default(true)
  }).default({}),
  modelId: Joi.string().optional()
});

/**
 * Comprehensive document analysis handler
 */
export async function comprehensiveDocumentAnalysis(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = uuidv4();
  const startTime = Date.now();

  try {
    logger.info('Comprehensive document analysis started', {
      correlationId,
      method: request.method,
      url: request.url
    });

    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Unauthorized", message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse and validate request body
    let requestBody;
    try {
      const bodyText = await request.text();
      requestBody = JSON.parse(bodyText);
    } catch (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid JSON in request body" }
      }, request);
    }

    const { error, value: analysisRequest } = documentAnalysisSchema.validate(requestBody);
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: "Validation failed", 
          details: error.details.map(d => d.message) 
        }
      }, request);
    }

    // Get document
    const document = await db.readItem('documents', analysisRequest.documentId, analysisRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.organizationId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Perform comprehensive analysis
    const analysisResults = await performComprehensiveAnalysis(
      analysisRequest.documentId,
      analysisRequest.analysisOptions,
      analysisRequest.modelId,
      user
    );

    const response = {
      documentId: analysisRequest.documentId,
      analysisId: analysisResults.analysisId,
      documentIntelligence: analysisResults.documentIntelligence,
      aiAnalysis: analysisResults.aiAnalysis,
      ragIndexing: analysisResults.ragIndexing,
      insights: analysisResults.insights,
      processingTime: Date.now() - startTime,
      success: true
    };

    logger.info('Comprehensive document analysis completed', {
      correlationId,
      documentId: analysisRequest.documentId,
      processingTime: response.processingTime,
      extractedTextLength: analysisResults.documentIntelligence?.extractedText?.length || 0,
      tablesFound: analysisResults.documentIntelligence?.tables?.length || 0,
      entitiesFound: analysisResults.documentIntelligence?.entities?.length || 0
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    logger.error('Comprehensive document analysis failed', {
      correlationId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { 
        error: "Internal server error",
        correlationId
      }
    }, request);
  }
}

/**
 * Perform comprehensive document analysis using all available services
 */
async function performComprehensiveAnalysis(
  documentId: string,
  options: any,
  modelId?: string,
  user?: any
): Promise<any> {
  const analysisId = uuidv4();
  const startTime = Date.now();

  try {
    logger.info('Starting comprehensive analysis', {
      documentId,
      analysisId,
      options
    });

    // Step 1: Get document from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );

    const document = await db.readItem('documents', documentId, documentId);
    const blobClient = containerClient.getBlobClient((document as any).blobName);
    const downloadResponse = await blobClient.download();

    if (!downloadResponse.readableStreamBody) {
      throw new Error('Failed to download document content');
    }

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of downloadResponse.readableStreamBody) {
      chunks.push(Buffer.from(chunk));
    }
    const documentBuffer = Buffer.concat(chunks);

    // Step 2: Enhanced Document Intelligence Analysis
    let documentIntelligence = null;
    if (options.extractLayout || options.extractTables || options.extractKeyValuePairs || options.extractEntities) {
      const features = [];
      if (options.extractTables) features.push('tables');
      if (options.extractKeyValuePairs) features.push('keyValuePairs');
      if (options.extractEntities) features.push('entities');

      documentIntelligence = await enhancedDocumentIntelligence.analyzeDocument(
        documentBuffer,
        documentId,
        modelId,
        features
      );

      logger.info('Document Intelligence analysis completed', {
        documentId,
        analysisId,
        extractedTextLength: documentIntelligence.extractedText.length,
        tablesFound: documentIntelligence.tables.length,
        entitiesFound: documentIntelligence.entities.length
      });
    }

    // Step 3: AI Analysis using DeepSeek R1 and Llama
    let aiAnalysis = null;
    if (documentIntelligence?.extractedText && (options.generateSummary || options.performClassification || options.analyzeSentiment)) {
      const analysisPrompts = [];

      if (options.performClassification) {
        analysisPrompts.push('classification');
      }
      if (options.generateSummary) {
        analysisPrompts.push('summarization');
      }
      if (options.analyzeSentiment) {
        analysisPrompts.push('sentiment');
      }

      // Use DeepSeek R1 for reasoning and analysis
      const reasoningPrompt = `Perform comprehensive analysis of this document:

Document Content:
${documentIntelligence.extractedText.substring(0, 4000)}...

Analysis Tasks:
${analysisPrompts.map(task => `- ${task}`).join('\n')}

Provide detailed analysis with reasoning for each task.`;

      const reasoningResult = await aiServices.reason(reasoningPrompt, [], {
        systemPrompt: 'You are an expert document analyst. Provide comprehensive, structured analysis with clear reasoning.',
        temperature: 0.3,
        maxTokens: 2000
      });

      // Use Llama for content generation (summary, insights)
      if (options.generateSummary) {
        const summaryPrompt = `Create a comprehensive summary of this document:

${documentIntelligence.extractedText}

Generate a well-structured summary that captures the key points, main themes, and important details.`;

        const summaryResult = await aiServices.generateContent(summaryPrompt, {
          systemPrompt: 'You are an expert at creating clear, comprehensive document summaries.',
          temperature: 0.4,
          maxTokens: 1000
        });

        aiAnalysis = {
          reasoning: reasoningResult.content,
          summary: summaryResult.content,
          confidence: (reasoningResult.confidence + summaryResult.confidence) / 2,
          tokensUsed: reasoningResult.tokensUsed + summaryResult.tokensUsed
        };
      } else {
        aiAnalysis = {
          reasoning: reasoningResult.content,
          confidence: reasoningResult.confidence,
          tokensUsed: reasoningResult.tokensUsed
        };
      }

      logger.info('AI analysis completed', {
        documentId,
        analysisId,
        confidence: aiAnalysis.confidence,
        tokensUsed: aiAnalysis.tokensUsed
      });
    }

    // Step 4: RAG Indexing
    let ragIndexing = null;
    if (options.indexForRAG && documentIntelligence?.extractedText) {
      try {
        await ragService.indexDocument({
          documentId,
          content: documentIntelligence.extractedText,
          metadata: {
            analysisId,
            modelUsed: documentIntelligence.modelUsed,
            confidence: documentIntelligence.confidence,
            hasLayout: documentIntelligence.layout.pages.length > 0,
            tablesCount: documentIntelligence.tables.length,
            keyValuePairsCount: documentIntelligence.keyValuePairs.length,
            entitiesCount: documentIntelligence.entities.length
          }
        });

        ragIndexing = {
          indexed: true,
          contentLength: documentIntelligence.extractedText.length,
          chunkCount: Math.ceil(documentIntelligence.extractedText.split(' ').length / 1000)
        };

        logger.info('RAG indexing completed', {
          documentId,
          analysisId,
          contentLength: ragIndexing.contentLength,
          chunkCount: ragIndexing.chunkCount
        });
      } catch (error) {
        logger.warn('RAG indexing failed', {
          documentId,
          analysisId,
          error: error instanceof Error ? error.message : String(error)
        });
        ragIndexing = { indexed: false, error: error instanceof Error ? error.message : String(error) };
      }
    }

    // Step 5: Generate Insights
    let insights = null;
    if (options.generateInsights && documentIntelligence?.extractedText) {
      const insightsPrompt = `Generate business insights for this document:

Document Type: ${documentIntelligence.metadata.title || 'Unknown'}
Content Length: ${documentIntelligence.extractedText.length} characters
Tables: ${documentIntelligence.tables.length}
Key-Value Pairs: ${documentIntelligence.keyValuePairs.length}
Entities: ${documentIntelligence.entities.length}

Content Sample:
${documentIntelligence.extractedText.substring(0, 1000)}...

Provide insights about:
1. Document purpose and business value
2. Data quality and completeness
3. Potential automation opportunities
4. Compliance considerations
5. Recommendations for processing`;

      const insightsResult = await aiServices.reason(insightsPrompt, [], {
        systemPrompt: 'You are a business analyst expert. Provide actionable insights about documents.',
        temperature: 0.4,
        maxTokens: 1500
      });

      insights = {
        content: insightsResult.content,
        reasoning: insightsResult.reasoning,
        confidence: insightsResult.confidence,
        tokensUsed: insightsResult.tokensUsed
      };

      logger.info('Insights generation completed', {
        documentId,
        analysisId,
        confidence: insights.confidence
      });
    }

    // Store comprehensive analysis results
    const analysisRecord = {
      id: analysisId,
      documentId,
      analysisType: 'comprehensive',
      documentIntelligence,
      aiAnalysis,
      ragIndexing,
      insights,
      options,
      processingTime: Date.now() - startTime,
      createdAt: new Date().toISOString(),
      createdBy: user?.id,
      organizationId: user?.organizationId,
      tenantId: user?.tenantId
    };

    await db.createItem('comprehensive-analyses', analysisRecord);

    return {
      analysisId,
      documentIntelligence,
      aiAnalysis,
      ragIndexing,
      insights
    };

  } catch (error) {
    logger.error('Comprehensive analysis failed', {
      documentId,
      analysisId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Register function
app.http('comprehensive-document-analysis', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/comprehensive-analysis',
  handler: comprehensiveDocumentAnalysis
});
