"use strict";
/**
 * Main entry point for the Azure Functions application
 * This file exports the app instance for the Azure Functions runtime
 */
Object.defineProperty(exports, "__esModule", { value: true });
// Load environment variables first
require("./env");
// Import the app instance
const functions_1 = require("@azure/functions");
// Configure the app for Azure Functions v4
functions_1.app.setup({
    enableHttpStream: true,
});
// Functions will be registered below
// Import all function modules to register them
require("./functions/Productionsample");
require("./functions/health");
// import './functions/document-upload'; // File doesn't exist
require("./functions/document-retrieve");
require("./functions/document-processing");
require("./functions/document-versions");
require("./functions/document-comments");
require("./functions/document-share");
require("./functions/document-sign");
// import './functions/document-specialized-processing'; // File doesn't exist
require("./functions/workflow-management");
require("./functions/workflow-execution");
require("./functions/workflow-templates");
require("./functions/user-management");
require("./functions/auth");
require("./functions/user-auth-operations");
require("./functions/user-permissions");
require("./functions/analytics");
require("./functions/organization-create");
require("./functions/organization-list");
require("./functions/organization-manage");
require("./functions/project-create");
require("./functions/project-list");
require("./functions/project-manage");
require("./functions/notification-send");
require("./functions/notification-list");
require("./functions/notification-mark-read");
require("./functions/search");
require("./functions/document-enhance");
require("./functions/document-transform");
require("./functions/workflow-execution-advanced");
require("./functions/user-tenants");
require("./functions/ai-document-analysis");
require("./functions/template-management");
require("./functions/permission-management");
require("./functions/document-collaboration");
require("./functions/audit-log");
require("./functions/webhook-management");
require("./functions/api-key-management");
require("./functions/advanced-analytics");
require("./functions/real-time-messaging");
require("./functions/email-automation");
require("./functions/mobile-api");
require("./functions/ai-model-training");
require("./functions/performance-monitoring");
require("./functions/cloud-storage-integration");
require("./functions/event-grid-handlers");
require("./functions/timer-functions");
require("./functions/blob-triggers");
require("./functions/queue-handlers");
require("./functions/service-bus-handlers");
require("./functions/notification-hub-integration");
require("./functions/lemonsqueezy-webhooks");
// Import newly migrated functions
require("./functions/document-complete-content");
require("./functions/user-personalization");
require("./functions/ai-intelligent-search");
require("./functions/organization-members-invite");
require("./functions/search-advanced");
require("./functions/ai-orchestration-hub");
require("./functions/workflow-template-create");
require("./functions/template-generate");
require("./functions/organization-teams-create");
require("./functions/workflow-execution-start");
require("./functions/organization-billing");
require("./functions/integration-create");
require("./functions/webhook-delivery");
require("./functions/workflow-monitoring");
require("./functions/document-versioning");
require("./functions/api-key-validation");
require("./functions/user-profile-management");
require("./functions/user-preferences");
require("./functions/project-members-management");
require("./functions/project-analytics");
require("./functions/document-approval");
require("./functions/notification-tracking");
require("./functions/notification-preferences-management");
require("./functions/organization-settings");
require("./functions/organization-analytics");
require("./functions/project-settings");
require("./functions/ai-batch-processing");
require("./functions/ai-smart-form-processing");
require("./functions/document-templates");
require("./functions/document-metadata-management");
require("./functions/audit-logging");
require("./functions/security-monitoring");
require("./functions/real-time-collaboration");
require("./functions/advanced-commenting");
require("./functions/system-monitoring");
require("./functions/custom-reports");
require("./functions/dashboard-management");
require("./functions/data-export");
require("./functions/search-indexing");
require("./functions/external-api-management");
require("./functions/workflow-scheduling");
require("./functions/document-upload");
require("./functions/user-activity-tracking");
require("./functions/backup-management");
require("./functions/cache-management");
require("./functions/data-migration");
require("./functions/classification-service");
require("./functions/email-service");
require("./functions/logging-service");
require("./functions/subscription-management");
// import './functions/file-processing'; // File doesn't exist
require("./functions/tenant-management");
require("./functions/feature-flags");
require("./functions/compliance-management");
require("./functions/data-encryption");
require("./functions/api-rate-limiting");
require("./functions/health-monitoring");
require("./functions/system-configuration");
require("./functions/metrics-collection");
require("./functions/predictive-analytics");
require("./functions/business-intelligence");
require("./functions/push-notifications");
require("./functions/document-archiving");
require("./functions/workflow-automation");
require("./functions/advanced-permissions");
// import './functions/document-intelligence'; // File doesn't exist
require("./functions/enterprise-integration");
// Import additional missing functions
require("./functions/rag-query");
require("./functions/comprehensive-document-management");
require("./functions/cache-warming-scheduler");
require("./functions/event-grid-custom-trigger");
require("./functions/event-grid-storage-trigger");
// Initialize enhanced Azure services
const redis_1 = require("./shared/services/redis");
const signalr_1 = require("./shared/services/signalr");
const service_bus_1 = require("./shared/services/service-bus");
// Initialize service instances
const signalREnhanced = signalr_1.SignalREnhancedService.getInstance();
const serviceBusEnhanced = service_bus_1.ServiceBusEnhancedService.getInstance();
// Initialize all services
async function initializeServices() {
    try {
        console.log('🚀 Initializing enhanced Azure services...');
        // Initialize Redis with enhanced features
        await redis_1.redis.initialize();
        console.log('✅ Redis Enhanced Service initialized');
        // Initialize SignalR with enhanced features
        await signalREnhanced.initialize();
        console.log('✅ SignalR Enhanced Service initialized');
        // Initialize Service Bus with enhanced features
        await serviceBusEnhanced.initialize();
        console.log('✅ Service Bus Enhanced Service initialized');
        // Event Grid Integration Service is already initialized
        console.log('✅ Event Grid Integration Service initialized');
        console.log('🎉 All enhanced Azure services initialized successfully!');
        console.log('📊 Enhanced features include:');
        console.log('   - Redis: Distributed locking, pub/sub, session management, clustering');
        console.log('   - Service Bus: Dead letter handling, circuit breaker, batch processing');
        console.log('   - SignalR: Connection management, group management, cross-instance scaling');
        console.log('   - Event Grid: Advanced filtering, batching, schema validation');
    }
    catch (error) {
        console.error('❌ Failed to initialize enhanced Azure services:', error);
    }
}
// Initialize services
initializeServices();
// Export the app instance for the Azure Functions runtime
exports.default = functions_1.app;
//# sourceMappingURL=index.js.map