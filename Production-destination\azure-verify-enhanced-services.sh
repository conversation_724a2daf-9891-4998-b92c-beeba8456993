#!/bin/bash

# Azure Enhanced Services Verification Script
# This script verifies that all Event Grid, Redis, and Service Bus resources are properly configured

# Configuration variables
RESOURCE_GROUP="docucontext"
SERVICE_BUS_NAMESPACE="hepzbackend"
REDIS_NAME="hepzbackend"
EVENT_GRID_TOPIC="hepz-events"
FUNCTION_APP_NAME="hepzlogic"

echo "🔍 Verifying Azure Enhanced Services Configuration..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a resource exists
check_resource() {
    local resource_type=$1
    local resource_name=$2
    local check_command=$3
    
    echo -n "Checking $resource_type '$resource_name'... "
    
    if eval $check_command > /dev/null 2>&1; then
        echo -e "${GREEN}✅ EXISTS${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT FOUND${NC}"
        return 1
    fi
}

# Function to check Service Bus queue/topic
check_servicebus_queue() {
    local queue_name=$1
    check_resource "Service Bus Queue" "$queue_name" \
        "az servicebus queue show --resource-group $RESOURCE_GROUP --namespace-name $SERVICE_BUS_NAMESPACE --name $queue_name --query name"
}

check_servicebus_topic() {
    local topic_name=$1
    check_resource "Service Bus Topic" "$topic_name" \
        "az servicebus topic show --resource-group $RESOURCE_GROUP --namespace-name $SERVICE_BUS_NAMESPACE --name $topic_name --query name"
}

check_servicebus_subscription() {
    local topic_name=$1
    local subscription_name=$2
    check_resource "Service Bus Subscription" "$topic_name/$subscription_name" \
        "az servicebus topic subscription show --resource-group $RESOURCE_GROUP --namespace-name $SERVICE_BUS_NAMESPACE --topic-name $topic_name --name $subscription_name --query name"
}

# 1. Verify Service Bus Resources
echo -e "\n${BLUE}📨 Verifying Service Bus Resources...${NC}"
echo "======================================"

# Check Service Bus Namespace
check_resource "Service Bus Namespace" "$SERVICE_BUS_NAMESPACE" \
    "az servicebus namespace show --resource-group $RESOURCE_GROUP --name $SERVICE_BUS_NAMESPACE --query name"

# Check Queues
echo -e "\n${YELLOW}Checking Service Bus Queues:${NC}"
check_servicebus_queue "ai-operations"
check_servicebus_queue "scheduled-emails"
check_servicebus_queue "document-processing"
check_servicebus_queue "notification-delivery"
check_servicebus_queue "analytics-events"
check_servicebus_queue "thumbnail-processing"
check_servicebus_queue "data-migration"
check_servicebus_queue "enterprise-integration"

# Check Topics
echo -e "\n${YELLOW}Checking Service Bus Topics:${NC}"
check_servicebus_topic "workflow-orchestration"
check_servicebus_topic "document-collaboration"
check_servicebus_topic "analytics-events"
check_servicebus_topic "monitoring-events"
check_servicebus_topic "user-events"
check_servicebus_topic "project-events"
check_servicebus_topic "organization-events"

# Check Subscriptions
echo -e "\n${YELLOW}Checking Service Bus Subscriptions:${NC}"
check_servicebus_subscription "workflow-orchestration" "workflow-processor"
check_servicebus_subscription "document-collaboration" "collaboration-processor"
check_servicebus_subscription "analytics-events" "analytics-aggregator"
check_servicebus_subscription "monitoring-events" "system-monitor"
check_servicebus_subscription "user-events" "user-processor"
check_servicebus_subscription "project-events" "project-processor"
check_servicebus_subscription "organization-events" "organization-processor"

# 2. Verify Event Grid Resources
echo -e "\n${BLUE}⚡ Verifying Event Grid Resources...${NC}"
echo "===================================="

# Check Event Grid Topic
check_resource "Event Grid Topic" "$EVENT_GRID_TOPIC" \
    "az eventgrid topic show --resource-group $RESOURCE_GROUP --name $EVENT_GRID_TOPIC --query name"

# Check Event Grid Subscriptions
echo -e "\n${YELLOW}Checking Event Grid Subscriptions:${NC}"
SUBSCRIPTION_ID=$(az account show --query id -o tsv)

check_resource "Event Grid Subscription" "user-events-subscription" \
    "az eventgrid event-subscription show --source-resource-id /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC --name user-events-subscription --query name"

check_resource "Event Grid Subscription" "project-events-subscription" \
    "az eventgrid event-subscription show --source-resource-id /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC --name project-events-subscription --query name"

check_resource "Event Grid Subscription" "organization-events-subscription" \
    "az eventgrid event-subscription show --source-resource-id /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC --name organization-events-subscription --query name"

# 3. Verify Redis Cache
echo -e "\n${BLUE}🔴 Verifying Redis Cache...${NC}"
echo "============================"

check_resource "Redis Cache" "$REDIS_NAME" \
    "az redis show --resource-group $RESOURCE_GROUP --name $REDIS_NAME --query name"

# Check Redis status
echo -n "Checking Redis status... "
REDIS_STATUS=$(az redis show --resource-group $RESOURCE_GROUP --name $REDIS_NAME --query provisioningState -o tsv 2>/dev/null)
if [ "$REDIS_STATUS" = "Succeeded" ]; then
    echo -e "${GREEN}✅ RUNNING${NC}"
else
    echo -e "${YELLOW}⚠️ STATUS: $REDIS_STATUS${NC}"
fi

# 4. Verify Function App Configuration
echo -e "\n${BLUE}⚙️ Verifying Function App Configuration...${NC}"
echo "==========================================="

check_resource "Function App" "$FUNCTION_APP_NAME" \
    "az functionapp show --resource-group $RESOURCE_GROUP --name $FUNCTION_APP_NAME --query name"

# Check Function App Settings
echo -e "\n${YELLOW}Checking Function App Settings:${NC}"

check_setting() {
    local setting_name=$1
    echo -n "Checking setting '$setting_name'... "
    
    local setting_value=$(az functionapp config appsettings list \
        --resource-group $RESOURCE_GROUP \
        --name $FUNCTION_APP_NAME \
        --query "[?name=='$setting_name'].value" -o tsv 2>/dev/null)
    
    if [ -n "$setting_value" ] && [ "$setting_value" != "null" ]; then
        echo -e "${GREEN}✅ SET${NC}"
    else
        echo -e "${RED}❌ NOT SET${NC}"
    fi
}

check_setting "EVENT_GRID_TOPIC_ENDPOINT"
check_setting "EVENT_GRID_TOPIC_KEY"
check_setting "REDIS_HOSTNAME"
check_setting "REDIS_PORT"
check_setting "REDIS_KEY"
check_setting "REDIS_CONNECTION_STRING"
check_setting "SERVICE_BUS_CONNECTION_STRING"

# 5. Test Connectivity (Optional)
echo -e "\n${BLUE}🔗 Testing Connectivity...${NC}"
echo "=========================="

# Test Service Bus connectivity
echo -n "Testing Service Bus connectivity... "
if az servicebus namespace authorization-rule keys list \
    --resource-group $RESOURCE_GROUP \
    --namespace-name $SERVICE_BUS_NAMESPACE \
    --name RootManageSharedAccessKey > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ FAILED${NC}"
fi

# Test Event Grid connectivity
echo -n "Testing Event Grid connectivity... "
if az eventgrid topic key list \
    --resource-group $RESOURCE_GROUP \
    --name $EVENT_GRID_TOPIC > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ FAILED${NC}"
fi

# Test Redis connectivity
echo -n "Testing Redis connectivity... "
if az redis list-keys \
    --resource-group $RESOURCE_GROUP \
    --name $REDIS_NAME > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ FAILED${NC}"
fi

# Summary
echo -e "\n${GREEN}🎉 Verification Complete!${NC}"
echo "========================="
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Deploy your Function App with the enhanced code"
echo "2. Test Event Grid event publishing from your functions"
echo "3. Test Redis caching functionality"
echo "4. Test Service Bus message sending and receiving"
echo "5. Monitor Function App logs for any issues"
echo ""
echo -e "${YELLOW}💡 Useful Commands:${NC}"
echo "- Monitor Function App logs: az functionapp logs tail --resource-group $RESOURCE_GROUP --name $FUNCTION_APP_NAME"
echo "- Check Service Bus metrics: az monitor metrics list --resource /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ServiceBus/namespaces/$SERVICE_BUS_NAMESPACE"
echo "- Check Redis metrics: az monitor metrics list --resource /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Cache/Redis/$REDIS_NAME"
echo "- Check Event Grid metrics: az monitor metrics list --resource /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.EventGrid/topics/$EVENT_GRID_TOPIC"
