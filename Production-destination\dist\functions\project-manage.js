"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.projectManage = projectManage;
/**
 * Project Management Function
 * Handles retrieving, updating, and deleting projects
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const service_bus_1 = require("../shared/services/service-bus");
const event_grid_handlers_1 = require("./event-grid-handlers");
// Project visibility enum
var ProjectVisibility;
(function (ProjectVisibility) {
    ProjectVisibility["PRIVATE"] = "PRIVATE";
    ProjectVisibility["ORGANIZATION"] = "ORGANIZATION";
    ProjectVisibility["PUBLIC"] = "PUBLIC";
})(ProjectVisibility || (ProjectVisibility = {}));
// Validation schemas
const updateProjectSchema = Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    visibility: Joi.string().valid(...Object.values(ProjectVisibility)).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    settings: Joi.object({
        defaultDocumentTags: Joi.array().items(Joi.string()).optional(),
        defaultWorkflowId: Joi.string().uuid().allow(null).optional(),
        autoProcessing: Joi.boolean().optional()
    }).optional()
});
/**
 * Project management handler
 */
async function projectManage(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const projectId = request.params.projectId;
    if (!projectId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Project ID is required' }
        }, request);
    }
    logger_1.logger.info("Project management started", { correlationId, projectId, method: request.method });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get project
        const project = await database_1.db.readItem('projects', projectId, projectId);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        // Check if user is a member of the project
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.projectId = @projectId';
        const memberships = await database_1.db.queryItems('project-members', membershipQuery, [user.id, projectId]);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        const membership = memberships[0];
        if (request.method === 'GET') {
            // Get project details with enriched data
            // Get document count
            const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const documentCountResult = await database_1.db.queryItems('documents', documentCountQuery, [projectId]);
            const documentCount = Number(documentCountResult[0]) || 0;
            // Get workflow count
            const workflowCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const workflowCountResult = await database_1.db.queryItems('workflows', workflowCountQuery, [projectId]);
            const workflowCount = Number(workflowCountResult[0]) || 0;
            // Get member count
            const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const memberCountResult = await database_1.db.queryItems('project-members', memberCountQuery, [projectId]);
            const memberCount = Number(memberCountResult[0]) || 0;
            // Get organization details
            let organizationName = 'Unknown';
            try {
                const organization = await database_1.db.readItem('organizations', project.organizationId, project.organizationId);
                if (organization) {
                    organizationName = organization.name;
                }
            }
            catch (error) {
                // Organization might not exist or user might not have access
            }
            // Enrich project with statistics
            const enrichedProject = {
                ...project,
                documentCount,
                workflowCount,
                memberCount,
                organizationName,
                storageUsed: 0, // TODO: Calculate actual storage usage
                userRole: membership.role
            };
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: enrichedProject
            }, request);
        }
        else if (request.method === 'PATCH') {
            // Update project
            // Check if user has admin role
            if (membership.role !== 'ADMIN') {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Only project admins can update project settings" }
                }, request);
            }
            // Validate request body
            const body = await request.json();
            const { error, value } = updateProjectSchema.validate(body);
            if (error) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: 'Validation Error',
                        message: error.details.map(d => d.message).join(', ')
                    }
                }, request);
            }
            const updateData = value;
            // Update project
            const updatedProject = {
                ...project,
                id: projectId,
                ...updateData,
                updatedBy: user.id,
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('projects', updatedProject);
            // Invalidate Redis cache
            await redis_1.redis.del(`project:${projectId}:details`);
            await redis_1.redis.del(`user:${user.id}:projects`);
            await redis_1.redis.del(`org:${project.organizationId}:details`);
            // Publish Event Grid event
            await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PROJECT_UPDATED, `projects/${projectId}/updated`, {
                projectId,
                projectName: project.name,
                organizationId: project.organizationId,
                updatedFields: Object.keys(updateData),
                updatedBy: user.id,
                timestamp: new Date().toISOString()
            });
            // Send Service Bus message for analytics
            await service_bus_1.serviceBusEnhanced.sendToQueue('analytics-events', {
                body: {
                    eventType: 'project_updated',
                    projectId,
                    projectName: project.name,
                    organizationId: project.organizationId,
                    updatedFields: Object.keys(updateData),
                    updatedBy: user.id,
                    timestamp: new Date().toISOString()
                },
                messageId: `project-update-${projectId}-${Date.now()}`,
                correlationId: `project-${projectId}`,
                subject: 'project.updated'
            });
            // Create activity record
            await database_1.db.createItem('activities', {
                id: (0, uuid_1.v4)(),
                type: "project_updated",
                userId: user.id,
                organizationId: project.organizationId,
                projectId,
                timestamp: new Date().toISOString(),
                details: {
                    updatedFields: Object.keys(updateData),
                    projectName: project.name
                },
                tenantId: user.tenantId
            });
            logger_1.logger.info("Project updated successfully", {
                correlationId,
                projectId,
                userId: user.id,
                updatedFields: Object.keys(updateData)
            });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    id: projectId,
                    message: "Project updated successfully"
                }
            }, request);
        }
        else if (request.method === 'DELETE') {
            // Delete project
            // Check if user has admin role
            if (membership.role !== 'ADMIN') {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Only project admins can delete projects" }
                }, request);
            }
            // Check if project has documents
            const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const documentCountResult = await database_1.db.queryItems('documents', documentCountQuery, [projectId]);
            const documentCount = Number(documentCountResult[0]) || 0;
            if (documentCount > 0) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: "Cannot delete project with existing documents",
                        message: `Project has ${documentCount} documents. Please delete all documents first.`
                    }
                }, request);
            }
            // Delete project members
            const allMembersQuery = 'SELECT * FROM c WHERE c.projectId = @projectId';
            const allMembers = await database_1.db.queryItems('project-members', allMembersQuery, [projectId]);
            for (const member of allMembers) {
                await database_1.db.deleteItem('project-members', member.id, member.id);
            }
            // Remove project from organization's project list
            try {
                const organization = await database_1.db.readItem('organizations', project.organizationId, project.organizationId);
                if (organization) {
                    const updatedOrganization = {
                        ...organization,
                        id: project.organizationId,
                        projectIds: (organization.projectIds || []).filter((id) => id !== projectId),
                        updatedAt: new Date().toISOString(),
                        updatedBy: user.id
                    };
                    await database_1.db.updateItem('organizations', updatedOrganization);
                }
            }
            catch (error) {
                // Organization might not exist, continue with project deletion
            }
            // Delete project
            await database_1.db.deleteItem('projects', projectId, projectId);
            // Invalidate Redis cache
            await redis_1.redis.del(`project:${projectId}:details`);
            await redis_1.redis.del(`user:${user.id}:projects`);
            await redis_1.redis.del(`org:${project.organizationId}:details`);
            // Publish Event Grid event
            await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PROJECT_DELETED, `projects/${projectId}/deleted`, {
                projectId,
                projectName: project.name,
                organizationId: project.organizationId,
                memberCount: allMembers.length,
                deletedBy: user.id,
                timestamp: new Date().toISOString()
            });
            // Send Service Bus message for cleanup workflows
            await service_bus_1.serviceBusEnhanced.sendToQueue('analytics-events', {
                body: {
                    eventType: 'project_deleted',
                    projectId,
                    projectName: project.name,
                    organizationId: project.organizationId,
                    memberCount: allMembers.length,
                    deletedBy: user.id,
                    timestamp: new Date().toISOString()
                },
                messageId: `project-delete-${projectId}-${Date.now()}`,
                correlationId: `project-${projectId}`,
                subject: 'project.deleted'
            });
            // Create activity record
            await database_1.db.createItem('activities', {
                id: (0, uuid_1.v4)(),
                type: "project_deleted",
                userId: user.id,
                organizationId: project.organizationId,
                projectId,
                timestamp: new Date().toISOString(),
                details: {
                    projectName: project.name,
                    memberCount: allMembers.length
                },
                tenantId: user.tenantId
            });
            logger_1.logger.info("Project deleted successfully", {
                correlationId,
                projectId,
                userId: user.id
            });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    message: "Project deleted successfully"
                }
            }, request);
        }
        else {
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Method not allowed" }
            }, request);
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Project management failed", {
            correlationId,
            projectId,
            method: request.method,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('project-manage', {
    methods: ['GET', 'PATCH', 'DELETE', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/{projectId}',
    handler: projectManage
});
//# sourceMappingURL=project-manage.js.map