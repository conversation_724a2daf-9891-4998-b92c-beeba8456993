#!/bin/bash

# Azure Redis Enterprise Configuration Script
# This script configures Redis Enterprise cluster for the enhanced services

# Configuration variables
RESOURCE_GROUP="docucontext"
LOCATION="eastus"
REDIS_CLUSTER_NAME="hepzbackend"
FUNCTION_APP_NAME="hepzlogic"

echo "🔴 Configuring Azure Redis Enterprise Cluster..."
echo "================================================"

# Check if Redis Enterprise cluster exists
echo "Checking Redis Enterprise cluster status..."
CLUSTER_STATUS=$(az redisenterprise show \
  --cluster-name $REDIS_CLUSTER_NAME \
  --resource-group $RESOURCE_GROUP \
  --query provisioningState -o tsv 2>/dev/null)

if [ "$CLUSTER_STATUS" = "Succeeded" ]; then
  echo "✅ Redis Enterprise cluster '$REDIS_CLUSTER_NAME' is running"
else
  echo "❌ Redis Enterprise cluster not found or not ready"
  echo "Creating Redis Enterprise cluster..."
  
  # Create Redis Enterprise cluster
  az redisenterprise create \
    --cluster-name $REDIS_CLUSTER_NAME \
    --resource-group $RESOURCE_GROUP \
    --location $LOCATION \
    --sku "Enterprise_E10" \
    --capacity 2 \
    --zones 1 2 3 \
    --minimum-tls-version "1.2"
  
  echo "Waiting for cluster to be ready..."
  sleep 60
fi

# Get cluster information
echo "Getting Redis Enterprise cluster information..."
CLUSTER_INFO=$(az redisenterprise show \
  --cluster-name $REDIS_CLUSTER_NAME \
  --resource-group $RESOURCE_GROUP)

REDIS_HOSTNAME=$(echo $CLUSTER_INFO | jq -r '.hostName')
echo "Redis Hostname: $REDIS_HOSTNAME"

# Get database information
echo "Getting database information..."
DATABASE_INFO=$(az redisenterprise database list \
  --cluster-name $REDIS_CLUSTER_NAME \
  --resource-group $RESOURCE_GROUP)

REDIS_PORT=$(echo $DATABASE_INFO | jq -r '.[0].port')
echo "Redis Port: $REDIS_PORT"

# Get access keys
echo "Getting Redis Enterprise access keys..."
KEYS_INFO=$(az redisenterprise database list-keys \
  --cluster-name $REDIS_CLUSTER_NAME \
  --resource-group $RESOURCE_GROUP)

REDIS_PRIMARY_KEY=$(echo $KEYS_INFO | jq -r '.primaryKey')
REDIS_SECONDARY_KEY=$(echo $KEYS_INFO | jq -r '.secondaryKey')

echo "Primary Key: [HIDDEN]"
echo "Secondary Key: [HIDDEN]"

# Create Redis connection string
REDIS_CONNECTION_STRING="$REDIS_HOSTNAME:$REDIS_PORT,password=$REDIS_PRIMARY_KEY,ssl=True,abortConnect=False"

echo "✅ Redis Enterprise configuration retrieved successfully!"

# Update Function App settings
echo "Updating Function App settings with Redis Enterprise configuration..."

az functionapp config appsettings set \
  --resource-group $RESOURCE_GROUP \
  --name $FUNCTION_APP_NAME \
  --settings \
    "REDIS_HOSTNAME=$REDIS_HOSTNAME" \
    "REDIS_PORT=$REDIS_PORT" \
    "REDIS_KEY=$REDIS_PRIMARY_KEY" \
    "REDIS_SECONDARY_KEY=$REDIS_SECONDARY_KEY" \
    "REDIS_CONNECTION_STRING=$REDIS_CONNECTION_STRING" \
    "REDIS_ENTERPRISE_ENABLED=true" \
    "REDIS_CLUSTER_NAME=$REDIS_CLUSTER_NAME"

echo "✅ Function App settings updated with Redis Enterprise configuration!"

# Test Redis Enterprise connectivity
echo "Testing Redis Enterprise connectivity..."

# Create a simple test script
cat > redis_test.js << 'EOF'
const redis = require('redis');

async function testRedisEnterprise() {
  const connectionString = process.argv[2];
  
  try {
    console.log('Connecting to Redis Enterprise...');
    const client = redis.createClient({
      url: `redis://${connectionString}`
    });
    
    await client.connect();
    console.log('✅ Connected to Redis Enterprise successfully!');
    
    // Test basic operations
    await client.set('test:connection', JSON.stringify({
      message: 'Redis Enterprise connection test',
      timestamp: new Date().toISOString(),
      cluster: 'hepzbackend'
    }), { EX: 60 });
    
    const result = await client.get('test:connection');
    if (result) {
      console.log('✅ Redis Enterprise read/write test successful!');
      console.log('Test data:', JSON.parse(result));
    }
    
    // Test cache patterns
    const userKey = 'user:test:profile';
    const userData = {
      id: 'test-user',
      name: 'Test User',
      email: '<EMAIL>',
      cachedAt: new Date().toISOString()
    };
    
    await client.setEx(userKey, 3600, JSON.stringify(userData));
    const cachedUser = await client.get(userKey);
    
    if (cachedUser) {
      console.log('✅ Cache pattern test successful!');
    }
    
    // Clean up
    await client.del('test:connection');
    await client.del(userKey);
    
    await client.disconnect();
    console.log('✅ Redis Enterprise test completed successfully!');
    
  } catch (error) {
    console.error('❌ Redis Enterprise test failed:', error.message);
    process.exit(1);
  }
}

testRedisEnterprise();
EOF

# Run the test if Node.js and redis package are available
if command -v node >/dev/null 2>&1; then
  if npm list redis >/dev/null 2>&1; then
    echo "Running Redis Enterprise connectivity test..."
    node redis_test.js "$REDIS_CONNECTION_STRING"
  else
    echo "⚠️  Redis npm package not found. Skipping connectivity test."
    echo "To test manually, install redis package: npm install redis"
  fi
else
  echo "⚠️  Node.js not found. Skipping connectivity test."
fi

# Clean up test file
rm -f redis_test.js

echo ""
echo "🎉 Redis Enterprise Configuration Complete!"
echo "=========================================="
echo ""
echo "📋 Configuration Summary:"
echo "Cluster Name: $REDIS_CLUSTER_NAME"
echo "Hostname: $REDIS_HOSTNAME"
echo "Port: $REDIS_PORT"
echo "SSL: Enabled"
echo "TLS Version: 1.2+"
echo ""
echo "🔧 Function App Settings Updated:"
echo "- REDIS_HOSTNAME"
echo "- REDIS_PORT"
echo "- REDIS_KEY"
echo "- REDIS_SECONDARY_KEY"
echo "- REDIS_CONNECTION_STRING"
echo "- REDIS_ENTERPRISE_ENABLED"
echo "- REDIS_CLUSTER_NAME"
echo ""
echo "📚 Redis Enterprise Features Available:"
echo "- High Availability with Zone Redundancy"
echo "- Enterprise-grade security"
echo "- Advanced data persistence (RDB enabled)"
echo "- Clustering support"
echo "- Enhanced performance"
echo ""
echo "🔗 Connection String Format:"
echo "redis://$REDIS_HOSTNAME:$REDIS_PORT"
echo ""
echo "💡 Next Steps:"
echo "1. Deploy your Function App with enhanced Redis code"
echo "2. Test Redis caching functionality"
echo "3. Monitor Redis Enterprise metrics in Azure Portal"
echo "4. Configure Redis modules if needed"
echo ""
echo "📊 Monitoring Commands:"
echo "az redisenterprise show --cluster-name $REDIS_CLUSTER_NAME --resource-group $RESOURCE_GROUP"
echo "az redisenterprise database list --cluster-name $REDIS_CLUSTER_NAME --resource-group $RESOURCE_GROUP"
