"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.search = search;
/**
 * Search Function
 * Handles searching across documents, projects, and organizations
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
const service_bus_1 = require("../shared/services/service-bus");
// Search types enum
var SearchType;
(function (SearchType) {
    SearchType["ALL"] = "ALL";
    SearchType["DOCUMENTS"] = "DOCUMENTS";
    SearchType["PROJECTS"] = "PROJECTS";
    SearchType["ORGANIZATIONS"] = "ORGANIZATIONS";
    SearchType["WORKFLOWS"] = "WORKFLOWS";
    SearchType["USERS"] = "USERS";
})(SearchType || (SearchType = {}));
// Validation schema
const searchSchema = Joi.object({
    query: Joi.string().required().min(1).max(200),
    type: Joi.string().valid(...Object.values(SearchType)).default(SearchType.ALL),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional(),
    filters: Joi.object({
        contentType: Joi.string().optional(),
        tags: Joi.array().items(Joi.string()).optional(),
        dateFrom: Joi.date().iso().optional(),
        dateTo: Joi.date().iso().optional(),
        createdBy: Joi.string().uuid().optional()
    }).optional()
});
/**
 * Search handler
 */
async function search(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Search started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Initialize services
        const serviceBusService = service_bus_1.ServiceBusEnhancedService.getInstance();
        await serviceBusService.initialize();
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = searchSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { query, type, page, limit, organizationId, projectId, filters } = value;
        // Check Redis cache for search results
        const cacheKey = `search:${user.id}:${JSON.stringify({ query, type, organizationId, projectId, filters })}`;
        const cachedResults = await redis_1.redis.getJson(cacheKey);
        if (cachedResults && cachedResults.timestamp && (Date.now() - cachedResults.timestamp) < 300000) { // 5 minutes cache
            logger_1.logger.info('Returning cached search results', {
                correlationId,
                query,
                cacheAge: Date.now() - cachedResults.timestamp
            });
            // Apply pagination to cached results
            const total = cachedResults.results.length;
            const offset = (page - 1) * limit;
            const paginatedResults = cachedResults.results.slice(offset, offset + limit);
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    ...cachedResults.response,
                    results: paginatedResults,
                    page,
                    limit,
                    hasMore: offset + limit < total,
                    fromCache: true
                }
            }, request);
        }
        let allResults = [];
        // Search documents
        if (type === SearchType.ALL || type === SearchType.DOCUMENTS) {
            const documentResults = await searchDocuments(query, user, organizationId, projectId, filters);
            allResults.push(...documentResults);
        }
        // Search projects
        if (type === SearchType.ALL || type === SearchType.PROJECTS) {
            const projectResults = await searchProjects(query, user, organizationId, filters);
            allResults.push(...projectResults);
        }
        // Search organizations
        if (type === SearchType.ALL || type === SearchType.ORGANIZATIONS) {
            const organizationResults = await searchOrganizations(query, user, filters);
            allResults.push(...organizationResults);
        }
        // Search workflows
        if (type === SearchType.ALL || type === SearchType.WORKFLOWS) {
            const workflowResults = await searchWorkflows(query, user, organizationId, projectId, filters);
            allResults.push(...workflowResults);
        }
        // Sort results by score (relevance)
        allResults.sort((a, b) => b.score - a.score);
        // Apply pagination
        const total = allResults.length;
        const offset = (page - 1) * limit;
        const paginatedResults = allResults.slice(offset, offset + limit);
        // Group results by type for summary
        const resultsByType = allResults.reduce((acc, result) => {
            acc[result.type] = (acc[result.type] || 0) + 1;
            return acc;
        }, {});
        logger_1.logger.info("Search completed successfully", {
            correlationId,
            userId: user.id,
            query,
            type,
            totalResults: total,
            page,
            limit
        });
        // Create response
        const response = {
            query,
            type,
            results: paginatedResults,
            total,
            page,
            limit,
            hasMore: offset + limit < total,
            summary: {
                totalResults: total,
                resultsByType
            }
        };
        // Cache search results
        await redis_1.redis.setJson(cacheKey, {
            results: allResults,
            response: {
                query,
                type,
                total,
                summary: {
                    totalResults: total,
                    resultsByType
                }
            },
            timestamp: Date.now()
        }, 300); // 5 minutes cache
        // Publish Event Grid event for search performed
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Search.Performed',
            subject: `search/${type.toLowerCase()}`,
            data: {
                query,
                type,
                userId: user.id,
                organizationId,
                projectId,
                totalResults: total,
                resultsByType,
                searchedAt: new Date().toISOString(),
                correlationId
            }
        });
        // Send analytics message to Service Bus
        await serviceBusService.sendToQueue('analytics-events', {
            body: {
                eventType: 'search-performed',
                query,
                type,
                userId: user.id,
                organizationId,
                projectId,
                totalResults: total,
                resultsByType,
                correlationId,
                timestamp: new Date().toISOString()
            },
            messageId: `search-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            correlationId,
            subject: 'search.analytics'
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Search failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Search documents
 */
async function searchDocuments(query, user, organizationId, projectId, filters) {
    let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query))
           OR CONTAINS(LOWER(c.extractedText), LOWER(@query)))
  `;
    const parameters = [query];
    // Add access control
    queryText += ' AND (c.createdBy = @userId OR c.organizationId = @tenantId)';
    parameters.push(user.id, user.tenantId);
    // Add filters
    if (organizationId) {
        queryText += ' AND c.organizationId = @organizationId';
        parameters.push(organizationId);
    }
    if (projectId) {
        queryText += ' AND c.projectId = @projectId';
        parameters.push(projectId);
    }
    if (filters?.contentType) {
        queryText += ' AND c.contentType = @contentType';
        parameters.push(filters.contentType);
    }
    if (filters?.dateFrom) {
        queryText += ' AND c.createdAt >= @dateFrom';
        parameters.push(filters.dateFrom);
    }
    if (filters?.dateTo) {
        queryText += ' AND c.createdAt <= @dateTo';
        parameters.push(filters.dateTo);
    }
    const documents = await database_1.db.queryItems('documents', queryText, parameters);
    return documents.map((doc) => ({
        id: doc.id,
        type: 'DOCUMENT',
        title: doc.name,
        description: doc.description,
        content: doc.extractedText?.substring(0, 200) + '...',
        url: `/documents/${doc.id}`,
        score: calculateRelevanceScore(query, doc.name, doc.description, doc.extractedText),
        highlights: extractHighlights(query, [doc.name, doc.description, doc.extractedText]),
        metadata: {
            createdAt: doc.createdAt,
            createdBy: doc.createdBy,
            organizationId: doc.organizationId,
            projectId: doc.projectId,
            contentType: doc.contentType,
            tags: doc.tags
        }
    }));
}
/**
 * Search projects
 */
async function searchProjects(query, user, organizationId, filters) {
    let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query)))
    AND ARRAY_CONTAINS(c.memberIds, @userId)
  `;
    const parameters = [query, user.id];
    if (organizationId) {
        queryText += ' AND c.organizationId = @organizationId';
        parameters.push(organizationId);
    }
    const projects = await database_1.db.queryItems('projects', queryText, parameters);
    return projects.map((project) => ({
        id: project.id,
        type: 'PROJECT',
        title: project.name,
        description: project.description,
        url: `/projects/${project.id}`,
        score: calculateRelevanceScore(query, project.name, project.description),
        highlights: extractHighlights(query, [project.name, project.description]),
        metadata: {
            createdAt: project.createdAt,
            createdBy: project.createdBy,
            organizationId: project.organizationId,
            tags: project.tags
        }
    }));
}
/**
 * Search organizations
 */
async function searchOrganizations(query, user, filters) {
    // Get user's organization memberships
    const membershipQuery = 'SELECT DISTINCT c.organizationId FROM c WHERE c.userId = @userId AND c.status = @status';
    const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, 'active']);
    if (memberships.length === 0)
        return [];
    const orgIds = memberships.map((m) => m.organizationId);
    let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query)))
    AND ARRAY_CONTAINS(@orgIds, c.id)
  `;
    const parameters = [query, orgIds];
    const organizations = await database_1.db.queryItems('organizations', queryText, parameters);
    return organizations.map((org) => ({
        id: org.id,
        type: 'ORGANIZATION',
        title: org.name,
        description: org.description,
        url: `/organizations/${org.id}`,
        score: calculateRelevanceScore(query, org.name, org.description),
        highlights: extractHighlights(query, [org.name, org.description]),
        metadata: {
            createdAt: org.createdAt,
            createdBy: org.createdBy
        }
    }));
}
/**
 * Search workflows
 */
async function searchWorkflows(query, user, organizationId, projectId, filters) {
    let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query)))
    AND (c.createdBy = @userId OR c.organizationId = @tenantId)
  `;
    const parameters = [query, user.id, user.tenantId];
    if (organizationId) {
        queryText += ' AND c.organizationId = @organizationId';
        parameters.push(organizationId);
    }
    if (projectId) {
        queryText += ' AND c.projectId = @projectId';
        parameters.push(projectId);
    }
    const workflows = await database_1.db.queryItems('workflows', queryText, parameters);
    return workflows.map((workflow) => ({
        id: workflow.id,
        type: 'WORKFLOW',
        title: workflow.name,
        description: workflow.description,
        url: `/workflows/${workflow.id}`,
        score: calculateRelevanceScore(query, workflow.name, workflow.description),
        highlights: extractHighlights(query, [workflow.name, workflow.description]),
        metadata: {
            createdAt: workflow.createdAt,
            createdBy: workflow.createdBy,
            organizationId: workflow.organizationId,
            projectId: workflow.projectId
        }
    }));
}
/**
 * Calculate relevance score
 */
function calculateRelevanceScore(query, ...texts) {
    const queryLower = query.toLowerCase();
    let score = 0;
    texts.forEach((text, index) => {
        if (!text)
            return;
        const textLower = text.toLowerCase();
        // Exact match gets highest score
        if (textLower.includes(queryLower)) {
            score += (10 - index) * 10;
        }
        // Word matches
        const queryWords = queryLower.split(' ');
        queryWords.forEach(word => {
            if (textLower.includes(word)) {
                score += (10 - index) * 2;
            }
        });
    });
    return score;
}
/**
 * Extract highlights
 */
function extractHighlights(query, texts) {
    const highlights = [];
    const queryLower = query.toLowerCase();
    texts.forEach(text => {
        if (!text)
            return;
        const textLower = text.toLowerCase();
        const index = textLower.indexOf(queryLower);
        if (index !== -1) {
            const start = Math.max(0, index - 50);
            const end = Math.min(text.length, index + queryLower.length + 50);
            const highlight = text.substring(start, end);
            highlights.push(highlight);
        }
    });
    return highlights.slice(0, 3); // Limit to 3 highlights
}
// Register functions
functions_1.app.http('search', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'search',
    handler: search
});
//# sourceMappingURL=search.js.map