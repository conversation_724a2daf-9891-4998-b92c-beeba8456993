"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userLogout = userLogout;
exports.refreshToken = refreshToken;
exports.userRegister = userRegister;
/**
 * User Auth Operations Functions
 * Handles logout, refresh token, and user registration operations
 */
const functions_1 = require("@azure/functions");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcrypt = __importStar(require("bcryptjs"));
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redisEnhanced_1 = require("../shared/services/redisEnhanced");
const serviceBusEnhanced_1 = require("../shared/services/serviceBusEnhanced");
const event_grid_handlers_1 = require("./event-grid-handlers");
// Validation schemas
const logoutSchema = Joi.object({
    refreshToken: Joi.string().optional(),
    allDevices: Joi.boolean().optional()
}).or('refreshToken', 'allDevices');
const refreshTokenSchema = Joi.object({
    refreshToken: Joi.string().required()
});
const registerSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    organizationId: Joi.string().uuid().optional(),
    invitationCode: Joi.string().optional()
});
/**
 * User logout handler
 */
async function userLogout(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("User logout started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = logoutSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { refreshToken, allDevices } = value;
        // Get current user data
        const currentUser = await database_1.db.readItem('users', user.id, user.id);
        if (!currentUser) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User not found" }
            }, request);
        }
        let updatedUser;
        if (allDevices) {
            // Log out from all devices by removing all refresh tokens
            updatedUser = {
                ...currentUser,
                id: currentUser.id,
                refreshTokens: [],
                lastLogoutAt: new Date().toISOString()
            };
            logger_1.logger.info("User logged out from all devices", { userId: user.id, correlationId });
        }
        else if (refreshToken) {
            // Log out from specific device by removing the specific refresh token
            const refreshTokens = currentUser.refreshTokens || [];
            const updatedTokens = refreshTokens.filter((t) => t.token !== refreshToken);
            updatedUser = {
                ...currentUser,
                id: currentUser.id,
                refreshTokens: updatedTokens,
                lastLogoutAt: new Date().toISOString()
            };
            logger_1.logger.info("User logged out from specific device", { userId: user.id, correlationId });
        }
        if (updatedUser) {
            await database_1.db.updateItem('users', updatedUser);
        }
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { message: "Logged out successfully" }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("User logout failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Refresh token handler
 */
async function refreshToken(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Refresh token started", { correlationId });
    try {
        // Validate request body
        const body = await request.json();
        const { error, value } = refreshTokenSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { refreshToken: token } = value;
        // Find user by refresh token
        const query = 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.refreshTokens, {"token": @token}, true)';
        const users = await database_1.db.queryItems('users', query, [token]);
        if (users.length === 0) {
            logger_1.logger.warn("Refresh token attempt with invalid token", { correlationId });
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Invalid refresh token" }
            }, request);
        }
        const user = users[0];
        // Find the specific token info
        const tokenInfo = user.refreshTokens.find((t) => t.token === token);
        if (!tokenInfo) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Invalid refresh token" }
            }, request);
        }
        // Check if token is expired
        const expiresAt = new Date(tokenInfo.expiresAt);
        if (expiresAt < new Date()) {
            logger_1.logger.warn("Refresh token attempt with expired token", { userId: user.id, correlationId });
            // Remove expired token
            const updatedTokens = user.refreshTokens.filter((t) => t.token !== token);
            const updatedUser = {
                ...user,
                refreshTokens: updatedTokens
            };
            await database_1.db.updateItem('users', updatedUser);
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Refresh token expired" }
            }, request);
        }
        // Generate new tokens
        const newRefreshToken = (0, uuid_1.v4)();
        const refreshTokenExpiry = new Date();
        refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days
        const jwtSecret = process.env.AUTH_CLIENT_SECRET;
        if (!jwtSecret) {
            logger_1.logger.error("AUTH_CLIENT_SECRET environment variable is not set", { correlationId });
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Authentication service misconfigured" }
            }, request);
        }
        const now = Math.floor(Date.now() / 1000);
        const expiresIn = 3600; // 1 hour
        const accessToken = jsonwebtoken_1.default.sign({
            sub: user.id,
            email: user.email,
            name: user.displayName || `${user.firstName} ${user.lastName}`,
            tenantId: user.tenantId,
            roles: user.roles || [],
            systemRoles: user.systemRoles || [],
            organizationIds: user.organizationIds || [],
            defaultOrganizationId: user.defaultOrganizationId,
            iat: now,
            exp: now + expiresIn,
            jti: (0, uuid_1.v4)(),
            auth_time: now
        }, jwtSecret, {
            expiresIn: `${expiresIn}s`,
            issuer: process.env.AUTH_ISSUER || "hepz-platform",
            audience: process.env.AUTH_CLIENT_ID || "hepz-client",
            algorithm: "HS256"
        });
        // Update refresh tokens in database
        const updatedTokens = user.refreshTokens.filter((t) => t.token !== token);
        updatedTokens.push({
            token: newRefreshToken,
            expiresAt: refreshTokenExpiry.toISOString(),
            createdAt: new Date().toISOString(),
            userAgent: request.headers.get("user-agent") || "unknown",
            ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown"
        });
        const updatedUser = {
            ...user,
            refreshTokens: updatedTokens,
            lastTokenRefreshAt: new Date().toISOString()
        };
        await database_1.db.updateItem('users', updatedUser);
        logger_1.logger.info("Token refreshed successfully", { userId: user.id, correlationId });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                accessToken,
                refreshToken: newRefreshToken,
                expiresIn,
                tokenType: "Bearer"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Refresh token failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * User registration handler
 */
async function userRegister(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("User registration started", { correlationId });
    try {
        // Validate request body
        const body = await request.json();
        const { error, value } = registerSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { email, password, firstName, lastName, organizationId, invitationCode } = value;
        // Check if user already exists
        const query = 'SELECT * FROM c WHERE c.email = @email';
        const existingUsers = await database_1.db.queryItems('users', query, [email.toLowerCase()]);
        if (existingUsers.length > 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User with this email already exists" }
            }, request);
        }
        // Hash password
        const salt = await bcrypt.genSalt(10);
        const passwordHash = await bcrypt.hash(password, salt);
        // Create new user
        const userId = (0, uuid_1.v4)();
        const newUser = {
            id: userId,
            email: email.toLowerCase(),
            firstName,
            lastName,
            displayName: `${firstName} ${lastName}`,
            passwordHash,
            tenantId: organizationId || (0, uuid_1.v4)(),
            roles: [],
            systemRoles: [],
            organizationIds: organizationId ? [organizationId] : [],
            defaultOrganizationId: organizationId,
            status: "active",
            refreshTokens: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            preferences: {
                theme: "system",
                language: "en",
                timezone: "UTC",
                dateFormat: "MM/DD/YYYY",
                notifications: {
                    email: true,
                    inApp: true,
                    documentUploaded: true,
                    documentProcessed: true,
                    commentAdded: true,
                    mentionedInComment: true,
                    projectInvitation: true,
                    organizationInvitation: true
                }
            }
        };
        await database_1.db.createItem('users', newUser);
        // Cache user profile in Redis
        const redis = redisEnhanced_1.RedisEnhancedService.getInstance();
        const userProfile = {
            id: userId,
            email: email.toLowerCase(),
            firstName,
            lastName,
            displayName: `${firstName} ${lastName}`,
            organizationIds: organizationId ? [organizationId] : [],
            defaultOrganizationId: organizationId,
            status: "active",
            preferences: newUser.preferences
        };
        await redis.setJson(`user:${userId}:profile`, userProfile, 3600); // 1 hour cache
        // Publish Event Grid event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.USER_REGISTERED, `users/${userId}/registered`, {
            userId,
            email: email.toLowerCase(),
            firstName,
            lastName,
            organizationId,
            registrationMethod: 'email',
            timestamp: new Date().toISOString()
        });
        // Send Service Bus message for welcome workflow
        const serviceBusService = serviceBusEnhanced_1.ServiceBusEnhancedService.getInstance();
        await serviceBusService.sendToQueue('notification-delivery', {
            body: {
                eventType: 'user_registered',
                userId,
                email: email.toLowerCase(),
                firstName,
                lastName,
                organizationId,
                timestamp: new Date().toISOString()
            },
            messageId: `user-register-${userId}-${Date.now()}`,
            correlationId: `user-${userId}`,
            subject: 'user.registered'
        });
        logger_1.logger.info("User registered successfully", {
            userId,
            email: email.toLowerCase(),
            correlationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "User registered successfully",
                userId,
                email: email.toLowerCase()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("User registration failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('auth-logout', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'auth/logout',
    handler: userLogout
});
functions_1.app.http('auth-refresh', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'auth/refresh',
    handler: refreshToken
});
functions_1.app.http('auth-register', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'auth/register',
    handler: userRegister
});
//# sourceMappingURL=user-auth-operations.js.map