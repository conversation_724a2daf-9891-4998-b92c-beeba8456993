"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAdvancedRole = createAdvancedRole;
exports.checkAdvancedPermission = checkAdvancedPermission;
/**
 * Advanced Permissions Function
 * Handles sophisticated role-based access control and fine-grained permissions
 * Migrated from old-arch/src/shared/middleware/authorization.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
// import { eventService } from '../shared/services/event'; // Unused for now
// Advanced permission types and enums
var SystemRole;
(function (SystemRole) {
    SystemRole["SYSTEM_ADMIN"] = "SYSTEM_ADMIN";
    SystemRole["PLATFORM_ADMIN"] = "PLATFORM_ADMIN";
    SystemRole["SUPPORT_ADMIN"] = "SUPPORT_ADMIN";
    SystemRole["SECURITY_ADMIN"] = "SECURITY_ADMIN";
})(SystemRole || (SystemRole = {}));
var OrganizationRole;
(function (OrganizationRole) {
    OrganizationRole["OWNER"] = "OWNER";
    OrganizationRole["ADMIN"] = "ADMIN";
    OrganizationRole["MANAGER"] = "MANAGER";
    OrganizationRole["MEMBER"] = "MEMBER";
    OrganizationRole["VIEWER"] = "VIEWER";
    OrganizationRole["GUEST"] = "GUEST";
})(OrganizationRole || (OrganizationRole = {}));
var PermissionAction;
(function (PermissionAction) {
    PermissionAction["CREATE"] = "CREATE";
    PermissionAction["READ"] = "READ";
    PermissionAction["UPDATE"] = "UPDATE";
    PermissionAction["DELETE"] = "DELETE";
    PermissionAction["SHARE"] = "SHARE";
    PermissionAction["MANAGE"] = "MANAGE";
    PermissionAction["APPROVE"] = "APPROVE";
    PermissionAction["EXECUTE"] = "EXECUTE";
    PermissionAction["ADMIN"] = "ADMIN";
})(PermissionAction || (PermissionAction = {}));
var ResourceType;
(function (ResourceType) {
    ResourceType["DOCUMENT"] = "DOCUMENT";
    ResourceType["WORKFLOW"] = "WORKFLOW";
    ResourceType["PROJECT"] = "PROJECT";
    ResourceType["ORGANIZATION"] = "ORGANIZATION";
    ResourceType["USER"] = "USER";
    ResourceType["TEMPLATE"] = "TEMPLATE";
    ResourceType["INTEGRATION"] = "INTEGRATION";
    ResourceType["REPORT"] = "REPORT";
})(ResourceType || (ResourceType = {}));
// Validation schemas
const createRoleSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(50).required(),
    description: joi_1.default.string().max(500).optional(),
    organizationId: joi_1.default.string().uuid().required(),
    permissions: joi_1.default.array().items(joi_1.default.object({
        resource: joi_1.default.string().valid(...Object.values(ResourceType)).required(),
        actions: joi_1.default.array().items(joi_1.default.string().valid(...Object.values(PermissionAction))).min(1).required(),
        conditions: joi_1.default.object({
            ownedOnly: joi_1.default.boolean().default(false),
            departmentOnly: joi_1.default.boolean().default(false),
            projectOnly: joi_1.default.boolean().default(false),
            timeRestrictions: joi_1.default.object().optional(),
            ipRestrictions: joi_1.default.array().items(joi_1.default.string().ip()).optional()
        }).optional()
    })).min(1).required(),
    hierarchy: joi_1.default.object({
        level: joi_1.default.number().min(1).max(10).default(5),
        inheritsFrom: joi_1.default.string().uuid().optional(),
        canDelegate: joi_1.default.boolean().default(false)
    }).optional(),
    isActive: joi_1.default.boolean().default(true)
});
const checkPermissionSchema = joi_1.default.object({
    userId: joi_1.default.string().uuid().required(),
    resource: joi_1.default.string().valid(...Object.values(ResourceType)).required(),
    action: joi_1.default.string().valid(...Object.values(PermissionAction)).required(),
    context: joi_1.default.object({
        organizationId: joi_1.default.string().uuid().optional(),
        projectId: joi_1.default.string().uuid().optional(),
        resourceId: joi_1.default.string().uuid().optional(),
        departmentId: joi_1.default.string().uuid().optional(),
        metadata: joi_1.default.object().optional()
    }).optional()
});
/**
 * Create advanced role handler
 */
async function createAdvancedRole(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Create advanced role started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const body = await request.json();
        const { error, value } = createRoleSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const roleRequest = value;
        // Check admin access
        const hasAccess = await checkAdvancedPermissionAccess(user, roleRequest.organizationId);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to role management" }
            }, request);
        }
        // Create advanced role
        const roleId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const advancedRole = {
            id: roleId,
            name: roleRequest.name,
            description: roleRequest.description,
            organizationId: roleRequest.organizationId,
            permissions: roleRequest.permissions,
            hierarchy: {
                level: 5,
                canDelegate: false,
                ...roleRequest.hierarchy
            },
            statistics: {
                userCount: 0,
                createdCount: 0
            },
            isActive: roleRequest.isActive,
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('advanced-roles', advancedRole);
        // Cache role for quick access
        await cacheAdvancedRole(advancedRole);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "advanced_role_created",
            userId: user.id,
            organizationId: roleRequest.organizationId,
            timestamp: now,
            details: {
                roleId,
                roleName: roleRequest.name,
                permissionCount: roleRequest.permissions.length,
                hierarchyLevel: advancedRole.hierarchy.level
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Advanced role created successfully", {
            correlationId,
            roleId,
            roleName: roleRequest.name,
            permissionCount: roleRequest.permissions.length,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                roleId,
                name: roleRequest.name,
                permissionCount: roleRequest.permissions.length,
                hierarchyLevel: advancedRole.hierarchy.level,
                createdAt: now,
                message: "Advanced role created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create advanced role failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check advanced permission handler
 */
async function checkAdvancedPermission(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Check advanced permission started", { correlationId });
    try {
        const body = await request.json();
        const { error, value } = checkPermissionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const permissionCheck = value;
        // Perform comprehensive permission check
        const permissionResult = await performAdvancedPermissionCheck(permissionCheck);
        logger_1.logger.info("Advanced permission checked", {
            correlationId,
            userId: permissionCheck.userId,
            resource: permissionCheck.resource,
            action: permissionCheck.action,
            allowed: permissionResult.allowed,
            reason: permissionResult.reason
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                allowed: permissionResult.allowed,
                reason: permissionResult.reason,
                roles: permissionResult.roles,
                conditions: permissionResult.conditions,
                checkedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Check advanced permission failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkAdvancedPermissionAccess(user, organizationId) {
    try {
        // Check if user has system admin role
        if (user.systemRoles?.includes(SystemRole.SYSTEM_ADMIN)) {
            return true;
        }
        // Check organization admin access
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === OrganizationRole.OWNER || membership.role === OrganizationRole.ADMIN;
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check advanced permission access', { error, userId: user.id, organizationId });
        return false;
    }
}
async function cacheAdvancedRole(role) {
    try {
        const cacheKey = `advanced_role:${role.id}`;
        await redis_1.redis.setex(cacheKey, 3600, JSON.stringify(role)); // 1 hour cache
        // Cache in organization role list
        const orgRolesKey = `org_advanced_roles:${role.organizationId}`;
        await redis_1.redis.sadd(orgRolesKey, role.id);
        await redis_1.redis.expire(orgRolesKey, 3600);
    }
    catch (error) {
        logger_1.logger.error('Failed to cache advanced role', { error, roleId: role.id });
    }
}
async function performAdvancedPermissionCheck(permissionCheck) {
    try {
        // Get user's roles and permissions
        const userRoles = await getUserAdvancedRoles(permissionCheck.userId, permissionCheck.context?.organizationId);
        // Check system-level permissions first
        const systemPermission = await checkSystemPermissions(permissionCheck.userId, permissionCheck.resource, permissionCheck.action);
        if (systemPermission.allowed) {
            return {
                allowed: true,
                reason: 'System-level permission granted',
                roles: ['SYSTEM_ADMIN'],
                conditions: {}
            };
        }
        // Check role-based permissions
        for (const role of userRoles) {
            const rolePermission = await checkRolePermissions(role, permissionCheck);
            if (rolePermission.allowed) {
                return {
                    allowed: true,
                    reason: `Permission granted via role: ${role.name}`,
                    roles: [role.name],
                    conditions: rolePermission.conditions
                };
            }
        }
        // Check inherited permissions
        const inheritedPermission = await checkInheritedPermissions(userRoles, permissionCheck);
        if (inheritedPermission.allowed) {
            return inheritedPermission;
        }
        return {
            allowed: false,
            reason: 'No matching permissions found',
            roles: userRoles.map(r => r.name),
            conditions: {}
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to perform advanced permission check', { error, permissionCheck });
        return {
            allowed: false,
            reason: 'Permission check failed',
            roles: [],
            conditions: {}
        };
    }
}
async function getUserAdvancedRoles(userId, organizationId) {
    try {
        // Try cache first
        const cacheKey = `user_advanced_roles:${userId}:${organizationId || 'global'}`;
        const cached = await redis_1.redis.get(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }
        // Query user role assignments
        let query = 'SELECT * FROM c WHERE c.userId = @userId';
        const parameters = [userId];
        if (organizationId) {
            query += ' AND c.organizationId = @orgId';
            parameters.push(organizationId);
        }
        const roleAssignments = await database_1.db.queryItems('user-role-assignments', query, parameters);
        // Get role details
        const roles = [];
        for (const assignment of roleAssignments) {
            const assignmentData = assignment;
            const role = await database_1.db.readItem('advanced-roles', assignmentData.roleId, assignmentData.roleId);
            if (role && role.isActive) {
                roles.push(role);
            }
        }
        // Cache for 15 minutes
        await redis_1.redis.setex(cacheKey, 900, JSON.stringify(roles));
        return roles;
    }
    catch (error) {
        logger_1.logger.error('Failed to get user advanced roles', { error, userId, organizationId });
        return [];
    }
}
async function checkSystemPermissions(userId, resource, action) {
    try {
        // Get user system roles
        const userQuery = 'SELECT * FROM c WHERE c.id = @userId';
        const users = await database_1.db.queryItems('users', userQuery, [userId]);
        if (users.length === 0) {
            return { allowed: false, reason: 'User not found' };
        }
        const user = users[0];
        const systemRoles = user.systemRoles || [];
        // System admins have all permissions
        if (systemRoles.includes(SystemRole.SYSTEM_ADMIN)) {
            return { allowed: true, reason: 'System admin access' };
        }
        // Platform admins have most permissions
        if (systemRoles.includes(SystemRole.PLATFORM_ADMIN)) {
            const restrictedActions = [PermissionAction.DELETE];
            if (!restrictedActions.includes(action)) {
                return { allowed: true, reason: 'Platform admin access' };
            }
        }
        return { allowed: false, reason: 'No system-level permissions' };
    }
    catch (error) {
        logger_1.logger.error('Failed to check system permissions', { error, userId, resource, action });
        return { allowed: false, reason: 'System permission check failed' };
    }
}
async function checkRolePermissions(role, permissionCheck) {
    try {
        // Find matching permission in role
        const matchingPermission = role.permissions.find(p => p.resource === permissionCheck.resource &&
            p.actions.includes(permissionCheck.action));
        if (!matchingPermission) {
            return { allowed: false, reason: 'No matching permission in role' };
        }
        // Check conditions if present
        if (matchingPermission.conditions) {
            const conditionCheck = await evaluatePermissionConditions(matchingPermission.conditions, permissionCheck);
            if (!conditionCheck.allowed) {
                return conditionCheck;
            }
        }
        return {
            allowed: true,
            reason: `Permission granted via role: ${role.name}`,
            conditions: matchingPermission.conditions || {}
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to check role permissions', { error, roleId: role.id, permissionCheck });
        return { allowed: false, reason: 'Role permission check failed' };
    }
}
async function evaluatePermissionConditions(conditions, permissionCheck, request) {
    try {
        // Check ownership condition
        if (conditions.ownedOnly && permissionCheck.context?.resourceId) {
            const isOwner = await checkResourceOwnership(permissionCheck.userId, permissionCheck.context.resourceId, permissionCheck.resource);
            if (!isOwner) {
                return { allowed: false, reason: 'Resource not owned by user' };
            }
        }
        // Check department condition
        if (conditions.departmentOnly && permissionCheck.context?.departmentId) {
            const inDepartment = await checkDepartmentMembership(permissionCheck.userId, permissionCheck.context.departmentId);
            if (!inDepartment) {
                return { allowed: false, reason: 'User not in required department' };
            }
        }
        // Check time restrictions
        if (conditions.timeRestrictions) {
            const timeAllowed = checkTimeRestrictions(conditions.timeRestrictions);
            if (!timeAllowed) {
                return { allowed: false, reason: 'Access not allowed at this time' };
            }
        }
        // Check IP restrictions
        if (conditions.ipRestrictions && conditions.ipRestrictions.length > 0 && request) {
            // Production client IP detection from Azure Functions request
            const clientIP = getClientIP(request);
            logger_1.logger.info('Checking IP restrictions', {
                clientIP,
                allowedIPs: conditions.ipRestrictions,
                userId: permissionCheck.userId
            });
            if (!isIPAllowed(clientIP, conditions.ipRestrictions)) {
                logger_1.logger.warn('Access denied due to IP restriction', {
                    clientIP,
                    allowedIPs: conditions.ipRestrictions,
                    userId: permissionCheck.userId
                });
                return { allowed: false, reason: `Access not allowed from IP: ${clientIP}` };
            }
        }
        return { allowed: true, reason: 'All conditions satisfied' };
    }
    catch (error) {
        logger_1.logger.error('Failed to evaluate permission conditions', { error, conditions, permissionCheck });
        return { allowed: false, reason: 'Condition evaluation failed' };
    }
}
async function checkResourceOwnership(userId, resourceId, resourceType) {
    try {
        const collectionMap = {
            [ResourceType.DOCUMENT]: 'documents',
            [ResourceType.WORKFLOW]: 'workflows',
            [ResourceType.PROJECT]: 'projects',
            [ResourceType.ORGANIZATION]: 'organizations',
            [ResourceType.USER]: 'users',
            [ResourceType.TEMPLATE]: 'templates',
            [ResourceType.INTEGRATION]: 'integrations',
            [ResourceType.REPORT]: 'reports'
        };
        const collection = collectionMap[resourceType];
        const resource = await database_1.db.readItem(collection, resourceId, resourceId);
        return !!(resource && resource.createdBy === userId);
    }
    catch (error) {
        logger_1.logger.error('Failed to check resource ownership', { error, userId, resourceId, resourceType });
        return false;
    }
}
async function checkDepartmentMembership(userId, departmentId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.departmentId = @deptId AND c.status = @status';
        const memberships = await database_1.db.queryItems('department-members', membershipQuery, [userId, departmentId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check department membership', { error, userId, departmentId });
        return false;
    }
}
function checkTimeRestrictions(timeRestrictions) {
    try {
        const now = new Date();
        const currentHour = now.getHours();
        const currentDay = now.getDay();
        // Check allowed hours
        if (timeRestrictions.allowedHours && !timeRestrictions.allowedHours.includes(currentHour)) {
            return false;
        }
        // Check allowed days
        if (timeRestrictions.allowedDays && !timeRestrictions.allowedDays.includes(currentDay)) {
            return false;
        }
        return true;
    }
    catch (error) {
        logger_1.logger.error('Failed to check time restrictions', { error, timeRestrictions });
        return false;
    }
}
async function checkInheritedPermissions(roles, permissionCheck) {
    try {
        // Check if any role inherits permissions that would grant access
        for (const role of roles) {
            if (role.hierarchy.inheritsFrom) {
                const parentRole = await database_1.db.readItem('advanced-roles', role.hierarchy.inheritsFrom, role.hierarchy.inheritsFrom);
                if (parentRole) {
                    const parentPermission = await checkRolePermissions(parentRole, permissionCheck);
                    if (parentPermission.allowed) {
                        return {
                            allowed: true,
                            reason: `Permission inherited from role: ${parentRole.name}`,
                            roles: [role.name, parentRole.name],
                            conditions: parentPermission.conditions
                        };
                    }
                }
            }
        }
        return { allowed: false, reason: 'No inherited permissions found' };
    }
    catch (error) {
        logger_1.logger.error('Failed to check inherited permissions', { error, permissionCheck });
        return { allowed: false, reason: 'Inherited permission check failed' };
    }
}
/**
 * Extract client IP address from Azure Functions request
 */
function getClientIP(request) {
    try {
        // Check for Azure Front Door headers first
        const frontDoorIP = request.headers.get('x-azure-clientip');
        if (frontDoorIP) {
            return frontDoorIP;
        }
        // Check for standard forwarded headers
        const forwardedFor = request.headers.get('x-forwarded-for');
        if (forwardedFor) {
            // X-Forwarded-For can contain multiple IPs, take the first one
            const ips = forwardedFor.split(',').map(ip => ip.trim());
            return ips[0];
        }
        // Check for real IP header
        const realIP = request.headers.get('x-real-ip');
        if (realIP) {
            return realIP;
        }
        // Check for Azure-specific headers
        const azureClientIP = request.headers.get('x-client-ip');
        if (azureClientIP) {
            return azureClientIP;
        }
        // Fallback to remote address (may not be available in Azure Functions)
        const remoteAddr = request.headers.get('x-forwarded-host');
        if (remoteAddr) {
            return remoteAddr;
        }
        logger_1.logger.warn('Unable to determine client IP, using fallback');
        return '0.0.0.0'; // Fallback IP
    }
    catch (error) {
        logger_1.logger.error('Error extracting client IP', {
            error: error instanceof Error ? error.message : String(error)
        });
        return '0.0.0.0';
    }
}
/**
 * Check if IP is allowed based on IP restrictions (supports CIDR notation)
 */
function isIPAllowed(clientIP, allowedIPs) {
    try {
        for (const allowedIP of allowedIPs) {
            if (allowedIP.includes('/')) {
                // CIDR notation support
                if (isIPInCIDR(clientIP, allowedIP)) {
                    return true;
                }
            }
            else {
                // Exact IP match
                if (clientIP === allowedIP) {
                    return true;
                }
                // Wildcard support (e.g., 192.168.1.*)
                if (allowedIP.includes('*')) {
                    const pattern = allowedIP.replace(/\*/g, '.*');
                    const regex = new RegExp(`^${pattern}$`);
                    if (regex.test(clientIP)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Error checking IP allowlist', {
            error: error instanceof Error ? error.message : String(error),
            clientIP,
            allowedIPs
        });
        return false;
    }
}
/**
 * Check if IP is within CIDR range
 */
function isIPInCIDR(ip, cidr) {
    try {
        const [network, prefixLength] = cidr.split('/');
        const prefix = parseInt(prefixLength, 10);
        const ipParts = ip.split('.').map(Number);
        const networkParts = network.split('.').map(Number);
        // Convert to 32-bit integers
        const ipInt = (ipParts[0] << 24) + (ipParts[1] << 16) + (ipParts[2] << 8) + ipParts[3];
        const networkInt = (networkParts[0] << 24) + (networkParts[1] << 16) + (networkParts[2] << 8) + networkParts[3];
        // Create subnet mask
        const mask = (-1 << (32 - prefix)) >>> 0;
        return (ipInt & mask) === (networkInt & mask);
    }
    catch (error) {
        logger_1.logger.error('Error checking CIDR range', {
            error: error instanceof Error ? error.message : String(error),
            ip,
            cidr
        });
        return false;
    }
}
// Register functions
functions_1.app.http('advanced-role-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'management/advanced-roles',
    handler: createAdvancedRole
});
functions_1.app.http('advanced-permission-check', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'permissions/check-advanced',
    handler: checkAdvancedPermission
});
//# sourceMappingURL=advanced-permissions.js.map