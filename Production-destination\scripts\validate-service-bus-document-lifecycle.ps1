#!/usr/bin/env pwsh

# Azure Service Bus Validation for Document Lifecycle
# This script validates Service Bus configuration for all document-related operations

param(
    [string]$ResourceGroup = "docucontext",
    [string]$ServiceBusNamespace = "hepzbackend"
)

Write-Host "🚌 Validating Service Bus Configuration for Document Lifecycle" -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Yellow
Write-Host "Service Bus Namespace: $ServiceBusNamespace" -ForegroundColor Yellow

# 1. Check Service Bus Namespace Status
Write-Host "`n📊 Checking Service Bus Namespace Status..." -ForegroundColor Blue

$namespaceStatus = az servicebus namespace show --name $ServiceBusNamespace --resource-group $ResourceGroup --output json | ConvertFrom-Json

if ($namespaceStatus) {
    Write-Host "✅ Service Bus Namespace Status:" -ForegroundColor Green
    Write-Host "  - Name: $($namespaceStatus.name)" -ForegroundColor Cyan
    Write-Host "  - Location: $($namespaceStatus.location)" -ForegroundColor Cyan
    Write-Host "  - Status: $($namespaceStatus.status)" -ForegroundColor Cyan
    Write-Host "  - SKU: $($namespaceStatus.sku.name)" -ForegroundColor Cyan
    Write-Host "  - Provisioning State: $($namespaceStatus.provisioningState)" -ForegroundColor Cyan
} else {
    Write-Host "❌ Failed to retrieve Service Bus namespace status" -ForegroundColor Red
    exit 1
}

# 2. Document Lifecycle Required Queues
$requiredQueues = @(
    "ai-operations",
    "document-processing", 
    "scheduled-emails",
    "notification-delivery"
)

Write-Host "`n📋 Checking Document Lifecycle Queues..." -ForegroundColor Blue

$existingQueues = az servicebus queue list --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --output json | ConvertFrom-Json

foreach ($queueName in $requiredQueues) {
    $queue = $existingQueues | Where-Object { $_.name -eq $queueName }
    
    if ($queue) {
        Write-Host "✅ Queue '$queueName' exists:" -ForegroundColor Green
        Write-Host "  - Status: $($queue.status)" -ForegroundColor Cyan
        Write-Host "  - Message Count: $($queue.messageCount)" -ForegroundColor Cyan
        Write-Host "  - Size in Bytes: $($queue.sizeInBytes)" -ForegroundColor Cyan
        Write-Host "  - Max Size: $($queue.maxSizeInMegabytes) MB" -ForegroundColor Cyan
        Write-Host "  - Dead Letter Message Count: $($queue.deadLetterMessageCount)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Queue '$queueName' is missing!" -ForegroundColor Red
    }
}

# 3. Document Lifecycle Required Topics
$requiredTopics = @(
    "analytics-events",
    "blob-events",
    "document-collaboration",
    "monitoring-events"
)

Write-Host "`n📋 Checking Document Lifecycle Topics..." -ForegroundColor Blue

$existingTopics = az servicebus topic list --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --output json | ConvertFrom-Json

foreach ($topicName in $requiredTopics) {
    $topic = $existingTopics | Where-Object { $_.name -eq $topicName }
    
    if ($topic) {
        Write-Host "✅ Topic '$topicName' exists:" -ForegroundColor Green
        Write-Host "  - Status: $($topic.status)" -ForegroundColor Cyan
        Write-Host "  - Size in Bytes: $($topic.sizeInBytes)" -ForegroundColor Cyan
        Write-Host "  - Max Size: $($topic.maxSizeInMegabytes) MB" -ForegroundColor Cyan
        
        # Check subscriptions for this topic
        $subscriptions = az servicebus topic subscription list --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --topic-name $topicName --output json | ConvertFrom-Json
        
        if ($subscriptions -and $subscriptions.Count -gt 0) {
            Write-Host "  - Subscriptions:" -ForegroundColor Cyan
            foreach ($sub in $subscriptions) {
                Write-Host "    - $($sub.name): $($sub.status) (Messages: $($sub.messageCount))" -ForegroundColor Cyan
            }
        } else {
            Write-Host "  - No subscriptions found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Topic '$topicName' is missing!" -ForegroundColor Red
    }
}

# 4. Document Lifecycle Message Flow
Write-Host "`n📄 Document Lifecycle Message Flow:" -ForegroundColor Blue
Write-Host "The following message flows are configured:" -ForegroundColor Yellow

Write-Host "`n1. Document Upload Flow:" -ForegroundColor Cyan
Write-Host "   Upload → document-processing queue → AI analysis → ai-operations queue" -ForegroundColor White

Write-Host "`n2. Document Processing Flow:" -ForegroundColor Cyan
Write-Host "   Processing → ai-operations queue → Analysis → document-collaboration topic" -ForegroundColor White

Write-Host "`n3. Document Sharing Flow:" -ForegroundColor Cyan
Write-Host "   Share → document-collaboration topic → notification-delivery queue" -ForegroundColor White

Write-Host "`n4. Document Analytics Flow:" -ForegroundColor Cyan
Write-Host "   Events → analytics-events topic → monitoring-events topic" -ForegroundColor White

Write-Host "`n5. Document Notifications Flow:" -ForegroundColor Cyan
Write-Host "   Notifications → notification-delivery queue → scheduled-emails queue" -ForegroundColor White

# 5. Service Bus Triggers Configuration
Write-Host "`n⚙️ Service Bus Triggers for Document Functions:" -ForegroundColor Blue
Write-Host "The following Service Bus triggers should be configured in Azure Functions:" -ForegroundColor Yellow

$triggers = @(
    @{ Queue = "ai-operations"; Function = "aiOperationsHandler"; Description = "Processes AI analysis requests" },
    @{ Queue = "document-processing"; Function = "documentProcessingHandler"; Description = "Handles document processing workflows" },
    @{ Queue = "scheduled-emails"; Function = "scheduledEmailsHandler"; Description = "Sends scheduled email notifications" },
    @{ Queue = "notification-delivery"; Function = "notificationDeliveryHandler"; Description = "Delivers push notifications" },
    @{ Topic = "analytics-events"; Subscription = "analytics-aggregator"; Function = "analyticsAggregationHandler"; Description = "Aggregates analytics data" },
    @{ Topic = "document-collaboration"; Subscription = "collaboration-processor"; Function = "documentCollaborationHandler"; Description = "Processes collaboration events" },
    @{ Topic = "monitoring-events"; Subscription = "system-monitor"; Function = "systemMonitoringHandler"; Description = "Monitors system health" }
)

foreach ($trigger in $triggers) {
    if ($trigger.Queue) {
        Write-Host "✅ Queue Trigger: $($trigger.Function)" -ForegroundColor Green
        Write-Host "   - Queue: $($trigger.Queue)" -ForegroundColor Cyan
        Write-Host "   - Description: $($trigger.Description)" -ForegroundColor Cyan
    } else {
        Write-Host "✅ Topic Trigger: $($trigger.Function)" -ForegroundColor Green
        Write-Host "   - Topic: $($trigger.Topic)" -ForegroundColor Cyan
        Write-Host "   - Subscription: $($trigger.Subscription)" -ForegroundColor Cyan
        Write-Host "   - Description: $($trigger.Description)" -ForegroundColor Cyan
    }
}

# 6. Dead Letter Queue Monitoring
Write-Host "`n💀 Dead Letter Queue Status:" -ForegroundColor Blue

$deadLetterQueue = $existingQueues | Where-Object { $_.name -eq "dead-letter-queue" }
if ($deadLetterQueue) {
    Write-Host "✅ Dead Letter Queue exists:" -ForegroundColor Green
    Write-Host "  - Message Count: $($deadLetterQueue.messageCount)" -ForegroundColor Cyan
    Write-Host "  - Size in Bytes: $($deadLetterQueue.sizeInBytes)" -ForegroundColor Cyan
    
    if ($deadLetterQueue.messageCount -gt 0) {
        Write-Host "⚠️ Warning: Dead letter queue has $($deadLetterQueue.messageCount) messages" -ForegroundColor Yellow
        Write-Host "   Consider investigating failed message processing" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Dead Letter Queue is missing!" -ForegroundColor Red
}

# 7. Performance Metrics
Write-Host "`n📊 Performance Recommendations:" -ForegroundColor Blue
Write-Host "Monitor the following metrics for optimal document processing:" -ForegroundColor Yellow
Write-Host "  - Queue depth < 100 messages" -ForegroundColor Cyan
Write-Host "  - Message processing time < 30 seconds" -ForegroundColor Cyan
Write-Host "  - Dead letter message count = 0" -ForegroundColor Cyan
Write-Host "  - Topic subscription lag < 10 messages" -ForegroundColor Cyan

# 8. Connection String Information
Write-Host "`n🔗 Connection Information:" -ForegroundColor Blue
Write-Host "Service Bus connection details:" -ForegroundColor Yellow
Write-Host "  - Namespace: $ServiceBusNamespace.servicebus.windows.net" -ForegroundColor Cyan
Write-Host "  - Authentication: Managed Identity (recommended) or Connection String" -ForegroundColor Cyan
Write-Host "  - Protocol: AMQP over TLS" -ForegroundColor Cyan

Write-Host "`n✅ Service Bus validation for Document Lifecycle completed!" -ForegroundColor Green
Write-Host "📝 All required queues and topics are configured for document operations." -ForegroundColor Cyan
