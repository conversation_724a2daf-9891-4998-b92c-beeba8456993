"use strict";
/**
 * RAG (Retrieval Augmented Generation) Service
 * Production-ready implementation using Azure AI Search for document-based AI reasoning and content generation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ragService = exports.RAGService = void 0;
const logger_1 = require("../utils/logger");
const database_1 = require("./database");
const ai_services_1 = require("./ai-services");
const redis_1 = require("./redis");
class RAGService {
    constructor() {
        this.initialized = false;
        this.CHUNK_SIZE = 1000; // words
        this.OVERLAP_SIZE = 200; // words
        this.SIMILARITY_THRESHOLD = 0.7;
        this.searchConfig = {
            endpoint: process.env.SEARCH_SERVICE_ENDPOINT || '',
            key: process.env.SEARCH_SERVICE_KEY || '',
            indexName: process.env.SEARCH_INDEX_NAME || 'documents',
            semanticConfig: process.env.SEARCH_SEMANTIC_CONFIG || 'default',
            vectorEnabled: process.env.SEARCH_VECTOR_ENABLED === 'true',
            semanticEnabled: process.env.SEARCH_SEMANTIC_ENABLED === 'true'
        };
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            await ai_services_1.aiServices.initialize();
            // Validate Azure AI Search configuration
            if (!this.searchConfig.endpoint || !this.searchConfig.key) {
                throw new Error('Azure AI Search endpoint and key must be configured');
            }
            // Test connection to Azure AI Search
            await this.testSearchConnection();
            this.initialized = true;
            logger_1.logger.info('RAG Service with Azure AI Search initialized successfully', {
                endpoint: this.searchConfig.endpoint,
                indexName: this.searchConfig.indexName,
                vectorEnabled: this.searchConfig.vectorEnabled,
                semanticEnabled: this.searchConfig.semanticEnabled
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize RAG Service', { error });
            throw error;
        }
    }
    /**
     * Test Azure AI Search connection
     */
    async testSearchConnection() {
        try {
            const url = `${this.searchConfig.endpoint}/indexes/${this.searchConfig.indexName}?api-version=2023-11-01`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'api-key': this.searchConfig.key,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                if (response.status === 404) {
                    logger_1.logger.warn('Search index does not exist, will be created on first indexing', {
                        indexName: this.searchConfig.indexName
                    });
                }
                else {
                    throw new Error(`Azure AI Search connection failed: ${response.status} ${response.statusText}`);
                }
            }
            else {
                logger_1.logger.info('Azure AI Search connection verified successfully');
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to test Azure AI Search connection', { error });
            throw error;
        }
    }
    /**
     * Index a document for RAG retrieval using Azure AI Search
     */
    async indexDocument(request) {
        await this.initialize();
        const startTime = Date.now();
        const chunkSize = request.chunkSize || this.CHUNK_SIZE;
        const overlapSize = request.overlapSize || this.OVERLAP_SIZE;
        try {
            logger_1.logger.info('Starting document indexing for Azure AI Search RAG', {
                documentId: request.documentId,
                contentLength: request.content.length,
                chunkSize,
                overlapSize
            });
            // Get document metadata from database
            const document = await database_1.db.readItem('documents', request.documentId, request.documentId);
            if (!document) {
                throw new Error(`Document ${request.documentId} not found`);
            }
            // Ensure search index exists
            await this.ensureSearchIndex();
            // Split document into chunks
            const chunks = this.splitIntoChunks(request.content, chunkSize, overlapSize);
            // Generate search documents with embeddings
            const searchDocuments = [];
            for (let i = 0; i < chunks.length; i++) {
                const chunk = chunks[i];
                try {
                    const embeddingResponse = await ai_services_1.aiServices.generateEmbeddings(chunk.content);
                    const searchDocument = {
                        id: `${request.documentId}-chunk-${i}`,
                        documentId: request.documentId,
                        title: document.name || 'Untitled Document',
                        content: chunk.content,
                        contentVector: embeddingResponse.embedding,
                        chunkIndex: i,
                        pageNumber: chunk.pageNumber || undefined,
                        section: chunk.section || undefined,
                        documentType: document.contentType || 'unknown',
                        organizationId: document.organizationId,
                        projectId: document.projectId,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                        metadata: {
                            fileName: document.name || 'unknown',
                            contentType: document.contentType || 'unknown',
                            size: document.size || 0,
                            author: document.createdBy || 'unknown',
                            tags: document.tags || [],
                            wordCount: chunk.wordCount,
                            extractedAt: new Date().toISOString(),
                            ...request.metadata
                        }
                    };
                    searchDocuments.push(searchDocument);
                }
                catch (error) {
                    logger_1.logger.warn('Failed to generate embedding for chunk', {
                        documentId: request.documentId,
                        chunkIndex: i,
                        error
                    });
                }
            }
            // Index documents in Azure AI Search
            await this.indexDocumentsInSearch(searchDocuments);
            // Cache document chunks for quick access
            const cacheKey = `rag:chunks:${request.documentId}`;
            await redis_1.redis.setex(cacheKey, 86400, JSON.stringify(searchDocuments)); // 24 hours
            logger_1.logger.info('Document indexing completed in Azure AI Search', {
                documentId: request.documentId,
                chunksCreated: searchDocuments.length,
                processingTime: Date.now() - startTime
            });
        }
        catch (error) {
            logger_1.logger.error('Document indexing failed', {
                documentId: request.documentId,
                error
            });
            throw error;
        }
    }
    /**
     * Ensure Azure AI Search index exists with proper schema
     */
    async ensureSearchIndex() {
        try {
            const indexSchema = {
                name: this.searchConfig.indexName,
                fields: [
                    { name: 'id', type: 'Edm.String', key: true, searchable: false, filterable: true, retrievable: true },
                    { name: 'documentId', type: 'Edm.String', searchable: false, filterable: true, retrievable: true },
                    { name: 'title', type: 'Edm.String', searchable: true, filterable: false, retrievable: true },
                    { name: 'content', type: 'Edm.String', searchable: true, filterable: false, retrievable: true },
                    { name: 'contentVector', type: 'Collection(Edm.Single)', searchable: true, retrievable: false, dimensions: 1536, vectorSearchProfile: 'vector-profile' },
                    { name: 'chunkIndex', type: 'Edm.Int32', searchable: false, filterable: true, retrievable: true },
                    { name: 'pageNumber', type: 'Edm.Int32', searchable: false, filterable: true, retrievable: true },
                    { name: 'section', type: 'Edm.String', searchable: true, filterable: true, retrievable: true },
                    { name: 'documentType', type: 'Edm.String', searchable: false, filterable: true, retrievable: true },
                    { name: 'organizationId', type: 'Edm.String', searchable: false, filterable: true, retrievable: true },
                    { name: 'projectId', type: 'Edm.String', searchable: false, filterable: true, retrievable: true },
                    { name: 'createdAt', type: 'Edm.DateTimeOffset', searchable: false, filterable: true, retrievable: true },
                    { name: 'updatedAt', type: 'Edm.DateTimeOffset', searchable: false, filterable: true, retrievable: true }
                ],
                vectorSearch: {
                    algorithms: [
                        {
                            name: 'vector-algorithm',
                            kind: 'hnsw',
                            hnswParameters: {
                                metric: 'cosine',
                                m: 4,
                                efConstruction: 400,
                                efSearch: 500
                            }
                        }
                    ],
                    profiles: [
                        {
                            name: 'vector-profile',
                            algorithm: 'vector-algorithm'
                        }
                    ]
                },
                semantic: this.searchConfig.semanticEnabled ? {
                    configurations: [
                        {
                            name: this.searchConfig.semanticConfig,
                            prioritizedFields: {
                                titleField: { fieldName: 'title' },
                                prioritizedContentFields: [
                                    { fieldName: 'content' }
                                ],
                                prioritizedKeywordsFields: [
                                    { fieldName: 'section' }
                                ]
                            }
                        }
                    ]
                } : undefined
            };
            const url = `${this.searchConfig.endpoint}/indexes/${this.searchConfig.indexName}?api-version=2023-11-01`;
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'api-key': this.searchConfig.key,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(indexSchema)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to create/update search index: ${response.status} ${errorText}`);
            }
            logger_1.logger.info('Azure AI Search index ensured successfully', {
                indexName: this.searchConfig.indexName
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to ensure search index', { error });
            throw error;
        }
    }
    /**
     * Index documents in Azure AI Search
     */
    async indexDocumentsInSearch(documents) {
        try {
            const batchSize = 100; // Azure AI Search batch limit
            for (let i = 0; i < documents.length; i += batchSize) {
                const batch = documents.slice(i, i + batchSize);
                const indexRequest = {
                    value: batch.map(doc => ({
                        '@search.action': 'mergeOrUpload',
                        ...doc
                    }))
                };
                const url = `${this.searchConfig.endpoint}/indexes/${this.searchConfig.indexName}/docs/index?api-version=2023-11-01`;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'api-key': this.searchConfig.key,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(indexRequest)
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Failed to index documents batch: ${response.status} ${errorText}`);
                }
                const result = await response.json();
                const failedDocs = result.value?.filter((r) => !r.status || r.status !== 201);
                if (failedDocs && failedDocs.length > 0) {
                    logger_1.logger.warn('Some documents failed to index', {
                        failedCount: failedDocs.length,
                        totalCount: batch.length,
                        failures: failedDocs
                    });
                }
            }
            logger_1.logger.info('Documents indexed successfully in Azure AI Search', {
                totalDocuments: documents.length
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to index documents in Azure AI Search', { error });
            throw error;
        }
    }
    /**
     * Perform RAG query - retrieve relevant documents and generate answer using Azure AI Search
     */
    async query(ragQuery) {
        await this.initialize();
        const startTime = Date.now();
        const maxResults = ragQuery.maxResults || 5;
        try {
            logger_1.logger.info('Starting Azure AI Search RAG query', {
                query: ragQuery.query,
                organizationId: ragQuery.organizationId,
                maxResults,
                vectorEnabled: this.searchConfig.vectorEnabled,
                semanticEnabled: this.searchConfig.semanticEnabled
            });
            // Generate embedding for the query if vector search is enabled
            let queryEmbedding = null;
            if (this.searchConfig.vectorEnabled) {
                queryEmbedding = await ai_services_1.aiServices.generateEmbeddings(ragQuery.query);
            }
            // Retrieve relevant document chunks using Azure AI Search
            const relevantChunks = await this.searchRelevantChunks(ragQuery, queryEmbedding?.embedding, maxResults);
            if (relevantChunks.length === 0) {
                return {
                    answer: "I couldn't find any relevant information in the documents to answer your question.",
                    sources: [],
                    confidence: 0,
                    tokensUsed: queryEmbedding?.tokensUsed || 0,
                    processingTime: Date.now() - startTime
                };
            }
            // Prepare context for AI generation
            const context = relevantChunks.map(chunk => chunk.content);
            const sourceInfo = relevantChunks.map(chunk => ({
                documentId: chunk.documentId,
                content: chunk.content,
                relevanceScore: chunk.score,
                pageNumber: chunk.pageNumber,
                section: chunk.section,
                title: chunk.title
            }));
            // Generate answer using DeepSeek R1 for reasoning
            const aiResponse = await ai_services_1.aiServices.reason(`Based on the provided context, please answer the following question: ${ragQuery.query}`, context, {
                systemPrompt: `You are an AI assistant that answers questions based on provided document context.
          Always base your answers on the given context and cite relevant information.
          If the context doesn't contain enough information to answer the question, say so clearly.
          Provide reasoning for your answer.`,
                maxTokens: 2000,
                temperature: 0.3
            });
            // Get document names for sources
            const documentIds = [...new Set(relevantChunks.map(chunk => chunk.documentId))];
            const documents = await this.getDocumentNames(documentIds);
            const sources = sourceInfo.map(source => ({
                documentId: source.documentId,
                documentName: documents[source.documentId] || source.title || 'Unknown Document',
                content: source.content.substring(0, 200) + '...',
                relevanceScore: source.relevanceScore,
                pageNumber: source.pageNumber,
                section: source.section
            }));
            const result = {
                answer: aiResponse.content,
                reasoning: aiResponse.reasoning,
                sources,
                confidence: aiResponse.confidence,
                tokensUsed: (queryEmbedding?.tokensUsed || 0) + aiResponse.tokensUsed,
                processingTime: Date.now() - startTime
            };
            logger_1.logger.info('Azure AI Search RAG query completed', {
                query: ragQuery.query,
                sourcesFound: sources.length,
                confidence: result.confidence,
                processingTime: result.processingTime
            });
            return result;
        }
        catch (error) {
            logger_1.logger.error('Azure AI Search RAG query failed', {
                query: ragQuery.query,
                error
            });
            throw error;
        }
    }
    /**
     * Search relevant chunks using Azure AI Search
     */
    async searchRelevantChunks(ragQuery, queryVector, maxResults = 5) {
        try {
            const searchRequest = {
                search: ragQuery.query,
                top: maxResults,
                select: 'id,documentId,title,content,chunkIndex,pageNumber,section,documentType,organizationId,projectId,createdAt,updatedAt',
                filter: `organizationId eq '${ragQuery.organizationId}'`,
                queryType: this.searchConfig.semanticEnabled ? 'semantic' : 'simple',
                semanticConfiguration: this.searchConfig.semanticEnabled ? this.searchConfig.semanticConfig : undefined,
                count: true
            };
            // Add project filter if specified
            if (ragQuery.projectId) {
                searchRequest.filter += ` and projectId eq '${ragQuery.projectId}'`;
            }
            // Add document filter if specified
            if (ragQuery.documentIds && ragQuery.documentIds.length > 0) {
                const documentFilter = ragQuery.documentIds.map(id => `documentId eq '${id}'`).join(' or ');
                searchRequest.filter += ` and (${documentFilter})`;
            }
            // Add vector search if enabled and embedding is available
            if (this.searchConfig.vectorEnabled && queryVector) {
                searchRequest.vectors = [{
                        value: queryVector,
                        fields: 'contentVector',
                        k: maxResults
                    }];
            }
            const url = `${this.searchConfig.endpoint}/indexes/${this.searchConfig.indexName}/docs/search?api-version=2023-11-01`;
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'api-key': this.searchConfig.key,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchRequest)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Azure AI Search query failed: ${response.status} ${errorText}`);
            }
            const result = await response.json();
            logger_1.logger.info('Azure AI Search query executed', {
                query: ragQuery.query,
                totalResults: result['@odata.count'],
                returnedResults: result.value?.length || 0,
                vectorSearch: !!queryVector,
                semanticSearch: this.searchConfig.semanticEnabled
            });
            return result.value || [];
        }
        catch (error) {
            logger_1.logger.error('Failed to search relevant chunks in Azure AI Search', { error });
            throw error;
        }
    }
    /**
     * Split content into overlapping chunks
     */
    splitIntoChunks(content, chunkSize, overlapSize) {
        const words = content.split(/\s+/);
        const chunks = [];
        let startIndex = 0;
        while (startIndex < words.length) {
            const endIndex = Math.min(startIndex + chunkSize, words.length);
            const chunkWords = words.slice(startIndex, endIndex);
            const chunkContent = chunkWords.join(' ');
            chunks.push({
                content: chunkContent,
                wordCount: chunkWords.length,
                pageNumber: undefined,
                section: undefined
            });
            // Move start index forward, accounting for overlap
            startIndex = endIndex - overlapSize;
            // Prevent infinite loop
            if (startIndex >= endIndex) {
                break;
            }
        }
        return chunks;
    }
    /**
     * Remove document from Azure AI Search index
     */
    async removeDocumentFromIndex(documentId) {
        try {
            // Search for all chunks of this document
            const searchRequest = {
                search: '*',
                filter: `documentId eq '${documentId}'`,
                select: 'id',
                top: 1000 // Maximum batch size
            };
            const url = `${this.searchConfig.endpoint}/indexes/${this.searchConfig.indexName}/docs/search?api-version=2023-11-01`;
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'api-key': this.searchConfig.key,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchRequest)
            });
            if (!response.ok) {
                throw new Error(`Failed to search for document chunks: ${response.status}`);
            }
            const result = await response.json();
            const chunks = result.value || [];
            if (chunks.length === 0) {
                logger_1.logger.info('No chunks found to remove for document', { documentId });
                return;
            }
            // Delete chunks in batches
            const deleteRequest = {
                value: chunks.map((chunk) => ({
                    '@search.action': 'delete',
                    id: chunk.id
                }))
            };
            const deleteUrl = `${this.searchConfig.endpoint}/indexes/${this.searchConfig.indexName}/docs/index?api-version=2023-11-01`;
            const deleteResponse = await fetch(deleteUrl, {
                method: 'POST',
                headers: {
                    'api-key': this.searchConfig.key,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(deleteRequest)
            });
            if (!deleteResponse.ok) {
                const errorText = await deleteResponse.text();
                throw new Error(`Failed to delete document chunks: ${deleteResponse.status} ${errorText}`);
            }
            // Remove from cache
            const cacheKey = `rag:chunks:${documentId}`;
            await redis_1.redis.del(cacheKey);
            logger_1.logger.info('Document removed from Azure AI Search index', {
                documentId,
                chunksRemoved: chunks.length
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to remove document from Azure AI Search index', {
                documentId,
                error
            });
            throw error;
        }
    }
    /**
     * Get document names for source attribution
     */
    async getDocumentNames(documentIds) {
        try {
            const documents = {};
            for (const documentId of documentIds) {
                try {
                    const doc = await database_1.db.readItem('documents', documentId, documentId);
                    documents[documentId] = doc?.name || 'Unknown Document';
                }
                catch (error) {
                    documents[documentId] = 'Unknown Document';
                }
            }
            return documents;
        }
        catch (error) {
            logger_1.logger.error('Failed to get document names', { error });
            return {};
        }
    }
}
exports.RAGService = RAGService;
// Export singleton instance
exports.ragService = new RAGService();
//# sourceMappingURL=rag-service.js.map