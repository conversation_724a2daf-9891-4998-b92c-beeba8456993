/**
 * RAG Query Function
 * Handles Retrieval Augmented Generation queries for document-based AI reasoning
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * RAG Query handler
 */
export declare function ragQuery(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get RAG query history
 */
export declare function getRagQueryHistory(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
