"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDocumentVersion = createDocumentVersion;
exports.restoreDocumentVersion = restoreDocumentVersion;
/**
 * Document Versioning Function
 * Handles document version management, comparison, and restoration
 * Migrated from old-arch/src/document-service/versioning/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
const service_bus_1 = require("../shared/services/service-bus");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Version types and enums
var VersionType;
(function (VersionType) {
    VersionType["MAJOR"] = "major";
    VersionType["MINOR"] = "minor";
    VersionType["PATCH"] = "patch";
    VersionType["AUTO"] = "auto";
})(VersionType || (VersionType = {}));
var VersionStatus;
(function (VersionStatus) {
    VersionStatus["ACTIVE"] = "active";
    VersionStatus["ARCHIVED"] = "archived";
    VersionStatus["DELETED"] = "deleted";
})(VersionStatus || (VersionStatus = {}));
// Validation schemas
const createVersionSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    versionType: Joi.string().valid(...Object.values(VersionType)).default(VersionType.AUTO),
    comment: Joi.string().max(500).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).default([]),
    isMinor: Joi.boolean().default(false)
});
const restoreVersionSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    versionId: Joi.string().uuid().required(),
    comment: Joi.string().max(500).optional(),
    createBackup: Joi.boolean().default(true)
});
/**
 * Create document version handler
 */
async function createDocumentVersion(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create document version started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Initialize services
        const serviceBusService = service_bus_1.ServiceBusEnhancedService.getInstance();
        await serviceBusService.initialize();
        // Validate request body
        const body = await request.json();
        const { error, value } = createVersionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const versionRequest = value;
        // Get document and verify access
        const document = await database_1.db.readItem('documents', versionRequest.documentId, versionRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check edit permissions
        const hasEditPermission = await checkDocumentEditPermission(documentData, user);
        if (!hasEditPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to create document versions" }
            }, request);
        }
        // Get current version number
        const currentVersionNumber = await getCurrentVersionNumber(versionRequest.documentId);
        const newVersionNumber = calculateNextVersionNumber(currentVersionNumber, versionRequest.versionType);
        // Create version from current document
        const versionId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        // Copy current document to version blob storage
        const versionBlobName = await createVersionBlob(documentData, versionId);
        // Calculate content hash for integrity
        const contentHash = await calculateContentHash(documentData.blobName);
        // Detect changes from previous version
        const changes = await detectDocumentChanges(versionRequest.documentId, documentData);
        const documentVersion = {
            id: versionId,
            documentId: versionRequest.documentId,
            versionNumber: newVersionNumber,
            versionType: versionRequest.versionType,
            status: VersionStatus.ACTIVE,
            comment: versionRequest.comment,
            tags: versionRequest.tags,
            fileSize: documentData.size || 0,
            contentHash,
            blobName: versionBlobName,
            createdBy: user.id,
            createdAt: now,
            metadata: {
                changes,
                previousVersionId: await getPreviousVersionId(versionRequest.documentId),
                isRestored: false
            }
        };
        await database_1.db.createItem('document-versions', documentVersion);
        // Update document with latest version info
        const updatedDocument = {
            ...documentData,
            currentVersionId: versionId,
            currentVersionNumber: newVersionNumber,
            versionCount: (documentData.versionCount || 0) + 1,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Cache version metadata in Redis
        await redis_1.redis.setJson(`doc:${versionRequest.documentId}:version:${versionId}`, {
            id: versionId,
            documentId: versionRequest.documentId,
            versionNumber: newVersionNumber,
            versionType: versionRequest.versionType,
            comment: versionRequest.comment,
            createdBy: user.id,
            createdAt: now,
            fileSize: documentVersion.fileSize
        }, 3600); // 1 hour cache
        // Invalidate document cache
        await redis_1.redis.del(`doc:${versionRequest.documentId}:metadata`);
        await redis_1.redis.del(`doc:${versionRequest.documentId}:versions`);
        // Publish Event Grid event for version created
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: 'Document.VersionCreated',
            subject: `documents/${versionRequest.documentId}/versions/created`,
            data: {
                documentId: versionRequest.documentId,
                documentName: documentData.name,
                versionId,
                versionNumber: newVersionNumber,
                versionType: versionRequest.versionType,
                comment: versionRequest.comment,
                createdBy: user.id,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId,
                changeCount: changes.length,
                createdAt: now,
                correlationId
            }
        });
        // Send message to Service Bus for version workflow
        await serviceBusService.sendToQueue('document-processing', {
            body: {
                documentId: versionRequest.documentId,
                versionId,
                action: 'version-created',
                versionNumber: newVersionNumber,
                versionType: versionRequest.versionType,
                createdBy: user.id,
                organizationId: documentData.organizationId,
                projectId: documentData.projectId,
                changeCount: changes.length,
                correlationId,
                timestamp: now
            },
            messageId: `version-created-${versionId}-${Date.now()}`,
            correlationId,
            subject: 'document.version.created'
        });
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_version_created",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: versionRequest.documentId,
            timestamp: now,
            details: {
                versionId,
                versionNumber: newVersionNumber,
                versionType: versionRequest.versionType,
                documentName: documentData.name,
                changeCount: changes.length,
                comment: versionRequest.comment
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentVersionCreated',
            aggregateId: versionId,
            aggregateType: 'DocumentVersion',
            version: 1,
            data: {
                version: documentVersion,
                document: documentData,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        // Send notification for major versions
        if (versionRequest.versionType === VersionType.MAJOR) {
            await notification_1.notificationService.sendNotification({
                userId: user.id,
                type: 'DOCUMENT_VERSION_CREATED',
                title: 'Major document version created',
                message: `Version ${newVersionNumber} of "${documentData.name}" has been created.`,
                priority: 'normal',
                metadata: {
                    documentId: versionRequest.documentId,
                    documentName: documentData.name,
                    versionId,
                    versionNumber: newVersionNumber,
                    versionType: versionRequest.versionType,
                    organizationId: documentData.organizationId
                },
                organizationId: documentData.organizationId,
                projectId: documentData.projectId
            });
        }
        logger_1.logger.info("Document version created successfully", {
            correlationId,
            versionId,
            documentId: versionRequest.documentId,
            versionNumber: newVersionNumber,
            versionType: versionRequest.versionType,
            changeCount: changes.length,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: versionId,
                documentId: versionRequest.documentId,
                documentName: documentData.name,
                versionNumber: newVersionNumber,
                versionType: versionRequest.versionType,
                comment: versionRequest.comment,
                tags: versionRequest.tags,
                fileSize: documentVersion.fileSize,
                changes: changes,
                createdAt: now,
                message: "Document version created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create document version failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Restore document version handler
 */
async function restoreDocumentVersion(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Restore document version started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = restoreVersionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const restoreRequest = value;
        // Get document and version
        const [document, version] = await Promise.all([
            database_1.db.readItem('documents', restoreRequest.documentId, restoreRequest.documentId),
            database_1.db.readItem('document-versions', restoreRequest.versionId, restoreRequest.versionId)
        ]);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        if (!version) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Version not found" }
            }, request);
        }
        const documentData = document;
        const versionData = version;
        // Verify version belongs to document
        if (versionData.documentId !== restoreRequest.documentId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Version does not belong to the specified document" }
            }, request);
        }
        // Check edit permissions
        const hasEditPermission = await checkDocumentEditPermission(documentData, user);
        if (!hasEditPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to restore document versions" }
            }, request);
        }
        const now = new Date().toISOString();
        // Create backup of current version if requested
        if (restoreRequest.createBackup) {
            await createDocumentVersion({
                ...request,
                json: async () => ({
                    documentId: restoreRequest.documentId,
                    versionType: VersionType.AUTO,
                    comment: `Backup before restoring to version ${versionData.versionNumber}`,
                    tags: ['backup', 'pre-restore'],
                    isMinor: true
                })
            }, context);
        }
        // Restore version content to document
        await restoreVersionContent(documentData, versionData);
        // Create new version record for the restoration
        const restorationVersionId = (0, uuid_1.v4)();
        const currentVersionNumber = await getCurrentVersionNumber(restoreRequest.documentId);
        const newVersionNumber = calculateNextVersionNumber(currentVersionNumber, VersionType.MINOR);
        const restorationVersion = {
            id: restorationVersionId,
            documentId: restoreRequest.documentId,
            versionNumber: newVersionNumber,
            versionType: VersionType.MINOR,
            status: VersionStatus.ACTIVE,
            comment: restoreRequest.comment || `Restored from version ${versionData.versionNumber}`,
            tags: ['restored'],
            fileSize: versionData.fileSize,
            contentHash: versionData.contentHash,
            blobName: documentData.blobName, // Current document blob
            createdBy: user.id,
            createdAt: now,
            metadata: {
                changes: [`Restored from version ${versionData.versionNumber}`],
                previousVersionId: documentData.currentVersionId,
                isRestored: true,
                restoredFrom: restoreRequest.versionId
            }
        };
        await database_1.db.createItem('document-versions', restorationVersion);
        // Update document
        const updatedDocument = {
            ...documentData,
            currentVersionId: restorationVersionId,
            currentVersionNumber: newVersionNumber,
            versionCount: (documentData.versionCount || 0) + 1,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_version_restored",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: restoreRequest.documentId,
            timestamp: now,
            details: {
                restoredVersionId: restoreRequest.versionId,
                restoredVersionNumber: versionData.versionNumber,
                newVersionId: restorationVersionId,
                newVersionNumber,
                documentName: documentData.name,
                comment: restoreRequest.comment,
                backupCreated: restoreRequest.createBackup
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentVersionRestored',
            aggregateId: restorationVersionId,
            aggregateType: 'DocumentVersion',
            version: 1,
            data: {
                document: documentData,
                restoredVersion: versionData,
                newVersion: restorationVersion,
                restoredBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        // Send notification
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'DOCUMENT_VERSION_RESTORED',
            title: 'Document version restored',
            message: `Document "${documentData.name}" has been restored to version ${versionData.versionNumber}.`,
            priority: 'normal',
            metadata: {
                documentId: restoreRequest.documentId,
                documentName: documentData.name,
                restoredVersionId: restoreRequest.versionId,
                restoredVersionNumber: versionData.versionNumber,
                newVersionId: restorationVersionId,
                newVersionNumber,
                organizationId: documentData.organizationId
            },
            organizationId: documentData.organizationId,
            projectId: documentData.projectId
        });
        logger_1.logger.info("Document version restored successfully", {
            correlationId,
            documentId: restoreRequest.documentId,
            restoredVersionId: restoreRequest.versionId,
            restoredVersionNumber: versionData.versionNumber,
            newVersionId: restorationVersionId,
            newVersionNumber,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId: restoreRequest.documentId,
                documentName: documentData.name,
                restoredVersionId: restoreRequest.versionId,
                restoredVersionNumber: versionData.versionNumber,
                newVersionId: restorationVersionId,
                newVersionNumber,
                backupCreated: restoreRequest.createBackup,
                message: "Document version restored successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Restore document version failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentEditPermission(document, user) {
    // Document owner has edit permission
    if (document.createdBy === user.id) {
        return true;
    }
    // Check organization membership
    if (document.organizationId === user.tenantId) {
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, document.organizationId, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === 'OWNER' || membership.role === 'ADMIN' || membership.role === 'MEMBER';
        }
    }
    return false;
}
async function getCurrentVersionNumber(documentId) {
    try {
        const versionQuery = 'SELECT * FROM c WHERE c.documentId = @docId ORDER BY c.createdAt DESC';
        const versions = await database_1.db.queryItems('document-versions', versionQuery, [documentId]);
        if (versions.length === 0) {
            return '0.0.0';
        }
        return versions[0].versionNumber;
    }
    catch (error) {
        logger_1.logger.error('Failed to get current version number', { error, documentId });
        return '0.0.0';
    }
}
function calculateNextVersionNumber(currentVersion, versionType) {
    const [major, minor, patch] = currentVersion.split('.').map(Number);
    switch (versionType) {
        case VersionType.MAJOR:
            return `${major + 1}.0.0`;
        case VersionType.MINOR:
            return `${major}.${minor + 1}.0`;
        case VersionType.PATCH:
            return `${major}.${minor}.${patch + 1}`;
        case VersionType.AUTO:
        default:
            return `${major}.${minor}.${patch + 1}`;
    }
}
async function createVersionBlob(document, versionId) {
    try {
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        // Copy current document blob to version blob
        const sourceBlobClient = containerClient.getBlobClient(document.blobName);
        const versionBlobName = `versions/${document.organizationId}/${document.id}/${versionId}`;
        const versionBlobClient = containerClient.getBlobClient(versionBlobName);
        await versionBlobClient.beginCopyFromURL(sourceBlobClient.url);
        return versionBlobName;
    }
    catch (error) {
        logger_1.logger.error('Failed to create version blob', { error, documentId: document.id, versionId });
        throw error;
    }
}
async function calculateContentHash(blobName) {
    // Simplified hash calculation - in production, use proper content hashing
    return `hash_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}
async function detectDocumentChanges(documentId, document) {
    // Simplified change detection - in production, implement proper diff analysis
    return [
        'Content updated',
        'Metadata modified',
        'File size changed'
    ];
}
async function getPreviousVersionId(documentId) {
    try {
        const versionQuery = 'SELECT * FROM c WHERE c.documentId = @docId ORDER BY c.createdAt DESC OFFSET 0 LIMIT 1';
        const versions = await database_1.db.queryItems('document-versions', versionQuery, [documentId]);
        return versions.length > 0 ? versions[0].id : undefined;
    }
    catch (error) {
        logger_1.logger.error('Failed to get previous version ID', { error, documentId });
        return undefined;
    }
}
async function restoreVersionContent(document, version) {
    try {
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        // Copy version blob back to current document blob
        const versionBlobClient = containerClient.getBlobClient(version.blobName);
        const documentBlobClient = containerClient.getBlobClient(document.blobName);
        await documentBlobClient.beginCopyFromURL(versionBlobClient.url);
        logger_1.logger.info('Version content restored successfully', {
            documentId: document.id,
            versionId: version.id,
            versionNumber: version.versionNumber
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to restore version content', {
            error,
            documentId: document.id,
            versionId: version.id
        });
        throw error;
    }
}
// Register functions
functions_1.app.http('document-version-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/versions',
    handler: createDocumentVersion
});
functions_1.app.http('document-version-restore', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/versions/{versionId}/restore',
    handler: restoreDocumentVersion
});
//# sourceMappingURL=document-versioning.js.map