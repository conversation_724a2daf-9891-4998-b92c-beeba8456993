"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createOrganization = createOrganization;
/**
 * Organization Create Function
 * Handles creating new organizations with proper tier management
 * Enhanced with notification service and event system integration
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redisEnhanced_1 = require("../shared/services/redisEnhanced");
const serviceBusEnhanced_1 = require("../shared/services/serviceBusEnhanced");
const event_grid_handlers_1 = require("./event-grid-handlers");
const organization_list_1 = require("./organization-list");
const notification_1 = require("../shared/services/notification");
// Organization tiers enum
var OrganizationTier;
(function (OrganizationTier) {
    OrganizationTier["FREE"] = "FREE";
    OrganizationTier["PROFESSIONAL"] = "PROFESSIONAL";
    OrganizationTier["ENTERPRISE"] = "ENTERPRISE";
})(OrganizationTier || (OrganizationTier = {}));
// User roles enum
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["MEMBER"] = "MEMBER";
    UserRole["VIEWER"] = "VIEWER";
})(UserRole || (UserRole = {}));
// Validation schema
const createOrganizationSchema = Joi.object({
    name: Joi.string().required().min(2).max(100),
    description: Joi.string().max(500).optional(),
    tier: Joi.string().valid(...Object.values(OrganizationTier)).default(OrganizationTier.FREE)
});
/**
 * Create organization handler
 */
async function createOrganization(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create organization started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createOrganizationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { name, description, tier } = value;
        // Check if user already has organizations (for free tier limits)
        if (tier === OrganizationTier.FREE) {
            const existingOrgsQuery = 'SELECT * FROM c WHERE c.createdBy = @userId AND c.tier = @tier';
            const existingOrgs = await database_1.db.queryItems('organizations', existingOrgsQuery, [user.id, OrganizationTier.FREE]);
            if (existingOrgs.length >= 1) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: "Free tier limit reached",
                        message: "You can only create one free organization. Please upgrade to create more."
                    }
                }, request);
            }
        }
        // Create organization
        const organizationId = (0, uuid_1.v4)();
        const organization = {
            id: organizationId,
            name,
            description: description || "",
            tier,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            projectIds: [],
            memberIds: [user.id],
            teamIds: [],
            settings: {
                allowedDocumentTypes: ["pdf", "docx", "xlsx", "pptx", "jpg", "png"],
                maxFileSize: 20 * 1024 * 1024, // 20MB
                maxProjects: tier === OrganizationTier.FREE ? 3 :
                    tier === OrganizationTier.PROFESSIONAL ? 10 :
                        tier === OrganizationTier.ENTERPRISE ? 100 : 3,
                features: {
                    aiAnalysis: tier !== OrganizationTier.FREE,
                    advancedWorkflows: tier === OrganizationTier.ENTERPRISE,
                    bulkProcessing: tier !== OrganizationTier.FREE,
                    apiAccess: tier === OrganizationTier.ENTERPRISE
                }
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('organizations', organization);
        // Add creator as admin member
        const membership = {
            id: (0, uuid_1.v4)(),
            userId: user.id,
            organizationId,
            role: UserRole.ADMIN,
            joinedAt: new Date().toISOString(),
            invitedBy: user.id,
            status: "active",
            permissions: [],
            tenantId: user.tenantId
        };
        await database_1.db.createItem('organization-members', membership);
        // Update user's organizations list
        const currentUser = await database_1.db.readItem('users', user.id, user.id);
        if (currentUser) {
            const updatedUser = {
                ...currentUser,
                id: user.id,
                organizationIds: [...(currentUser.organizationIds || []), organizationId],
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('users', updatedUser);
        }
        // Cache organization data in Redis
        const redis = redisEnhanced_1.RedisEnhancedService.getInstance();
        const organizationWithStats = {
            ...organization,
            memberCount: 1,
            projectCount: 0,
            documentCount: 0,
            storageUsed: 0,
            userRole: 'admin'
        };
        await redis.setJson(`org:${organizationId}:details`, organizationWithStats, 1800); // 30 minutes cache
        // Invalidate user organizations cache
        await redis.del(`user:${user.id}:organizations`);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "organization_created",
            userId: user.id,
            organizationId,
            timestamp: new Date().toISOString(),
            details: {
                organizationName: name,
                tier,
                memberCount: 1
            },
            tenantId: user.tenantId
        });
        // Publish Event Grid event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.ORGANIZATION_CREATED, `organizations/${organizationId}/created`, {
            organizationId,
            organizationName: name,
            tier,
            createdBy: user.id,
            memberCount: 1,
            timestamp: new Date().toISOString()
        });
        // Send Service Bus message for workflow orchestration
        const serviceBusService = serviceBusEnhanced_1.ServiceBusEnhancedService.getInstance();
        await serviceBusService.sendToQueue('analytics-events', {
            body: {
                eventType: 'organization_created',
                organizationId,
                organizationName: name,
                tier,
                createdBy: user.id,
                timestamp: new Date().toISOString()
            },
            messageId: `org-create-${organizationId}-${Date.now()}`,
            correlationId: `org-${organizationId}`,
            subject: 'organization.created'
        });
        // Send welcome notification
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'ORGANIZATION_CREATED',
            title: 'Welcome to your new organization!',
            message: `Your organization "${name}" has been created successfully. You can now invite team members and start creating projects.`,
            priority: 'normal',
            metadata: {
                organizationId,
                organizationName: name,
                tier
            },
            organizationId
        });
        logger_1.logger.info("Organization created successfully", {
            correlationId,
            organizationId,
            userId: user.id,
            tier
        });
        // Return the complete organization object
        const responseOrganization = {
            id: organizationId,
            name,
            description: description || "",
            tier,
            createdBy: user.id,
            createdAt: organization.createdAt,
            updatedBy: user.id,
            updatedAt: organization.updatedAt,
            projectIds: [],
            memberIds: [user.id],
            teamIds: [],
            settings: organization.settings,
            memberCount: 1,
            projectCount: 0,
            documentCount: 0,
            storageUsed: 0
        };
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: responseOrganization
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create organization failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined organizations handler
 */
async function handleOrganizations(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createOrganization(request, context);
        case 'GET':
            return await (0, organization_list_1.listOrganizations)(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('organizations', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations',
    handler: handleOrganizations
});
//# sourceMappingURL=organization-create.js.map