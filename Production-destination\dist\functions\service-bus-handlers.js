"use strict";
/**
 * Service Bus Handlers for Azure Functions
 * Handles Service Bus queue and topic message processing
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEnhancedMessage = sendEnhancedMessage;
exports.sendBatchMessages = sendBatchMessages;
exports.processDeadLetterQueue = processDeadLetterQueue;
exports.getServiceBusMetrics = getServiceBusMetrics;
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const database_1 = require("../shared/services/database");
const event_grid_handlers_1 = require("./event-grid-handlers");
const redis_1 = require("../shared/services/redis");
const service_bus_1 = require("../shared/services/service-bus");
let metrics = {
    messagesSent: 0,
    messagesReceived: 0,
    messagesDeadLettered: 0,
    errors: 0,
    averageProcessingTime: 0,
    activeConnections: 0
};
let circuitBreakers = new Map();
/**
 * Initialize shared Service Bus service
 */
async function initializeServiceBus() {
    await service_bus_1.serviceBusEnhanced.initialize();
}
/**
 * AI Operations handler - processes AI operation requests from the queue
 */
async function aiOperationsHandler(message, context) {
    const startTime = Date.now();
    metrics.messagesReceived++;
    try {
        logger_1.logger.info('Processing AI operation from Service Bus', {
            messageId: context.invocationId,
            triggerMetadata: context.triggerMetadata
        });
        const aiMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { operationId, operationType, data } = aiMessage;
        if (!operationId || !operationType) {
            throw new Error('Invalid AI operation message: missing operationId or operationType');
        }
        // Update operation status to processing
        await database_1.db.updateItem('ai-operations', {
            ...data,
            id: operationId,
            status: 'processing',
            processingStartedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Process based on operation type
        let result;
        switch (operationType) {
            case 'DOCUMENT_ANALYSIS':
                result = await processDocumentAnalysis(data);
                break;
            case 'CONTENT_GENERATION':
                result = await processContentGeneration(data);
                break;
            case 'CONTENT_COMPLETION':
                result = await processContentCompletion(data);
                break;
            case 'DOCUMENT_SUMMARIZATION':
                result = await processDocumentSummarization(data);
                break;
            case 'INTELLIGENT_SEARCH':
                result = await processIntelligentSearch(data);
                break;
            case 'WORKFLOW_OPTIMIZATION':
                result = await processWorkflowOptimization(data);
                break;
            case 'BATCH_PROCESSING':
                result = await processBatchOperation(data);
                break;
            default:
                throw new Error(`Unknown AI operation type: ${operationType}`);
        }
        // Update operation status to completed
        await database_1.db.updateItem('ai-operations', {
            ...data,
            id: operationId,
            status: 'completed',
            result,
            processingCompletedAt: new Date().toISOString(),
            processingTime: Date.now() - startTime,
            updatedAt: new Date().toISOString()
        });
        // Publish completion event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.ANALYTICS_GENERATED, `ai-operations/${operationId}`, {
            operationId,
            operationType,
            status: 'completed',
            processingTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
        });
        // Cache result in Redis for quick access
        await redis_1.redis.setJson(`ai-op:${operationId}`, result, 3600); // 1 hour
        logger_1.logger.info('AI operation processed successfully', {
            operationId,
            operationType,
            processingTime: Date.now() - startTime
        });
    }
    catch (error) {
        metrics.errors++;
        logger_1.logger.error('AI operation handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
        // Update operation status to failed
        const aiMessage = typeof message === 'string' ? JSON.parse(message) : message;
        if (aiMessage?.operationId) {
            await database_1.db.updateItem('ai-operations', {
                ...aiMessage.data,
                id: aiMessage.operationId,
                status: 'failed',
                error: error instanceof Error ? error.message : String(error),
                processingFailedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        }
        throw error; // Re-throw to trigger dead letter processing
    }
    finally {
        updateMetrics(Date.now() - startTime);
    }
}
/**
 * Scheduled Emails handler - processes scheduled email delivery from the queue
 */
async function scheduledEmailsHandler(message, context) {
    const startTime = Date.now();
    metrics.messagesReceived++;
    try {
        logger_1.logger.info('Processing scheduled email from Service Bus', {
            messageId: context.invocationId,
            triggerMetadata: context.triggerMetadata
        });
        const emailMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { emailId, type, emailData } = emailMessage;
        if (!emailId || !emailData) {
            throw new Error('Invalid email message: missing emailId or emailData');
        }
        // Update email status to processing
        await database_1.db.updateItem('email-messages', {
            ...emailData,
            id: emailId,
            status: 'processing',
            delivery: {
                ...emailData.delivery,
                processingStartedAt: new Date().toISOString(),
                attempts: emailData.delivery.attempts + 1
            },
            updatedAt: new Date().toISOString()
        });
        // Process email delivery using Postmark
        const postmarkToken = process.env.POSTMARK_SERVER_TOKEN;
        const fromEmail = process.env.POSTMARK_FROM_EMAIL || '<EMAIL>';
        if (!postmarkToken) {
            throw new Error('Postmark server token not configured');
        }
        const { Client } = require('postmark');
        const postmarkClient = new Client(postmarkToken);
        // Prepare email for delivery
        const emailPayload = {
            From: fromEmail,
            To: emailData.to.join(','),
            Cc: emailData.cc?.join(','),
            Bcc: emailData.bcc?.join(','),
            Subject: emailData.subject,
            HtmlBody: emailData.content,
            TextBody: emailData.content.replace(/<[^>]*>/g, ''), // Strip HTML for text version
            Tag: emailData.type || 'general',
            TrackOpens: emailData.options?.trackOpens || true,
            TrackLinks: emailData.options?.trackLinks || 'HtmlAndText',
            MessageStream: 'outbound',
            Attachments: emailData.attachments?.map((att) => ({
                Name: att.filename,
                Content: att.content,
                ContentType: att.contentType
            })) || []
        };
        // Send email via Postmark
        const result = await postmarkClient.sendEmail(emailPayload);
        // Update email status to sent
        await database_1.db.updateItem('email-messages', {
            ...emailData,
            id: emailId,
            status: 'sent',
            delivery: {
                ...emailData.delivery,
                sentAt: new Date().toISOString(),
                messageId: result.MessageID,
                attempts: emailData.delivery.attempts + 1
            },
            updatedAt: new Date().toISOString()
        });
        // Publish email sent event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.NOTIFICATION_SENT, `emails/${emailId}`, {
            emailId,
            messageId: result.MessageID,
            to: emailData.to,
            subject: emailData.subject,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info('Scheduled email processed successfully', {
            emailId,
            messageId: result.MessageID,
            processingTime: Date.now() - startTime
        });
    }
    catch (error) {
        metrics.errors++;
        logger_1.logger.error('Scheduled email handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
        // Update email status to failed
        const emailMessage = typeof message === 'string' ? JSON.parse(message) : message;
        if (emailMessage?.emailId && emailMessage?.emailData) {
            await database_1.db.updateItem('email-messages', {
                ...emailMessage.emailData,
                id: emailMessage.emailId,
                status: 'failed',
                delivery: {
                    ...emailMessage.emailData.delivery,
                    failedAt: new Date().toISOString(),
                    errorMessage: error instanceof Error ? error.message : String(error),
                    attempts: emailMessage.emailData.delivery.attempts + 1
                },
                updatedAt: new Date().toISOString()
            });
        }
        throw error; // Re-throw to trigger dead letter processing
    }
    finally {
        updateMetrics(Date.now() - startTime);
    }
}
/**
 * Document Processing handler - processes document analysis requests from the queue
 */
async function documentProcessingHandler(message, context) {
    const startTime = Date.now();
    metrics.messagesReceived++;
    try {
        logger_1.logger.info('Processing document processing request from Service Bus', {
            messageId: context.invocationId,
            triggerMetadata: context.triggerMetadata
        });
        const docMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { documentId, userId, action, analysisType = 'layout' } = docMessage;
        if (!documentId || !userId || !action) {
            throw new Error('Invalid document processing message: missing required fields');
        }
        // Get document from database
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            throw new Error(`Document not found: ${documentId}`);
        }
        // Update document status to processing
        await database_1.db.updateItem('documents', {
            ...document,
            id: documentId,
            status: 'processing',
            processingStartedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Process document based on action
        let result;
        switch (action) {
            case 'analyze':
                result = await processDocumentAnalysisAction(document, analysisType);
                break;
            case 'extract-text':
                result = await extractTextFromDocumentAction(document);
                break;
            case 'generate-thumbnail':
                result = await generateThumbnailAction(document);
                break;
            case 'classify':
                result = await classifyDocumentAction(document);
                break;
            default:
                throw new Error(`Unknown document processing action: ${action}`);
        }
        // Update document status to processed
        await database_1.db.updateItem('documents', {
            ...document,
            id: documentId,
            status: 'processed',
            processingResult: result,
            processingCompletedAt: new Date().toISOString(),
            processingTime: Date.now() - startTime,
            updatedAt: new Date().toISOString()
        });
        // Publish completion event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.DOCUMENT_PROCESSED, `documents/${documentId}`, {
            documentId,
            action,
            status: 'completed',
            processingTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
        });
        // Cache result in Redis
        await redis_1.redis.setJson(`doc-result:${documentId}`, result, 3600); // 1 hour
        logger_1.logger.info('Document processing completed successfully', {
            documentId,
            action,
            processingTime: Date.now() - startTime
        });
    }
    catch (error) {
        metrics.errors++;
        logger_1.logger.error('Document processing handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
        // Update document status to failed
        const docMessage = typeof message === 'string' ? JSON.parse(message) : message;
        if (docMessage?.documentId) {
            await database_1.db.updateItem('documents', {
                id: docMessage.documentId,
                status: 'failed',
                error: error instanceof Error ? error.message : String(error),
                processingFailedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        }
        throw error; // Re-throw to trigger dead letter processing
    }
    finally {
        updateMetrics(Date.now() - startTime);
    }
}
/**
 * Notification Delivery handler - processes push notification delivery from the queue
 */
async function notificationDeliveryHandler(message, context) {
    const startTime = Date.now();
    metrics.messagesReceived++;
    try {
        logger_1.logger.info('Processing notification delivery from Service Bus', {
            messageId: context.invocationId,
            triggerMetadata: context.triggerMetadata
        });
        const notificationMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { notificationId, platform, title, body, data, userId, deviceToken } = notificationMessage;
        if (!notificationId || !platform || !title || !body) {
            throw new Error('Invalid notification message: missing required fields');
        }
        // Update notification status to processing
        await database_1.db.updateItem('push-notifications', {
            id: notificationId,
            status: 'processing',
            processingStartedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Process notification delivery using Azure Notification Hubs
        const hubName = process.env.NOTIFICATION_HUB_NAME || 'hepztech';
        const connectionString = process.env.NOTIFICATION_HUB_CONNECTION_STRING;
        if (!connectionString) {
            throw new Error('Notification Hub connection string not configured');
        }
        const { NotificationHubsClient, createAppleNotification, createFcmV1Notification } = require('@azure/notification-hubs');
        const client = new NotificationHubsClient(connectionString, hubName);
        // Create platform-specific notification
        let notification;
        switch (platform.toLowerCase()) {
            case 'ios':
            case 'apple':
                notification = createAppleNotification({
                    body: {
                        aps: {
                            alert: { title, body },
                            badge: 1,
                            sound: 'default'
                        },
                        data: data || {}
                    }
                });
                break;
            case 'android':
            case 'fcm':
                notification = createFcmV1Notification({
                    body: {
                        notification: { title, body },
                        data: data || {},
                        android: { priority: 'high' }
                    }
                });
                break;
            default:
                throw new Error(`Unsupported platform: ${platform}`);
        }
        // Send notification
        const result = await client.sendNotification(notification, { tags: [`userId:${userId}`] });
        // Update notification status to sent
        await database_1.db.updateItem('push-notifications', {
            id: notificationId,
            status: 'sent',
            delivery: {
                sentAt: new Date().toISOString(),
                notificationId: result.notificationId,
                trackingId: result.trackingId
            },
            processingCompletedAt: new Date().toISOString(),
            processingTime: Date.now() - startTime,
            updatedAt: new Date().toISOString()
        });
        // Publish notification sent event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.NOTIFICATION_SENT, `notifications/${notificationId}`, {
            notificationId,
            platform,
            userId,
            title,
            hubNotificationId: result.notificationId,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info('Notification delivery completed successfully', {
            notificationId,
            platform,
            userId,
            hubNotificationId: result.notificationId,
            processingTime: Date.now() - startTime
        });
    }
    catch (error) {
        metrics.errors++;
        logger_1.logger.error('Notification delivery handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
        // Update notification status to failed
        const notificationMessage = typeof message === 'string' ? JSON.parse(message) : message;
        if (notificationMessage?.notificationId) {
            await database_1.db.updateItem('push-notifications', {
                id: notificationMessage.notificationId,
                status: 'failed',
                error: error instanceof Error ? error.message : String(error),
                processingFailedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        }
        throw error; // Re-throw to trigger dead letter processing
    }
    finally {
        updateMetrics(Date.now() - startTime);
    }
}
/**
 * Workflow orchestration queue handler
 * Handles workflow step execution messages
 */
async function workflowOrchestrationHandler(message, context) {
    logger_1.logger.info('Workflow orchestration handler triggered', {
        message,
        messageId: context.triggerMetadata?.messageId
    });
    try {
        const workflowMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { workflowId, stepId, action, data = {}, userId, priority = 'normal' } = workflowMessage;
        if (!workflowId || !stepId || !action) {
            throw new Error('Invalid workflow message: missing required fields');
        }
        // Get workflow from database
        const workflows = await database_1.db.queryItems('workflows', 'SELECT * FROM c WHERE c.id = @workflowId', [workflowId]);
        if (workflows.length === 0) {
            throw new Error(`Workflow not found: ${workflowId}`);
        }
        const workflow = workflows[0];
        // Process workflow step
        const stepResult = await processWorkflowStep(workflow, stepId, action, data, userId);
        // Update workflow status
        await database_1.db.updateItem('workflows', {
            ...workflow,
            currentStep: stepResult.nextStep,
            status: stepResult.workflowStatus,
            lastExecutedAt: new Date().toISOString(),
            steps: workflow.steps.map((step) => step.id === stepId
                ? { ...step, status: stepResult.stepStatus, executedAt: new Date().toISOString(), result: stepResult.result }
                : step)
        });
        // Publish workflow events
        if (stepResult.workflowStatus === 'completed') {
            await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.WORKFLOW_COMPLETED, `workflows/${workflowId}`, {
                workflowId,
                completedBy: userId,
                duration: Date.now() - new Date(workflow.createdAt).getTime(),
                timestamp: new Date().toISOString()
            });
        }
        // Queue next step if workflow continues
        if (stepResult.nextStep && stepResult.workflowStatus === 'active') {
            await queueNextWorkflowStep(workflowId, stepResult.nextStep, userId);
        }
        logger_1.logger.info('Workflow step processed successfully', {
            workflowId,
            stepId,
            action,
            nextStep: stepResult.nextStep,
            workflowStatus: stepResult.workflowStatus
        });
    }
    catch (error) {
        logger_1.logger.error('Workflow orchestration handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
        // Handle workflow failure
        if (typeof message === 'object' && message !== null && 'workflowId' in message) {
            const workflowId = message.workflowId;
            try {
                const workflows = await database_1.db.queryItems('workflows', 'SELECT * FROM c WHERE c.id = @workflowId', [workflowId]);
                if (workflows.length > 0) {
                    const workflow = workflows[0];
                    await database_1.db.updateItem('workflows', {
                        ...workflow,
                        status: 'failed',
                        error: error instanceof Error ? error.message : String(error),
                        failedAt: new Date().toISOString()
                    });
                }
            }
            catch (updateError) {
                logger_1.logger.error('Failed to update workflow status after failure', { updateError });
            }
        }
    }
}
/**
 * Document collaboration handler
 * Handles real-time document collaboration messages
 */
async function documentCollaborationHandler(message, context) {
    logger_1.logger.info('Document collaboration handler triggered', {
        message,
        messageId: context.triggerMetadata?.messageId
    });
    try {
        const collaborationMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { documentId, userId, action, data = {}, sessionId, timestamp } = collaborationMessage;
        if (!documentId || !userId || !action) {
            throw new Error('Invalid collaboration message: missing required fields');
        }
        // Process collaboration action
        let result;
        switch (action) {
            case 'join':
                result = await handleUserJoinDocument(documentId, userId, sessionId);
                break;
            case 'leave':
                result = await handleUserLeaveDocument(documentId, userId, sessionId);
                break;
            case 'edit':
                result = await handleDocumentEdit(documentId, userId, data);
                break;
            case 'comment':
                result = await handleDocumentComment(documentId, userId, data);
                break;
            case 'share':
                result = await handleDocumentShare(documentId, userId, data);
                break;
            default:
                throw new Error(`Unknown collaboration action: ${action}`);
        }
        // Log collaboration activity
        await database_1.db.createItem('collaboration-logs', {
            id: `collab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            documentId,
            userId,
            action,
            data,
            sessionId,
            result,
            timestamp: new Date().toISOString()
        });
        // Broadcast to other collaborators via SignalR
        await broadcastCollaborationUpdate(documentId, {
            action,
            userId,
            data,
            result,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info('Document collaboration processed successfully', {
            documentId,
            userId,
            action,
            sessionId
        });
    }
    catch (error) {
        logger_1.logger.error('Document collaboration handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Analytics aggregation handler
 * Handles analytics data aggregation messages
 */
async function analyticsAggregationHandler(message, context) {
    logger_1.logger.info('Analytics aggregation handler triggered', {
        message,
        messageId: context.triggerMetadata?.messageId
    });
    try {
        const analyticsMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { eventType, entityId, entityType, metrics = {}, timestamp, userId } = analyticsMessage;
        if (!eventType || !entityId || !entityType) {
            throw new Error('Invalid analytics message: missing required fields');
        }
        // Aggregate metrics based on event type
        const aggregationResult = await aggregateMetrics(eventType, entityId, entityType, metrics, userId);
        // Update analytics data
        await updateAnalyticsData(entityType, entityId, aggregationResult);
        // Check for analytics thresholds and alerts
        await checkAnalyticsThresholds(entityType, entityId, aggregationResult);
        logger_1.logger.info('Analytics aggregation processed successfully', {
            eventType,
            entityId,
            entityType,
            metricsCount: Object.keys(metrics).length
        });
    }
    catch (error) {
        logger_1.logger.error('Analytics aggregation handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * System monitoring handler
 * Handles system monitoring and alerting messages
 */
async function systemMonitoringHandler(message, context) {
    logger_1.logger.info('System monitoring handler triggered', {
        message,
        messageId: context.triggerMetadata?.messageId
    });
    try {
        const monitoringMessage = typeof message === 'string' ? JSON.parse(message) : message;
        const { source, metricType, value, threshold = {}, severity = 'info', timestamp } = monitoringMessage;
        if (!source || !metricType || value === undefined) {
            throw new Error('Invalid monitoring message: missing required fields');
        }
        // Store monitoring data
        await database_1.db.createItem('monitoring-data', {
            id: `monitor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            source,
            metricType,
            value,
            severity,
            timestamp: timestamp || new Date().toISOString()
        });
        // Check thresholds and generate alerts
        if (threshold.warning && value > threshold.warning) {
            await generateMonitoringAlert('warning', source, metricType, value, threshold.warning);
        }
        if (threshold.critical && value > threshold.critical) {
            await generateMonitoringAlert('critical', source, metricType, value, threshold.critical);
        }
        logger_1.logger.info('System monitoring processed successfully', {
            source,
            metricType,
            value,
            severity
        });
    }
    catch (error) {
        logger_1.logger.error('System monitoring handler failed', {
            message,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
// Helper functions (placeholder implementations)
async function processWorkflowStep(workflow, stepId, action, data, userId) {
    // Implement workflow step processing logic
    return {
        stepStatus: 'completed',
        workflowStatus: 'active',
        nextStep: 'step-2',
        result: { processed: true }
    };
}
async function queueNextWorkflowStep(workflowId, stepId, userId) {
    // Queue next workflow step using shared service
    await service_bus_1.serviceBusEnhanced.initialize();
    const success = await service_bus_1.serviceBusEnhanced.sendToQueue('workflow-orchestration', {
        body: {
            workflowId,
            stepId,
            action: 'execute',
            userId,
            timestamp: new Date().toISOString()
        },
        messageId: `workflow-${workflowId}-${stepId}-${Date.now()}`,
        correlationId: workflowId,
        applicationProperties: {
            workflowId,
            stepId,
            userId,
            source: 'service-bus-handlers'
        }
    });
    if (!success) {
        throw new Error('Failed to queue workflow step');
    }
}
async function handleUserJoinDocument(documentId, userId, sessionId) {
    return { action: 'joined', activeUsers: 2 };
}
async function handleUserLeaveDocument(documentId, userId, sessionId) {
    return { action: 'left', activeUsers: 1 };
}
async function handleDocumentEdit(documentId, userId, data) {
    return { action: 'edited', changes: data.changes };
}
async function handleDocumentComment(documentId, userId, data) {
    return { action: 'commented', commentId: 'comment-123' };
}
async function handleDocumentShare(documentId, userId, data) {
    return { action: 'shared', shareId: 'share-123' };
}
async function broadcastCollaborationUpdate(documentId, update) {
    // Implement SignalR broadcast
}
async function aggregateMetrics(eventType, entityId, entityType, metrics, userId) {
    return { aggregated: true, count: 1 };
}
async function updateAnalyticsData(entityType, entityId, data) {
    // Update analytics data in database
}
async function checkAnalyticsThresholds(entityType, entityId, data) {
    // Check for threshold breaches
}
async function generateMonitoringAlert(severity, source, metricType, value, threshold) {
    await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PERFORMANCE_ALERT, `monitoring/${source}`, {
        alertType: 'threshold_breach',
        severity,
        source,
        metricType,
        value,
        threshold,
        timestamp: new Date().toISOString()
    });
}
// AI Operation Processing Functions
async function processDocumentAnalysis(data) {
    // Implement AI document analysis logic
    return {
        type: 'document_analysis',
        confidence: 0.95,
        entities: [],
        extractedText: 'Sample extracted text',
        processingTime: 2500
    };
}
async function processContentGeneration(data) {
    // Implement AI content generation logic
    return {
        type: 'content_generation',
        generatedContent: 'Sample generated content',
        confidence: 0.92,
        processingTime: 3000
    };
}
async function processContentCompletion(data) {
    // Implement AI content completion logic
    return {
        type: 'content_completion',
        completedContent: 'Sample completed content',
        confidence: 0.88,
        processingTime: 1500
    };
}
async function processDocumentSummarization(data) {
    // Implement AI document summarization logic
    return {
        type: 'document_summarization',
        summary: 'Sample document summary',
        keyPoints: ['Point 1', 'Point 2', 'Point 3'],
        confidence: 0.90,
        processingTime: 2000
    };
}
async function processIntelligentSearch(data) {
    // Implement AI intelligent search logic
    return {
        type: 'intelligent_search',
        results: [],
        relevanceScore: 0.85,
        processingTime: 800
    };
}
async function processWorkflowOptimization(data) {
    // Implement AI workflow optimization logic
    return {
        type: 'workflow_optimization',
        optimizations: [],
        efficiency_gain: 0.15,
        processingTime: 4000
    };
}
async function processBatchOperation(data) {
    // Implement AI batch processing logic
    return {
        type: 'batch_processing',
        processedItems: 0,
        totalItems: 0,
        processingTime: 5000
    };
}
// Document Processing Action Functions
async function processDocumentAnalysisAction(document, analysisType) {
    const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');
    const { ragService } = require('../shared/services/rag-service');
    try {
        // Get document buffer from blob storage
        const { BlobServiceClient } = require('@azure/storage-blob');
        const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        const blobClient = containerClient.getBlobClient(document.blobName);
        const downloadResponse = await blobClient.download();
        if (!downloadResponse.readableStreamBody) {
            throw new Error('Failed to download document content');
        }
        // Convert stream to buffer
        const chunks = [];
        for await (const chunk of downloadResponse.readableStreamBody) {
            chunks.push(Buffer.from(chunk));
        }
        const documentBuffer = Buffer.concat(chunks);
        // Perform enhanced document analysis
        const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(documentBuffer, document.id, analysisType === 'invoice' ? 'prebuilt-invoice' : 'prebuilt-layout');
        // Index for RAG if substantial content
        if (analysisResult.extractedText && analysisResult.extractedText.length > 500) {
            await ragService.indexDocument({
                documentId: document.id,
                content: analysisResult.extractedText,
                metadata: {
                    analysisType,
                    modelUsed: analysisResult.modelUsed,
                    confidence: analysisResult.confidence
                }
            });
        }
        return {
            type: 'document_analysis',
            analysisType,
            extractedText: analysisResult.extractedText,
            tablesCount: analysisResult.tables.length,
            keyValuePairsCount: analysisResult.keyValuePairs.length,
            entitiesCount: analysisResult.entities.length,
            confidence: analysisResult.confidence,
            processingTime: analysisResult.processingTime
        };
    }
    catch (error) {
        logger_1.logger.error('Document analysis action failed', { error, documentId: document.id });
        throw error;
    }
}
async function extractTextFromDocumentAction(document) {
    const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');
    try {
        // Get document buffer from blob storage
        const { BlobServiceClient } = require('@azure/storage-blob');
        const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        const blobClient = containerClient.getBlobClient(document.blobName);
        const downloadResponse = await blobClient.download();
        if (!downloadResponse.readableStreamBody) {
            throw new Error('Failed to download document content');
        }
        // Convert stream to buffer
        const chunks = [];
        for await (const chunk of downloadResponse.readableStreamBody) {
            chunks.push(Buffer.from(chunk));
        }
        const documentBuffer = Buffer.concat(chunks);
        // Extract text using enhanced document intelligence
        const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(documentBuffer, document.id, 'prebuilt-read');
        return {
            type: 'text_extraction',
            extractedText: analysisResult.extractedText,
            wordCount: analysisResult.extractedText.split(/\s+/).length,
            confidence: analysisResult.confidence,
            processingTime: analysisResult.processingTime
        };
    }
    catch (error) {
        logger_1.logger.error('Text extraction action failed', { error, documentId: document.id });
        throw error;
    }
}
async function generateThumbnailAction(document) {
    const { BlobServiceClient } = require('@azure/storage-blob');
    try {
        logger_1.logger.info('Starting thumbnail generation', { documentId: document.id });
        // Get document blob from storage
        const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING || '');
        const containerClient = blobServiceClient.getContainerClient('documents');
        const blobClient = containerClient.getBlobClient(document.blobName || `${document.id}.pdf`);
        // Check if document exists
        const exists = await blobClient.exists();
        if (!exists) {
            throw new Error(`Document blob not found: ${document.blobName}`);
        }
        // Download document content
        const downloadResponse = await blobClient.download();
        const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
        // Generate thumbnail based on document type
        let thumbnailBuffer;
        let dimensions = { width: 200, height: 300 };
        const fileExtension = (document.fileName || document.blobName || '').toLowerCase();
        if (fileExtension.includes('.pdf')) {
            thumbnailBuffer = await generatePDFThumbnail(documentBuffer);
        }
        else if (fileExtension.includes('.jpg') || fileExtension.includes('.jpeg') ||
            fileExtension.includes('.png') || fileExtension.includes('.gif')) {
            thumbnailBuffer = await generateImageThumbnail(documentBuffer);
        }
        else if (fileExtension.includes('.doc') || fileExtension.includes('.docx')) {
            thumbnailBuffer = await generateDocumentThumbnail(documentBuffer, 'docx');
        }
        else {
            // Generate a generic document thumbnail
            thumbnailBuffer = await generateGenericThumbnail(document);
        }
        // Upload thumbnail to storage
        const thumbnailBlobName = `thumbnails/${document.id}/thumb.jpg`;
        const thumbnailBlobClient = containerClient.getBlobClient(thumbnailBlobName);
        await thumbnailBlobClient.upload(thumbnailBuffer, thumbnailBuffer.length, {
            blobHTTPHeaders: {
                blobContentType: 'image/jpeg'
            },
            metadata: {
                documentId: document.id,
                generatedAt: new Date().toISOString(),
                originalFileName: document.fileName || 'unknown'
            }
        });
        // Get thumbnail URL
        const thumbnailUrl = thumbnailBlobClient.url;
        // Update document with thumbnail information
        await database_1.db.updateItem('documents', {
            ...document,
            thumbnailUrl,
            thumbnailBlobName,
            thumbnailGenerated: true,
            thumbnailGeneratedAt: new Date().toISOString()
        });
        logger_1.logger.info('Thumbnail generated successfully', {
            documentId: document.id,
            thumbnailUrl,
            dimensions
        });
        return {
            type: 'thumbnail_generation',
            thumbnailUrl,
            thumbnailBlobName,
            dimensions,
            success: true,
            generatedAt: new Date().toISOString()
        };
    }
    catch (error) {
        logger_1.logger.error('Thumbnail generation failed', {
            error: error instanceof Error ? error.message : String(error),
            documentId: document.id
        });
        // Return error response but don't throw to avoid breaking the workflow
        return {
            type: 'thumbnail_generation',
            success: false,
            error: error instanceof Error ? error.message : String(error),
            fallbackThumbnail: await generateFallbackThumbnail(document)
        };
    }
}
async function streamToBuffer(readableStream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        readableStream.on('data', (data) => {
            chunks.push(data);
        });
        readableStream.on('end', () => {
            resolve(Buffer.concat(chunks));
        });
        readableStream.on('error', reject);
    });
}
async function generatePDFThumbnail(pdfBuffer) {
    // This would use a PDF processing library like pdf-poppler or pdf2pic
    // For now, return a placeholder that indicates PDF processing
    const sharp = require('sharp');
    // Create a simple placeholder image for PDF
    const placeholderSvg = `
    <svg width="200" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="300" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
      <text x="100" y="150" text-anchor="middle" font-family="Arial" font-size="16" fill="#666">PDF</text>
      <text x="100" y="170" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">Document</text>
    </svg>
  `;
    return await sharp(Buffer.from(placeholderSvg))
        .jpeg({ quality: 80 })
        .toBuffer();
}
async function generateImageThumbnail(imageBuffer) {
    const sharp = require('sharp');
    return await sharp(imageBuffer)
        .resize(200, 300, {
        fit: 'inside',
        withoutEnlargement: true
    })
        .jpeg({ quality: 80 })
        .toBuffer();
}
async function generateDocumentThumbnail(docBuffer, type) {
    // This would use a document processing library
    // For now, return a placeholder
    const sharp = require('sharp');
    const placeholderSvg = `
    <svg width="200" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="300" fill="#e8f4fd" stroke="#4a90e2" stroke-width="2"/>
      <text x="100" y="150" text-anchor="middle" font-family="Arial" font-size="16" fill="#4a90e2">${type.toUpperCase()}</text>
      <text x="100" y="170" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Document</text>
    </svg>
  `;
    return await sharp(Buffer.from(placeholderSvg))
        .jpeg({ quality: 80 })
        .toBuffer();
}
async function generateGenericThumbnail(document) {
    const sharp = require('sharp');
    const fileType = (document.fileName || '').split('.').pop()?.toUpperCase() || 'FILE';
    const placeholderSvg = `
    <svg width="200" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
      <text x="100" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">${fileType}</text>
      <text x="100" y="160" text-anchor="middle" font-family="Arial" font-size="12" fill="#adb5bd">Document</text>
      <text x="100" y="180" text-anchor="middle" font-family="Arial" font-size="10" fill="#ced4da">Preview not available</text>
    </svg>
  `;
    return await sharp(Buffer.from(placeholderSvg))
        .jpeg({ quality: 80 })
        .toBuffer();
}
async function generateFallbackThumbnail(document) {
    // Return a data URL for a simple fallback thumbnail
    const fileType = (document.fileName || '').split('.').pop()?.toUpperCase() || 'FILE';
    return `data:image/svg+xml;base64,${Buffer.from(`
    <svg width="200" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="300" fill="#ffeaa7" stroke="#fdcb6e" stroke-width="2"/>
      <text x="100" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="#e17055">${fileType}</text>
      <text x="100" y="160" text-anchor="middle" font-family="Arial" font-size="12" fill="#d63031">Thumbnail Error</text>
      <text x="100" y="180" text-anchor="middle" font-family="Arial" font-size="10" fill="#a29bfe">Fallback image</text>
    </svg>
  `).toString('base64')}`;
}
async function classifyDocumentAction(document) {
    const { aiServices } = require('../shared/services/ai-services');
    try {
        // Get document text for classification
        let documentText = document.extractedText || '';
        if (!documentText) {
            // Extract text first if not available
            const textResult = await extractTextFromDocumentAction(document);
            documentText = textResult.extractedText;
        }
        if (!documentText || documentText.length < 50) {
            return {
                type: 'document_classification',
                category: 'unknown',
                subcategory: 'insufficient_content',
                confidence: 0.1,
                tags: ['unknown']
            };
        }
        // Use AI services for classification
        const classificationPrompt = `Classify this document and determine its category, subcategory, and relevant tags.

Document Content:
${documentText.substring(0, 2000)}...

Provide a JSON response with:
- category: string (main document category)
- subcategory: string (specific document type)
- confidence: number (0-1)
- tags: string[] (relevant tags)`;
        const classificationResult = await aiServices.reason(classificationPrompt, [], {
            systemPrompt: 'You are a document classification expert. Analyze documents and provide structured classification results.',
            temperature: 0.2,
            maxTokens: 500
        });
        try {
            const parsed = JSON.parse(classificationResult.content);
            return {
                type: 'document_classification',
                category: parsed.category || 'general',
                subcategory: parsed.subcategory || 'document',
                confidence: parsed.confidence || classificationResult.confidence,
                tags: parsed.tags || ['document'],
                processingTime: classificationResult.processingTime
            };
        }
        catch {
            return {
                type: 'document_classification',
                category: 'general',
                subcategory: 'document',
                confidence: classificationResult.confidence,
                tags: ['document'],
                reasoning: classificationResult.reasoning
            };
        }
    }
    catch (error) {
        logger_1.logger.error('Document classification action failed', { error, documentId: document.id });
        throw error;
    }
}
// Metrics and utility functions
function updateMetrics(processingTime) {
    metrics.averageProcessingTime =
        (metrics.averageProcessingTime * 0.9) + (processingTime * 0.1);
}
// Register Service Bus triggers
functions_1.app.serviceBusQueue('workflowOrchestration', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    queueName: 'workflow-orchestration',
    handler: workflowOrchestrationHandler
});
functions_1.app.serviceBusTopic('documentCollaboration', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    topicName: 'document-collaboration',
    subscriptionName: 'collaboration-processor',
    handler: documentCollaborationHandler
});
functions_1.app.serviceBusTopic('analyticsAggregation', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    topicName: 'analytics-events',
    subscriptionName: 'analytics-aggregator',
    handler: analyticsAggregationHandler
});
functions_1.app.serviceBusTopic('systemMonitoring', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    topicName: 'monitoring-events',
    subscriptionName: 'system-monitor',
    handler: systemMonitoringHandler
});
// Register missing Service Bus queue triggers
functions_1.app.serviceBusQueue('aiOperations', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    queueName: 'ai-operations',
    handler: aiOperationsHandler
});
functions_1.app.serviceBusQueue('scheduledEmails', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    queueName: 'scheduled-emails',
    handler: scheduledEmailsHandler
});
functions_1.app.serviceBusQueue('documentProcessing', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    queueName: 'document-processing',
    handler: documentProcessingHandler
});
functions_1.app.serviceBusQueue('notificationDelivery', {
    connection: 'SERVICE_BUS_CONNECTION_STRING',
    queueName: 'notification-delivery',
    handler: notificationDeliveryHandler
});
/**
 * Enhanced Service Bus utilities
 */
/**
 * Send message with enhanced features using shared serviceBusEnhanced service
 */
async function sendEnhancedMessage(destination, message, options = {}) {
    const startTime = Date.now();
    try {
        // Initialize shared service
        await service_bus_1.serviceBusEnhanced.initialize();
        // Check circuit breaker
        if (isCircuitBreakerOpen(destination)) {
            logger_1.logger.warn('Circuit breaker is open for destination', { destination });
            return false;
        }
        // Check for duplicate message
        if (options.messageId && await isDuplicateMessage(options.messageId)) {
            logger_1.logger.info('Duplicate message detected, skipping', { messageId: options.messageId });
            return true;
        }
        const serviceBusMessage = {
            body: message,
            messageId: options.messageId || generateMessageId(),
            sessionId: options.sessionId,
            timeToLive: options.timeToLive,
            scheduledEnqueueTime: options.scheduledEnqueueTime,
            correlationId: options.correlationId,
            applicationProperties: {
                originalTimestamp: new Date().toISOString(),
                source: 'service-bus-handlers'
            }
        };
        // Use shared service to send message
        const success = options.isQueue !== false
            ? await service_bus_1.serviceBusEnhanced.sendToQueue(destination, serviceBusMessage)
            : await service_bus_1.serviceBusEnhanced.sendToTopic(destination, serviceBusMessage);
        if (!success) {
            throw new Error('Failed to send message via shared service');
        }
        // Mark message as sent to prevent duplicates
        if (options.messageId) {
            await markMessageAsSent(options.messageId);
        }
        metrics.messagesSent++;
        updateProcessingTime(Date.now() - startTime);
        resetCircuitBreaker(destination);
        logger_1.logger.info('Enhanced message sent successfully via shared service', {
            destination,
            messageId: serviceBusMessage.messageId,
            isQueue: options.isQueue !== false
        });
        await publishMetricsEvent('message_sent', {
            destination,
            messageId: serviceBusMessage.messageId
        });
        return true;
    }
    catch (error) {
        metrics.errors++;
        recordCircuitBreakerFailure(destination);
        logger_1.logger.error('Error sending enhanced message via shared service', {
            destination,
            error: error instanceof Error ? error.message : String(error)
        });
        return false;
    }
}
/**
 * Send batch of messages using shared serviceBusEnhanced service
 */
async function sendBatchMessages(destination, messages, isQueue = true) {
    try {
        // Initialize shared service
        await service_bus_1.serviceBusEnhanced.initialize();
        const serviceBusMessages = messages.map(msg => ({
            body: msg.body || msg,
            messageId: msg.messageId || generateMessageId(),
            sessionId: msg.sessionId,
            timeToLive: msg.timeToLive,
            correlationId: msg.correlationId,
            applicationProperties: {
                ...msg.applicationProperties,
                batchId: generateMessageId(),
                originalTimestamp: new Date().toISOString(),
                source: 'service-bus-handlers'
            }
        }));
        // Use shared service to send batch (send messages individually since batch method doesn't exist)
        let successCount = 0;
        for (const message of serviceBusMessages) {
            const success = isQueue
                ? await service_bus_1.serviceBusEnhanced.sendToQueue(destination, message)
                : await service_bus_1.serviceBusEnhanced.sendToTopic(destination, message);
            if (success)
                successCount++;
        }
        const allSuccessful = successCount === serviceBusMessages.length;
        if (!allSuccessful) {
            throw new Error(`Failed to send ${serviceBusMessages.length - successCount} out of ${serviceBusMessages.length} batch messages`);
        }
        metrics.messagesSent += messages.length;
        logger_1.logger.info('Batch messages sent successfully via shared service', {
            destination,
            messageCount: messages.length,
            isQueue
        });
        await publishMetricsEvent('batch_sent', {
            destination,
            messageCount: messages.length,
            isQueue
        });
        return true;
    }
    catch (error) {
        metrics.errors++;
        logger_1.logger.error('Error sending batch messages via shared service', {
            destination,
            messageCount: messages.length,
            error: error instanceof Error ? error.message : String(error)
        });
        return false;
    }
}
/**
 * Process dead letter messages using shared service
 * Note: This is a simplified implementation - the shared service handles dead letter processing internally
 */
async function processDeadLetterQueue(queueOrTopicName, subscriptionName) {
    try {
        // Initialize shared service
        await service_bus_1.serviceBusEnhanced.initialize();
        logger_1.logger.info('Dead letter queue processing initiated', {
            queueOrTopicName,
            subscriptionName
        });
        // The shared serviceBusEnhanced service handles dead letter processing internally
        // This function is kept for compatibility but delegates to the shared service
        // Log that dead letter processing is handled by the shared service
        logger_1.logger.info('Dead letter processing delegated to shared serviceBusEnhanced service', {
            queueOrTopicName,
            subscriptionName,
            note: 'Dead letter handling is built into the shared service'
        });
        metrics.messagesDeadLettered++;
    }
    catch (error) {
        logger_1.logger.error('Error processing dead letter queue via shared service', {
            queueOrTopicName,
            subscriptionName,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Get service metrics
 */
function getServiceBusMetrics() {
    return { ...metrics };
}
/**
 * Helper functions for enhanced Service Bus features
 * Note: Legacy sender management removed - now using shared serviceBusEnhanced service
 */
async function isDuplicateMessage(messageId) {
    const key = `servicebus:sent:${messageId}`;
    return await redis_1.redis.exists(key);
}
async function markMessageAsSent(messageId) {
    const key = `servicebus:sent:${messageId}`;
    await redis_1.redis.setex(key, 3600, 'sent'); // Keep for 1 hour
}
function generateMessageId() {
    return `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}
function isCircuitBreakerOpen(destination) {
    const breaker = circuitBreakers.get(destination);
    if (!breaker) {
        return false;
    }
    if (breaker.isOpen) {
        const now = new Date();
        if (now.getTime() - breaker.lastFailureTime.getTime() > breaker.timeout) {
            // Reset circuit breaker after timeout
            breaker.isOpen = false;
            breaker.failureCount = 0;
            logger_1.logger.info('Circuit breaker reset', { destination });
        }
    }
    return breaker.isOpen;
}
function recordCircuitBreakerFailure(destination) {
    let breaker = circuitBreakers.get(destination);
    if (!breaker) {
        breaker = {
            isOpen: false,
            failureCount: 0,
            lastFailureTime: new Date(),
            threshold: 5,
            timeout: 60000 // 1 minute
        };
        circuitBreakers.set(destination, breaker);
    }
    breaker.failureCount++;
    breaker.lastFailureTime = new Date();
    if (breaker.failureCount >= breaker.threshold) {
        breaker.isOpen = true;
        logger_1.logger.warn('Circuit breaker opened', {
            destination,
            failureCount: breaker.failureCount
        });
    }
}
function resetCircuitBreaker(destination) {
    const breaker = circuitBreakers.get(destination);
    if (breaker) {
        breaker.failureCount = 0;
        breaker.isOpen = false;
    }
}
function updateProcessingTime(processingTime) {
    metrics.averageProcessingTime =
        (metrics.averageProcessingTime * 0.9) + (processingTime * 0.1);
}
async function storeDeadLetterMessage(message) {
    try {
        await database_1.db.createItem('dead-letter-messages', {
            id: `dl-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            messageId: message.messageId,
            body: message.body,
            deadLetterReason: message.deadLetterReason,
            deadLetterErrorDescription: message.deadLetterErrorDescription,
            deliveryCount: message.deliveryCount,
            enqueuedTimeUtc: message.enqueuedTimeUtc,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.debug('Failed to store dead letter message', {
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
async function publishMetricsEvent(eventType, data) {
    try {
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PERFORMANCE_ALERT, 'servicebus/metrics', {
            service: 'servicebus',
            eventType,
            metrics,
            circuitBreakers: Array.from(circuitBreakers.entries()).map(([dest, state]) => ({
                destination: dest,
                isOpen: state.isOpen,
                failureCount: state.failureCount
            })),
            timestamp: new Date().toISOString(),
            ...data
        });
    }
    catch (error) {
        // Don't log errors for metrics publishing to avoid recursion
    }
}
// Initialize periodic tasks
setInterval(async () => {
    // Process dead letter queues every 10 minutes
    const knownQueues = [
        'workflow-orchestration',
        'ai-operations',
        'scheduled-emails',
        'document-processing',
        'notification-delivery'
    ];
    const knownTopics = [
        { topic: 'analytics-events', subscription: 'analytics-aggregator' },
        { topic: 'document-collaboration', subscription: 'collaboration-processor' },
        { topic: 'monitoring-events', subscription: 'system-monitor' }
    ];
    // Process queue dead letter queues
    for (const queue of knownQueues) {
        await processDeadLetterQueue(queue);
    }
    // Process topic dead letter queues
    for (const { topic, subscription } of knownTopics) {
        await processDeadLetterQueue(topic, subscription);
    }
}, 10 * 60 * 1000);
// Publish metrics every minute
setInterval(async () => {
    await publishMetricsEvent('periodic_metrics');
}, 60 * 1000);
// Service Bus utilities now provided by shared serviceBusEnhanced service
// Legacy exports removed - use serviceBusEnhanced instead
//# sourceMappingURL=service-bus-handlers.js.map